<template>
  <div class="transfer">
    <!-- 头部 -->
    <div class="header">
      <div class="back-icon" @click="goBack">
        <img src="@/assets/icons/back-icon.svg" alt="返回">
      </div>
      <div class="title">转账</div>
      <div class="right-icon" @click="goToTransferRecord">
        <span>记录</span>
      </div>
    </div> <!-- 账户余额卡片 -->
    <div class="balance-card">
      <div class="balance-card-inner">
        <div class="wallet-icon">
          <img src="@/assets/images/recharge.png" alt="钱包">
        </div>
        <div class="balance-info">
          <div class="balance-label">可转账余额</div>
          <div class="balance-amount">{{ withdrawCan }}元</div>
        </div>
      </div>
    </div>

    <!-- 转账表单 -->
    <div class="transfer-form">
      <!-- 收款人姓名 -->
      <div class="form-item">
        <div class="item-label">收款人姓名</div>
        <div class="item-input">
          <input type="text" v-model="kycName" placeholder="请输入收款人真实姓名">
        </div>
      </div>

      <!-- 收款人手机号 -->
      <div class="form-item">
        <div class="item-label">收款人手机号</div>
        <div class="item-input">
          <input type="text" v-model="mobile" placeholder="请输入收款人手机号">
        </div>
      </div>

      <!-- 转账金额 -->
      <div class="form-item">
        <div class="item-label">转账金额</div>
        <div class="amount-input">
          <span class="currency">￥</span>
          <input type="number" v-model="amount" placeholder="请输入转账金额">
        </div>
        <div class="amount-tips">
          <span>可转账余额：{{ withdrawCan }}元</span>
          <span class="all-btn" @click="transferAll">全部转出</span>
        </div>
      </div>

      <!-- 资金密码 -->
      <div class="form-item" v-if="hasPassword">
        <div class="item-label">资金密码</div>
        <div class="item-input">
          <input type="password" v-model="password" placeholder="请输入资金密码">
        </div>
      </div>
    </div>

    <!-- 转账说明 -->
    <div class="transfer-tips" v-if="transferTips.length > 0">
      <div class="tips-title">转账说明</div>
      <div class="tips-content">
        <p v-for="(tip, index) in transferTips" :key="index">{{ tip }}</p>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="bottom-btn">
      <van-button :disabled="!canSubmit" @click="submitTransfer" :loading="submitting" type="primary" round block
        color="#0474FC ">
        {{ submitting ? '转账中...' : '确认转账' }}
      </van-button>
    </div>

    <!-- 页面加载状态 -->
    <div class="loading" v-if="loading">
      <van-loading size="24px" color="#0474FC ">加载中...</van-loading>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Transfer',
  data() {
    return {
      // 表单数据
      kycName: '',     // 收款人姓名
      mobile: '',      // 收款人手机号
      amount: '',      // 转账金额
      password: '',    // 资金密码

      // API数据
      withdrawCan: '0.00',      // 可转账余额
      hasPassword: false,       // 是否需要密码
      transferTips: [],         // 转账说明

      // 状态
      submitting: false,        // 提交状态
      loading: false           // 页面加载状态
    }
  },
  computed: {
    // 是否可以提交
    canSubmit() {
      if (!this.kycName.trim() || !this.mobile.trim() || !this.amount) {
        return false
      }
      if (this.hasPassword && !this.password.trim()) {
        return false
      }
      return !this.submitting
    }
  },
  async created() {
    await this.fetchTransferInfo()
  },
  methods: {
    // 返回上一页
    goBack() {
      this.$router.go(-1)
    },

    // 跳转到转账记录
    goToTransferRecord() {
      this.$router.push('/transfer-detail')
    },

    // 全部转出
    transferAll() {
      this.amount = parseFloat(this.withdrawCan).toString()
    },

    // 获取转账信息
    async fetchTransferInfo() {
      const userToken = localStorage.getItem('user_token')
      if (!userToken) {
        this.$toast('请先登录')
        // 使用 $nextTick 确保 toast 显示后再跳转
        this.$nextTick(() => {
          this.$router.replace('/login')
        })
        return
      }

      try {
        this.loading = true

        const response = await this.$http.get('/finance/getTransfer', {
          headers: {
            'authorization': userToken
          }
        })

        if (response.data.code === 200) {
          const data = response.data.data
          this.withdrawCan = data.info.withdraw_can || '0.00'
          this.hasPassword = data.info.password === true
          this.transferTips = data.des || []
        } else {
          this.$toast(response.data.msg || '获取转账信息失败')
        }
      } catch (error) {
        console.error('获取转账信息失败:', error)
        this.$toast('获取转账信息失败，请稍后重试')
      } finally {
        this.loading = false
      }
    },

    // 表单验证
    validateForm() {
      if (!this.kycName.trim()) {
        this.$toast('请输入收款人姓名')
        return false
      }

      if (!this.mobile.trim()) {
        this.$toast('请输入收款人手机号')
        return false
      }

      // 验证手机号格式
      const mobileReg = /^1[3-9]\d{9}$/
      if (!mobileReg.test(this.mobile)) {
        this.$toast('请输入正确的手机号')
        return false
      }

      if (!this.amount || parseFloat(this.amount) <= 0) {
        this.$toast('请输入有效的转账金额')
        return false
      }

      // 检查余额
      const withdrawCanFloat = parseFloat(this.withdrawCan)
      const amountFloat = parseFloat(this.amount)
      if (amountFloat > withdrawCanFloat) {
        this.$toast('转账金额不能超过可转账余额')
        return false
      }

      if (this.hasPassword && !this.password.trim()) {
        this.$toast('请输入资金密码')
        return false
      }

      return true
    },

    // 提交转账
    async submitTransfer() {
      if (!this.validateForm()) {
        return
      }

      const userToken = localStorage.getItem('user_token')
      if (!userToken) {
        this.$toast('请先登录')
        this.$nextTick(() => {
          this.$router.replace('/login')
        })
        return
      }

      try {
        // 确认对话框
        await this.$dialog.confirm({
          title: '转账确认',
          message: `收款人：${this.kycName}\n手机号：${this.mobile}\n转账金额：${this.amount}元`,
          confirmButtonText: '确认转账',
          cancelButtonText: '取消'
        })

        this.submitting = true

        // 准备请求数据
        const requestData = {
          kyc_name: this.kycName,
          mobile: this.mobile,
          amount: this.amount
        }

        // 如果需要密码则添加密码
        if (this.hasPassword) {
          requestData.password = this.password
        }

        const response = await this.$http.post('/finance/doTransfer', requestData, {
          headers: {
            'authorization': userToken,
            'Content-Type': 'application/json'
          }
        })

        if (response.data.code === 200) {
          this.$toast.success('转账成功')

          // 延迟跳转，让用户看到成功提示
          setTimeout(() => {
            this.$router.go(-1)
          }, 1500)
        } else {
          this.$toast(response.data.msg || '转账失败')
        }
      } catch (error) {
        if (error === 'cancel') {
          // 用户取消操作
          return
        }

        console.error('转账失败:', error)
        this.$toast('转账失败，请稍后重试')
      } finally {
        this.submitting = false
      }
    }
  }
}
</script>

<style scoped>
.transfer {
  min-height: 100vh;
  background-color: #f6f6f6;
  padding-bottom: 80px;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 16px;
  background-color: #fff;
}

.back-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.back-icon img {
  width: 20px;
  height: 20px;
}

.title {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

.right-icon {
  font-size: 16px;
  color: #666;
  cursor: pointer;
}

/* 账户余额卡片 */
.balance-card {
  margin: 15px;
  height: 123px;
  position: relative;
  border-radius: 10px;
}

.balance-card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 10px;
  background: linear-gradient(180deg, #0474FC 0%, #2585EB 1%, #1F68D6 43%, #1869F5 79%, #0940A0 100%);
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-left: 20px;
  overflow: hidden;
}

.wallet-icon {
  width: 70px;
  height: 70px;
  margin-right: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.wallet-icon img {
  width: 100%;
  height: auto;
  object-fit: contain;
}

.balance-info {
  color: #FFFFFF;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.balance-label {
  font-size: 14px;
  font-weight: 400;
  margin-bottom: 3px;
}

.balance-amount {
  font-size: 20px;
  font-weight: 600;
  color: #FFFFFF;
}

.transfer-form {
  background-color: #fff;
  padding: 0 16px;
}

.form-item {
  padding: 20px 0;
  border-bottom: 1px solid #f0f0f0;
}

.form-item:last-child {
  border-bottom: none;
}

.item-label {
  font-size: 16px;
  color: #333;
  margin-bottom: 10px;
}

.item-input {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.item-input input {
  width: 100%;
  border: none;
  outline: none;
  font-size: 16px;
  color: #333;
}

.item-input input::placeholder {
  color: #ccc;
}

.amount-input {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
  margin-bottom: 10px;
}

.currency {
  font-size: 24px;
  font-weight: 500;
  color: #333;
  margin-right: 10px;
}

.amount-input input {
  flex: 1;
  border: none;
  outline: none;
  font-size: 24px;
  color: #333;
}

.amount-input input::placeholder {
  color: #ccc;
  font-size: 16px;
}

.amount-tips {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #999;
}

.all-btn {
  color: #0474FC;
  cursor: pointer;
}

.transfer-tips {
  background-color: #fff;
  padding: 20px 16px;
  margin-top: 10px;
}

.tips-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

.tips-content {
  font-size: 12px;
  color: #999;
  line-height: 1.6;
}

.tips-content p {
  margin-bottom: 8px;
}

.tips-content p:last-child {
  margin-bottom: 0;
}

.bottom-btn {
  position: fixed;
  bottom: 20px;
  left: 16px;
  right: 16px;
  z-index: 100;
}

/* 加载状态样式 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #999;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .header {
    padding: 8px 12px;
  }

  .balance-section {
    padding: 16px 12px;
  }

  .transfer-form {
    padding: 0 12px;
  }

  .bottom-btn {
    left: 12px;
    right: 12px;
  }
}
</style>