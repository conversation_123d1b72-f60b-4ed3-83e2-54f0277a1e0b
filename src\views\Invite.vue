<template>
  <div class="invite-page">
    <!-- 状态栏 -->
    <div class="status-bar"></div>

    <!-- 背景图片 -->
    <div class="background-gradient">
      <img src="@/assets/images/invite/invite-bg-gradient.png" alt="背景渐变">
    </div>

    <!-- 圆形装饰图 -->
    <div class="decorative-circles">
      <div class="circle-large"></div>
      <div class="circle-overlay">
        <img src="@/assets/images/invite/invite-hero-bg.png" alt="人物背景" class="hero-image">
      </div>
    </div>

    <!-- 导航栏 -->
    <div class="header">
      <div class="back-icon" @click="goBack">
        <img src="@/assets/images/invite/back-button.png" alt="返回">
      </div>
      <div class="title">邀请好友</div>
    </div>

    <!-- 主要内容区域 -->
    <div class="content-area">
      <!-- 标题图片 -->
      <div class="title-image">
        <img src="@/assets/images/invite/invite-title.png" alt="邀请标题">
      </div>

      <!-- 邀请码卡片 -->
      <div class="invite-card-container">
        <!-- 加载状态 -->
        <div class="loading-container" v-if="loading">
          <div class="loading-spinner"></div>
          <div class="loading-text">加载中...</div>
        </div> <!-- 邀请码卡片 -->
        <div class="invite-card" v-else>
          <!-- 边框装饰 -->
          <div class="left-border"></div>
          <div class="right-border"></div>
          <div class="bottom-border"></div>
          <!-- 邀请码部分 -->
          <div class="invite-code-label">邀请码：</div>
          <div class="invite-code-display">
            <div class="invite-code-value">{{ inviteCode || '获取中...' }}</div>
          </div> <!-- 复制按钮 -->
          <div class="card-actions">
            <button class="copy-btn" @click="copyInviteCode">
              <div class="copy-btn-content">
                <svg class="copy-icon" width="18" height="18" viewBox="0 0 24 24" fill="none"
                  xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M8 4V16C8 16.5304 8.21071 17.0391 8.58579 17.4142C8.96086 17.7893 9.46957 18 10 18H18C18.5304 18 19.0391 17.7893 19.4142 17.4142C19.7893 17.0391 20 16.5304 20 16V7.242C20 6.97556 19.9467 6.71181 19.8433 6.46624C19.7399 6.22068 19.5885 5.99824 19.398 5.812L16.188 2.602C15.8129 2.22698 15.3163 2.0157 14.799 2H10C9.46957 2 8.96086 2.21071 8.58579 2.58579C8.21071 2.96086 8 3.46957 8 4Z"
                    stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                  <path
                    d="M16 18V20C16 20.5304 15.7893 21.0391 15.4142 21.4142C15.0391 21.7893 14.5304 22 14 22H6C5.46957 22 4.96086 21.7893 4.58579 21.4142C4.21071 21.0391 4 20.5304 4 20V9C4 8.46957 4.21071 7.96086 4.58579 7.58579C4.96086 7.21071 5.46957 7 6 7H8"
                    stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
                <span>复制邀请码</span>
              </div>
            </button>

            <button class="download-btn" @click="copyDownloadLink">
              <div class="copy-btn-content">
                <svg class="copy-icon" width="18" height="18" viewBox="0 0 24 24" fill="none"
                  xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15"
                    stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                  <path d="M7 10L12 15L17 10" stroke="white" stroke-width="2" stroke-linecap="round"
                    stroke-linejoin="round" />
                  <path d="M12 15V3" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
                <span>复制下载地址</span>
              </div>
            </button>
          </div>
        </div>
      </div>

      <!-- 装饰元素 -->
      <div class="decorative-elements">
        <div class="decorative-1"></div>
        <div class="decorative-2"></div>
        <div class="decorative-3"></div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Invite',
  data() {
    return {
      inviteCode: '',
      inviteUrl: '',
      loading: true
    }
  },
  created() {
    this.getInviteInfo()
  },
  methods: {
    // 获取邀请信息
    async getInviteInfo() {
      this.loading = true

      const userToken = localStorage.getItem('user_token')
      if (!userToken) {
        this.$toast('请先登录')
        this.$nextTick(() => {
          this.$router.replace('/login')
        })
        return
      }

      try {
        const response = await this.$http.get('/member/getInvite', {
          headers: {
            'authorization': userToken
          }
        })

        if (response.data.code === 200) {
          const data = response.data.data
          if (data && data.info) {
            this.inviteCode = data.info.invite
            this.inviteUrl = data.info.url
          }
        } else {
          this.$toast(response.data.msg || '获取邀请信息失败')
        }
      } catch (error) {
        console.error('获取邀请信息失败:', error)

        // 处理401/403错误 - token过期或无效
        if (error.response && (error.response.status === 401 || error.response.status === 403)) {
          localStorage.removeItem('user_token')
          localStorage.removeItem('userInfo')
          this.$toast('登录已过期，请重新登录')
          this.$nextTick(() => {
            this.$router.replace('/login')
          })
        } else {
          this.$toast('网络错误，请稍后重试')
        }
      } finally {
        this.loading = false
      }
    },

    goBack() {
      try {
        // 检查是否有历史记录可以返回
        if (window.history.length > 1) {
          this.$router.go(-1)
        } else {
          // 如果没有历史记录，跳转到首页
          this.$router.push('/')
        }
      } catch (error) {
        // 备用方案：直接使用 window.history
        if (window.history.length > 1) {
          window.history.back()
        } else {
          window.location.href = '/'
        }
      }
    },

    copyInviteCode() {
      try {
        const textArea = document.createElement('textarea')
        textArea.value = this.inviteCode
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        this.$toast && this.$toast('邀请码已复制')
      } catch (error) {
        this.$toast && this.$toast('复制失败，请手动复制')
      }
    },

    copyDownloadLink() {
      try {
        const textArea = document.createElement('textarea')
        textArea.value = this.inviteUrl || `邀请码：${this.inviteCode}`
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        this.$toast && this.$toast('邀请链接已复制')
      } catch (error) {
        this.$toast && this.$toast('复制失败，请手动复制')
      }
    }
  }
}
</script>

<style scoped>
.invite-page {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  background: #FFFFFF;
}

/* 状态栏 */
.status-bar {
  height: 20px;
  width: 100%;
  z-index: 100;
}

/* 背景渐变 */
.background-gradient {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.background-gradient img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 圆形装饰背景 */
.decorative-circles {
  position: absolute;
  top: -168px;
  left: -167px;
  width: 702px;
  height: 519px;
  z-index: 2;
  pointer-events: none;
}

.circle-large {
  position: absolute;
  width: 692px;
  height: 511px;
  border-radius: 50%;
  background: #5EC5FF;
  top: 8px;
  left: 0;
}

.circle-overlay {
  position: absolute;
  width: 692px;
  height: 511px;
  border-radius: 50%;
  overflow: hidden;
  top: 0;
  left: 10px;
  background: #D9D9D9;
}

.hero-image {
  width: 497px;
  height: 884px;
  position: absolute;
  top: -198px;
  left: 73px;
  object-fit: cover;
}

/* 导航栏 */
.header {
  position: relative;
  z-index: 10;
  display: flex;
  align-items: center;
  padding: 10px 15px;
  height: 44px;
}

.back-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: relative;
  -webkit-tap-highlight-color: transparent;
}

.back-icon img {
  width: 10px;
  height: 16px;
}

.title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 400;
  color: white;
  font-family: 'PingFang SC', sans-serif;
}

/* 主内容区域 */
.content-area {
  position: relative;
  z-index: 5;
  padding: 0 15px;
  margin-top: 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 标题图片 */
.title-image {
  position: relative;
  z-index: 15;
  text-align: center;
  margin-top: 0;
  width: 100%;
  max-width: 320px;
}

.title-image img {
  width: 100%;
  filter: drop-shadow(0.6px 1px 3.6px rgba(126, 16, 16, 0.75));
}

/* 邀请码卡片容器 */
.invite-card-container {
  position: relative;
  z-index: 20;
  width: 320px;
  max-width: 90%;
  margin: 15px auto 0;
  border-radius: 12px;
  overflow: visible;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

/* 圆形切口 */
.invite-card-container::before,
.invite-card-container::after {
  content: "";
  position: absolute;
  width: 24px;
  height: 24px;
  background-color: #037cfc;
  border-radius: 50%;
  z-index: 25;
}

.invite-card-container::before {
  top: 65%;
  left: -12px;
  transform: translateY(-50%);
}

.invite-card-container::after {
  top: 65%;
  right: -12px;
  transform: translateY(-50%);
}

/* 邀请卡片 */
.invite-card {
  background: white;
  border-radius: 12px;
  border: 1px solid rgba(224, 224, 224, 0.6);
  padding: 35px 15px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
  overflow: hidden;
  z-index: 20;
}

/* 完全重构边框实现，确保切口处边框的连续性 */
.invite-card::before {
  content: "";
  position: absolute;
  top: 5px;
  left: 5px;
  right: 5px;
  bottom: 5px;
  border: 2px solid rgba(3, 124, 252, 0.15);
  border-radius: 8px;
  pointer-events: none;
  z-index: 1;
  /* 使用clip-path创建沿切口形状的边框 */
  clip-path: polygon(
      /* 上左角圆角区 */
      0% 8px, 8px 0%,
      /* 上边沿 */
      calc(100% - 8px) 0%, 100% 8px,
      /* 右边沿到切口 */
      100% calc(65% - 14px),
      /* 右切口形状 - 半圆弧形 */
      calc(100% - 14px) calc(65% - 12px),
      calc(100% - 11px) 65%,
      calc(100% - 14px) calc(65% + 12px),
      /* 右边沿下半部分 */
      100% calc(65% + 14px),
      100% calc(100% - 8px),
      /* 下右角圆角区 */
      calc(100% - 8px) 100%,
      /* 下边沿 */
      8px 100%,
      /* 下左角圆角区 */
      0% calc(100% - 8px),
      /* 左边沿下半部分 */
      0% calc(65% + 14px),
      /* 左切口形状 - 半圆弧形 */
      14px calc(65% + 12px),
      11px 65%,
      14px calc(65% - 12px),
      /* 左边沿上半部分 */
      0% calc(65% - 14px));
}

/* 圆形切口左侧弧形边框 */
.invite-card>.left-border {
  content: "";
  position: absolute;
  left: -12px;
  top: calc(65% - 12px);
  width: 17px;
  height: 24px;
  border: none;
  border-right: 2px solid rgba(3, 124, 252, 0.15);
  border-radius: 0 12px 12px 0;
  z-index: 3;
}

/* 圆形切口右侧弧形边框 */
.invite-card>.right-border {
  content: "";
  position: absolute;
  right: -12px;
  top: calc(65% - 12px);
  width: 17px;
  height: 24px;
  border: none;
  border-left: 2px solid rgba(3, 124, 252, 0.15);
  border-radius: 12px 0 0 12px;
  z-index: 3;
}

/* 底部边框 - 隐藏，由clip-path主边框实现 */
.invite-card>.bottom-border {
  display: none;
}

/* 蓝色圆形切口 */
.invite-card-container::before,
.invite-card-container::after {
  content: "";
  position: absolute;
  width: 24px;
  height: 24px;
  background-color: #037cfc;
  border-radius: 50%;
  z-index: 25;
}

.invite-card-container::before {
  top: 65%;
  left: -12px;
  transform: translateY(-50%);
}

.invite-card-container::after {
  top: 65%;
  right: -12px;
  transform: translateY(-50%);
}

/* 邀请码卡片 */
.invite-card {
  background: white;
  border-radius: 12px;
  border: 1px solid rgba(224, 224, 224, 0.6);
  padding: 35px 15px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
  position: relative;
  overflow: hidden;
  z-index: 20;
}

/* 邀请码标签 */
.invite-code-label {
  font-size: 18px;
  color: #333;
  font-weight: 500;
  margin-bottom: 22px;
  text-align: center;
}

/* 邀请码显示 */
.invite-code-display {
  background: linear-gradient(90deg, #F7BD34 0%, #EDBC33 100%);
  border-radius: 20px;
  padding: 13px 20px;
  width: 170px;
  text-align: center;
  margin-bottom: 30px;
  box-shadow: 0 2px 8px rgba(237, 188, 51, 0.25);
}

.invite-code-value {
  font-size: 16px;
  font-weight: 700;
  color: #FFFFFF;
  text-align: center;
  letter-spacing: 1.5px;
}

/* 加载状态 */
.loading-container {
  background: #FAFEFD;
  border-radius: 10px;
  border: 1px solid #0474FC;
  padding: 40px 20px;
  box-shadow: 4.57px 21.52px 17px rgba(129, 12, 12, 0.36);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 15px;
  width: 100%;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(4, 116, 252, 0.2);
  border-top: 3px solid #0474FC;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-family: 'PingFang SC', sans-serif;
  font-size: 16px;
  color: #414655;
  font-weight: 400;
}

/* 卡片内操作按钮 */
.card-actions {
  display: flex;
  flex-direction: column;
  gap: 18px;
  align-items: center;
  margin-top: 25px;
  width: 100%;
  max-width: 260px;
}

.copy-btn,
.download-btn {
  width: 240px;
  height: 50px;
  background: linear-gradient(90deg, #3B7EF8 0%, #548BFF 100%);
  border-radius: 25px;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4px 10px rgba(59, 126, 248, 0.25);
}

.copy-btn:active,
.download-btn:active {
  transform: scale(0.98);
  opacity: 0.9;
}

.copy-btn-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.copy-icon {
  width: 20px;
  height: 20px;
}

.copy-btn span,
.download-btn span {
  color: #FFFFFF;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

/* 装饰元素 */
.decorative-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 5;
}

.decorative-1 {
  position: absolute;
  width: 35.08px;
  height: 35.65px;
  top: 174px;
  right: 31px;
}

.decorative-2 {
  position: absolute;
  width: 70px;
  height: 64px;
  top: 205px;
  left: -19px;
}

.decorative-3 {
  position: absolute;
  width: 40.31px;
  height: 44px;
  bottom: 150px;
  left: 50px;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .invite-card-container {
    width: 300px;
  }

  .invite-card-container::before,
  .invite-card-container::after {
    width: 20px;
    height: 20px;
  }

  .invite-card-container::before {
    left: -10px;
  }

  .invite-card-container::after {
    right: -10px;
  }

  .title-image {
    max-width: 300px;
  }

  .invite-code-display {
    width: 160px;
    padding: 10px 15px;
  }

  .invite-code-value {
    font-size: 15px;
  }

  .copy-btn,
  .download-btn {
    width: 220px;
    height: 46px;
  }

  .copy-btn span,
  .download-btn span {
    font-size: 15px;
  }

  /* 加载状态移动端适配 */
  .loading-container {
    padding: 30px 15px;
  }

  .loading-spinner {
    width: 35px;
    height: 35px;
    border-width: 2px;
  }

  .loading-text {
    font-size: 14px;
  }
}

/* 适配安全区域 */
@supports (padding: max(0px)) {
  .status-bar {
    padding-top: max(0px, env(safe-area-inset-top));
  }
}
</style>
