<template>
  <div class="profile">
    <!-- 头部 -->
    <div class="header">
      <div class="back-icon" @click="goBack">
        <img src="@/assets/icons/back-icon.svg" alt="返回">
      </div>
      <div class="title">个人信息</div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <van-loading type="spinner" color="#FF618B">加载中...</van-loading>
    </div>

    <!-- 个人信息列表 -->
    <div v-else class="profile-list">
      <!-- 头像 -->
      <div class="profile-item" @click="uploadAvatar">
        <div class="item-label">头像</div>
        <div class="item-content avatar">
          <img 
            :src="userInfo.avatar || require('@/assets/images/avatar.jpg')" 
            alt="用户头像"
            @error="handleImageError"
          >
        </div>
        <div class="item-arrow">
          <van-icon name="arrow" />
        </div>
      </div>
      
      <!-- 昵称 -->
      <div class="profile-item" @click="editNickname">
        <div class="item-label">昵称</div>
        <div class="item-content">{{ userInfo.nickname }}</div>
        <div class="item-arrow">
          <van-icon name="arrow" />
        </div>
      </div>
        <!-- 手机号 -->
      <div class="profile-item disabled">
        <div class="item-label">手机号</div>
        <div class="item-content">{{ formatPhone(userInfo.mobile) }}</div>
        <div class="item-arrow disabled">
          <van-icon name="arrow" />
        </div>
      </div>

      <!-- 实名认证 -->
      <div class="profile-item" :class="{ disabled: userInfo.kyc }" @click="goToVerification">
        <div class="item-label">实名认证</div>
        <div class="item-content">{{ userInfo.kyc ? '已认证' : '未认证' }}</div>
        <div class="item-arrow" :class="{ disabled: userInfo.kyc }">
          <van-icon name="arrow" />
        </div>
      </div>
    </div>    <!-- 昵称编辑弹窗 -->
    <van-popup v-model="showNicknameEdit" position="bottom" round>
      <div class="edit-popup">
        <div class="popup-title">修改昵称</div>
        <div class="popup-input">
          <input type="text" v-model="newNickname" placeholder="请输入昵称">
        </div>
        <div class="popup-actions">
          <div class="action-btn cancel" @click="showNicknameEdit = false">取消</div>
          <div class="action-btn confirm" @click="confirmNickname">确认</div>
        </div>
      </div>
    </van-popup>

    <!-- 手机号编辑弹窗 -->
    <van-popup v-model="showPhoneEdit" position="bottom" round>
      <div class="edit-popup">
        <div class="popup-title">修改手机号</div>
        <div class="popup-input">
          <input type="tel" v-model="editPhone" placeholder="请输入新手机号">
        </div>
        <div class="popup-input verification">
          <input type="text" v-model="verificationCode" placeholder="请输入验证码">
          <div class="send-code-btn" @click="sendVerificationCode">
            {{ countdown > 0 ? `${countdown}s后可重新获取` : '获取验证码' }}
          </div>
        </div>
        <div class="popup-actions">
          <div class="action-btn cancel" @click="showPhoneEdit = false">取消</div>
          <div class="action-btn confirm" @click="confirmPhone">确认</div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
export default {
  name: 'Profile',
  data() {
    return {
      userInfo: {
        avatar: '',
        nickname: '',
        mobile: '',
        kyc: false,
        finance: false
      },      showNicknameEdit: false,
      showPhoneEdit: false,
      newNickname: '',
      editPhone: '',
      verificationCode: '',
      countdown: 0,
      loading: false
    }
  },
  mounted() {
    this.getUserInfo()
  },
  
  methods: {    // 获取用户信息
    async getUserInfo() {
      this.loading = true
      try {
        const token = localStorage.getItem('user_token')
        if (!token) {
          this.$router.replace('/login')
          return
        }

        const response = await this.$http.get('/member/getAccount', {
          headers: {
            'authorization': token
          }
        })

        if (response.data.code === 200) {
          this.userInfo = {
            avatar: response.data.data.info.avatar,
            nickname: response.data.data.info.nickname,
            mobile: response.data.data.info.mobile,
            kyc: response.data.data.info.kyc,
            finance: response.data.data.info.finance
          }
        } else {
          this.$toast(response.data.msg || '获取用户信息失败')
        }
      } catch (error) {
        console.error('获取用户信息失败:', error)
        this.$toast('网络错误，请稍后重试')
      } finally {
        this.loading = false
      }
    },
    
    goBack() {
      this.$router.go(-1)
    },
      goToVerification() {
      // 检查是否已实名认证
      if (this.userInfo.kyc) {
        this.$toast('您已完成实名认证')
        return
      }
      this.$router.push('/verification')
    },    // 头像上传
    uploadAvatar() {
      // 创建文件输入元素
      const input = document.createElement('input')
      input.type = 'file'
      input.accept = 'image/*'
      input.style.display = 'none'
      
      input.onchange = async (event) => {
        const file = event.target.files[0]
        if (!file) return

        // 检查文件大小 (限制为5MB)
        if (file.size > 5 * 1024 * 1024) {
          this.$toast('图片大小不能超过5MB')
          return
        }        this.$toast.loading('上传中...')
        try {
          const token = localStorage.getItem('user_token')
          const formData = new FormData()
          formData.append('file', file)

          const response = await this.$http.post('/tools/uploadAvatar', formData, {
            headers: {
              'authorization': token,
              'Content-Type': 'multipart/form-data'
            }
          })

          if (response.data.code === 200) {
            // 拼接完整的头像URL：domain + name
            const avatarUrl = response.data.data.domain + response.data.data.name
            
            // 更新本地头像显示
            this.userInfo.avatar = avatarUrl
            
            // 同步更新到localStorage中的用户信息（如果存在）
            const userInfo = localStorage.getItem('user_info')
            if (userInfo) {
              try {
                const userData = JSON.parse(userInfo)
                if (userData.info) {
                  userData.info.avatar = avatarUrl
                  localStorage.setItem('user_info', JSON.stringify(userData))
                }
              } catch (e) {
                console.warn('更新localStorage用户信息失败:', e)
              }
            }
            
            this.$toast.clear()
            this.$toast('头像上传成功')
          } else {
            this.$toast.clear()
            this.$toast(response.data.msg || '头像上传失败')
          }
        } catch (error) {
          console.error('头像上传失败:', error)
          this.$toast.clear()
          this.$toast('网络错误，请稍后重试')
        }
      }
      
      document.body.appendChild(input)
      input.click()
      document.body.removeChild(input)
    },
    
    formatPhone(phone) {
      if (!phone) return ''
      return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
    },
      // 编辑昵称
    editNickname() {
      this.showNicknameEdit = true
    },
    
    // 图片加载错误处理
    handleImageError(event) {
      event.target.src = require('@/assets/images/avatar.jpg')
    },
      // 修改昵称
    async confirmNickname() {      if (!this.newNickname) {
        this.$toast('昵称不能为空')
        return
      }

      this.$toast.loading('修改中...')
      
      try {
        const token = localStorage.getItem('user_token')
        const response = await this.$http.post('/member/doNickname', {
          nickname: this.newNickname
        }, {
          headers: {
            'authorization': token
          }
        })

        if (response.data.code === 200) {
          this.userInfo.nickname = this.newNickname
          this.showNicknameEdit = false
          this.$toast.clear()
          this.$toast('昵称修改成功')
        } else {
          this.$toast.clear()
          this.$toast(response.data.msg || '昵称修改失败')
        }
      } catch (error) {
        console.error('昵称修改失败:', error)
        this.$toast.clear()
        this.$toast('网络错误，请稍后重试')
      }
    },
    
    sendVerificationCode() {
      if (this.countdown > 0) return

      if (!this.editPhone) {
        this.$toast('请输入手机号')
        return
      }

      // 实际项目中应该调用发送验证码API
      this.$toast('验证码已发送')
      this.countdown = 60
      const timer = setInterval(() => {
        this.countdown--
        if (this.countdown <= 0) {
          clearInterval(timer)
        }
      }, 1000)
    },
    
    confirmPhone() {
      if (!this.editPhone) {
        this.$toast('手机号不能为空')
        return
      }

      if (!this.verificationCode) {
        this.$toast('验证码不能为空')
        return
      }

      // 实际项目中应该调用验证API
      this.userInfo.phone = this.editPhone
      this.showPhoneEdit = false
      this.editPhone = ''
      this.verificationCode = ''
      this.$toast('手机号修改成功')
    }
  },
    watch: {
    showNicknameEdit(val) {
      if (val) {
        this.newNickname = this.userInfo.nickname
      }
    },
    showPhoneEdit(val) {
      if (val) {
        this.editPhone = ''
        this.verificationCode = ''
      }
    }
  }
}
</script>

<style scoped>
.profile {
  min-height: 100vh;
  background-color: #f6f6f6;
}

.header {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  background-color: #fff;
  position: relative;
}

.back-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon img {
  width: 20px;
  height: 20px;
}

.title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.profile-list {
  background-color: #fff;
  margin-top: 10px;
}

.profile-item {
  display: flex;
  align-items: center;
  padding: 15px 16px;
  border-bottom: 1px solid #f5f5f5;
  cursor: pointer;
}

.profile-item.disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.profile-item:last-child {
  border-bottom: none;
}

.item-label {
  width: 80px;
  font-size: 16px;
  color: #333;
}

.item-content {
  flex: 1;
  font-size: 16px;
  color: #666;
  text-align: right;
  margin-right: 10px;
}

.item-content.avatar {
  display: flex;
  justify-content: flex-end;
}

.item-content.avatar img {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  object-fit: cover;
}

.item-arrow {
  color: #999;
}

.item-arrow.disabled {
  color: #ccc;
}

.edit-popup {
  padding: 20px;
}

.popup-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  text-align: center;
  margin-bottom: 20px;
}

.popup-input {
  border-bottom: 1px solid #eee;
  padding: 10px 0;
  margin-bottom: 20px;
}

.popup-input input {
  width: 100%;
  border: none;
  outline: none;
  font-size: 16px;
  color: #333;
}

.popup-input.verification {
  display: flex;
  align-items: center;
}

.popup-input.verification input {
  flex: 1;
}

.send-code-btn {
  padding: 8px 12px;
  background-color: #FF618B;
  color: #fff;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
}

.popup-actions {
  display: flex;
}

.action-btn {
  flex: 1;
  height: 44px;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  margin: 0 10px;
}

.action-btn.cancel {
  background-color: #f5f5f5;
  color: #666;
}

.action-btn.confirm {
  background-color: #FF618B;
  color: #fff;
}
</style>
