<template>
  <div class="news-page">
    <!-- 头部 -->
    <div class="header">
      <div class="back-button" @click="$router.back()">
        <img src="@/assets/icons/back-icon.svg" alt="返回" class="back-icon">
      </div>
      <span class="title">新闻公告</span>
    </div>

    <!-- 标签栏 -->
    <van-tabs v-model="activeTab" sticky swipeable @change="handleTabChange">
      <van-tab v-for="tab in tabs" :key="tab.type" :title="tab.title">
        <!-- 新闻列表 -->
        <div class="news-list" v-if="newsList.length > 0">
          <div class="news-item" v-for="(item, index) in newsList" :key="index" @click="handleNewsClick(item)">
            <div class="news-image">
              <img :src="item.pic" alt="新闻图片">
            </div>
            <div class="news-content">
              <div class="news-title">{{ item.title }}</div>
              <div class="news-info">
                <span class="news-time">{{ formatDate(item.create_time) }}</span>
                <span class="news-type">{{ getNewsTypeName(currentType) }}</span>
              </div>
            </div>
          </div>
        </div> <!-- 无数据提示 -->
        <div class="no-data" v-else>
          <van-empty image="search" description="暂无数据" />
        </div>

        <!-- 加载状态 -->
        <div class="loading-container" v-if="loading">
          <van-loading type="spinner" color="##3575FF" />
          <span>加载中..</span>
        </div>

        <!-- 没有更多数据 -->
        <div class="no-more" v-if="finished && newsList.length > 0">
          没有更多数据了
        </div>
      </van-tab>
    </van-tabs>
  </div>
</template>

<script>
import { Empty } from 'vant'

export default {
  name: 'News',
  components: {
    'van-empty': Empty
  },
  // 路由守卫，进入前检查登录状态
  beforeRouteEnter(to, from, next) {
    const userToken = localStorage.getItem('user_token')
    if (!userToken) {
      next('/login')
    } else {
      next()
    }
  },
  data() {
    return {
      activeTab: 0, // 当前激活的标签
      tabs: [
        { title: '全部', type: 0 },
        { title: '公告', type: 1 },
        { title: '活动', type: 2 },
        { title: '资讯', type: 3 }
      ],
      newsList: [], // 新闻列表
      currentPage: 1, // 当前页码
      loading: false, // 是否正在加载
      finished: false, // 是否已加载完所有数据
      currentType: 0 // 当前新闻类型
    }
  },
  created() {
    // 检查登录状态并加载数据
    this.checkLoginAndLoadNews()

    // 添加滚动监听
    window.addEventListener('scroll', this.handleScroll)
  },
  destroyed() {
    // 移除滚动监听
    window.removeEventListener('scroll', this.handleScroll)
  }, methods: {
    // 检查登录状态并加载新闻数据
    checkLoginAndLoadNews() {
      const userToken = localStorage.getItem('user_token')
      if (!userToken) {
        this.redirectToLogin()
        return
      }
      this.loadNews()
    },

    // 重定向到登录页
    redirectToLogin() {
      this.$toast('请先登录')
      this.$nextTick(() => {
        this.$router.replace('/login')
      })
    },

    // 加载新闻数据
    loadNews() {
      if (this.loading || this.finished) return

      this.loading = true

      // 从localStorage获取token
      const userToken = localStorage.getItem('user_token')
      if (!userToken) {
        this.redirectToLogin()
        this.loading = false
        return
      }

      // 发起API请求 - 使用正确的API端点
      this.$http.get(`/news/getlist`, {
        params: {
          type: this.currentType,
          page: this.currentPage
        },
        headers: {
          'authorization': userToken
        }
      })
        .then(response => {
          if (response.data.code === 200) {
            // 处理API返回的数据格式 {code: 200, data: {list: [...], total: xxx}}
            const newData = response.data.data && response.data.data.list ? response.data.data.list : []

            if (newData && newData.length > 0) {
              // 首页加载或切换标签时，替换列表
              if (this.currentPage === 1) {
                this.newsList = newData
              } else {
                // 加载更多时，追加到列表
                this.newsList = [...this.newsList, ...newData]
              }

              // 如果返回的数据少于预期，说明已经到达末页
              if (newData.length < 10) {
                this.finished = true
              } else {
                this.currentPage++
              }
            } else {
              this.finished = true
              if (this.currentPage === 1) {
                this.newsList = []
              }
            }
          } else {
            // 处理API返回的错误信息
            this.$toast(response.data.msg || '获取新闻列表失败')
          }
        })
        .catch(error => {
          console.error('获取新闻列表失败:', error)

          // 处理401/403错误 - token过期或无效
          if (error.response && (error.response.status === 401 || error.response.status === 403)) {
            localStorage.removeItem('user_token')
            localStorage.removeItem('userInfo')
            this.$toast('登录已过期，请重新登录')
            this.$nextTick(() => {
              this.$router.replace('/login')
            })
          } else {
            this.$toast('网络错误，请稍后重试')
          }
        })
        .finally(() => {
          this.loading = false
        })
    },

    // 处理滚动事件实现分页加载
    handleScroll() {
      // 检查是否滚动到底部
      const scrollTop = document.documentElement.scrollTop || document.body.scrollTop
      const scrollHeight = document.documentElement.scrollHeight || document.body.scrollHeight
      const clientHeight = document.documentElement.clientHeight || window.innerHeight

      if (scrollTop + clientHeight >= scrollHeight - 50) {
        // 距离底部50px时加载更多数据
        this.loadNews()
      }
    },

    // 处理标签切换
    handleTabChange(index) {
      this.currentType = this.tabs[index].type
      this.currentPage = 1
      this.finished = false
      this.newsList = []
      this.loadNews()
    },
    // 处理新闻点击
    handleNewsClick(news) {
      // 如果有URL并且不为空，则进行跳转
      if (news.url && news.url.trim() !== '') {

        // 在新窗口打开
        window.location.href = news.url

      }
    },
    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return ''

      // API返回的格式是 "YYYY-MM-DD HH:MM:SS"，我们只需要提取日期部分
      if (typeof dateString === 'string') {
        // 直接返回日期部分 "YYYY-MM-DD"
        return dateString.split(' ')[0]
      }

      // 如果传入的是时间戳，则按原来的方式处理
      if (typeof dateString === 'number') {
        const date = new Date(dateString * 1000)
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')

        return `${year}-${month}-${day}`
      }

      return ''
    },

    // 获取新闻类型名称
    getNewsTypeName(type) {
      switch (parseInt(type)) {
        case 1:
          return '公告'
        case 2:
          return '活动'
        case 3:
          return '资讯'
        default:
          return '全部'
      }
    }
  }
}
</script>

<style scoped>
.news-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 20px;
}

.header {
  display: flex;
  align-items: center;
  height: 44px;
  background-color: #fff;
  position: relative;
  padding: 0 15px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.back-button {
  position: absolute;
  left: 15px;
  display: flex;
  align-items: center;
}

.back-icon {
  width: 20px;
  height: 20px;
}

.title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
}

/* 标签页样式 */
::v-deep .van-tabs__line {
  background-color: #3575FF;
}

::v-deep .van-tab--active {
  color: #3575FF;
  font-weight: 500;
}

/* 新闻列表样式 */
.news-list {
  padding: 10px 15px;
}

.news-item {
  display: flex;
  padding: 15px;
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.news-image {
  width: 120px;
  height: 80px;
  margin-right: 12px;
  overflow: hidden;
  border-radius: 6px;
}

.news-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.news-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.news-title {
  font-size: 16px;
  font-weight: 500;
  line-height: 1.4;
  margin-bottom: 6px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  overflow: hidden;
}

.news-info {
  display: flex;
  justify-content: space-between;
  color: #999;
  font-size: 14px;
}

/* 无数据和加载状态样式 */
.no-data,
.loading-container,
.no-more {
  padding: 20px 0;
  text-align: center;
  color: #999;
  font-size: 14px;
}

.no-data-img {
  width: 120px;
  height: 120px;
  margin-bottom: 10px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10px;
}
</style>
