/**
 * 设备信息工具类
 * 用于生成登录所需的设备信息
 */

/**
 * 生成设备ID
 * @returns {string} 设备ID
 */
export function generateDeviceId() {
  // 使用时间戳 + 随机数生成设备ID
  const timestamp = Date.now().toString()
  const random = Math.floor(Math.random() * 1000000).toString().padStart(6, '0')
  return timestamp + random
}

/**
 * 获取平台信息
 * @returns {string} 平台类型 android/ios/web
 */
export function getPlatform() {
  const userAgent = navigator.userAgent
  
  if (/Android/i.test(userAgent)) {
    return 'android'
  } else if (/iPhone|iPad|iPod/i.test(userAgent)) {
    return 'ios'
  } else {
    return 'web'
  }
}

/**
 * 获取设备型号
 * @returns {string} 设备型号
 */
export function getDeviceModel() {
  const userAgent = navigator.userAgent
  
  if (/Android/i.test(userAgent)) {
    // 尝试从 UserAgent 中提取 Android 设备型号
    const modelMatch = userAgent.match(/\(([^)]+)\)/)
    if (modelMatch && modelMatch[1]) {
      const parts = modelMatch[1].split(';')
      // 通常设备型号在最后一部分
      const model = parts[parts.length - 1].trim()
      return model || 'Android Device'
    }
    return 'Android Device'
  } else if (/iPhone/i.test(userAgent)) {
    return 'iPhone'
  } else if (/iPad/i.test(userAgent)) {
    return 'iPad'
  } else if (/iPod/i.test(userAgent)) {
    return 'iPod'
  } else {
    return 'Web Browser'
  }
}

/**
 * 获取系统版本
 * @returns {string} 系统版本
 */
export function getSystemVersion() {
  const userAgent = navigator.userAgent
  
  if (/Android/i.test(userAgent)) {
    const androidMatch = userAgent.match(/Android\s([0-9.]+)/)
    return androidMatch ? `Android ${androidMatch[1]}` : 'Android'
  } else if (/iPhone|iPad|iPod/i.test(userAgent)) {
    const iosMatch = userAgent.match(/OS ([0-9_]+)/)
    if (iosMatch) {
      return `iOS ${iosMatch[1].replace(/_/g, '.')}`
    }
    return 'iOS'
  } else {
    return navigator.platform || 'Web Browser'
  }
}

/**
 * 获取完整的设备信息
 * @returns {Object} 设备信息对象
 */
export function getDeviceInfo() {
  return {
    deviceId: generateDeviceId(),
    platform: getPlatform(),
    model: getDeviceModel(),
    system: getSystemVersion()
  }
}

/**
 * 为了兼容原有代码，提供一个默认的设备信息
 * 按照接口要求的格式：Nexus 5, Android 6.0
 */
export function getDefaultDeviceInfo() {
  return {
    deviceId: generateDeviceId(),
    platform: 'android',
    model: 'Nexus 5',
    system: 'Android 6.0'
  }
}