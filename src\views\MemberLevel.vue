<template>
  <div class="member-level"> <!-- 头部 -->
    <div class="header">
      <div class="back-icon" @click="goBack">
        <svg width="10" height="17" viewBox="0 0 10 17" fill="none">
          <path d="M8.5 1.5L1.5 8.5L8.5 15.5" stroke="#000" stroke-width="2" />
        </svg>
      </div>
      <div class="title">会员等级</div>
    </div>

    <!-- 用户信息卡片 -->
    <div class="user-info">
      <div class="user-bg">
        <div class="background-pattern"></div>
        <div class="user-avatar">
          <img :src="userInfo.avatar" @error="handleImageError($event, 'avatar')" alt="用户头像" class="avatar">
        </div>
        <div class="username">{{ userInfo.nickname || '当前用户名' }}</div>
      </div>
    </div> <!-- 等级标题 -->
    <div class="level-title-container">
      <div class="level-title">{{ userInfo.level_name || '尊贵的等级' }}</div>
      <div class="level-line"></div>
    </div><!-- 等级列表容器 -->
    <div class="level-container" :style="{ height: containerHeight + 'px' }" v-if="!loading">
      <!-- 循环显示等级，每行3个 -->
      <div v-for="(level, index) in levels" :key="level.id" class="level-item"
        :class="{ 'current-level': level.level <= userInfo.level }" :style="{
          left: ((index % 3) * 115 + 15) + 'px',
          top: (Math.floor(index / 3) * 150 + 20) + 'px'
        }">
        <!-- 等级徽章 -->
        <div class="level-badge">
          <img :src="getLevelBadgeImage(level.level)" :alt="level.name">
        </div>

        <!-- 等级信息 -->
        <div class="level-info">
          <div class="level-header">
            <span class="level-name">{{ level.name }}</span>
            <div class="level-version">
              <span>v{{ level.level + 1 }}</span>
            </div>
          </div>
          <div class="level-stats">
            <p>有效推广：<span class="highlight-number">{{ level.upgrade_1 }}人</span></p>
            <p>个人累计充值：<span class="highlight-number">{{ level.upgrade_2 }}元</span></p>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div class="loading-container" v-if="loading">
      <div class="loading-text">正在加载会员等级信息...</div>
    </div>
  </div>
</template>

<script>
import ImageErrorHandler from '@/utils/ImageErrorHandler'

export default {
  name: 'MemberLevel',  data() {
    return {
      loading: true,
      userInfo: {
        level: 0,
        level_name: '普通会员',
        nickname: '',
        upgrade_1: 0,
        upgrade_2: 0
      },
      levels: []
    }
  },
  created() {
    this.checkLoginAndLoadData()
  },computed: {
    // 动态计算容器高度，根据等级数量
    containerHeight() {
      const totalLevels = this.levels.length;
      const rowsNeeded = Math.ceil(totalLevels / 3); // 每行3个，计算需要多少行
      const itemHeight = 150; // 每个等级项目的高度（包含间距）
      const paddingTop = 20; // 上边距
      const paddingBottom = 30; // 下边距
      return rowsNeeded * itemHeight + paddingTop + paddingBottom;
    }
  },  methods: {
    handleImageError(event, type) {
      ImageErrorHandler.handleImageError(event, type)
    },
    // 检查登录状态并加载数据
    checkLoginAndLoadData() {
      const userToken = localStorage.getItem('user_token')
      
      if (!userToken) {
        this.$toast('请先登录')
        this.$router.replace('/login')
        return
      }
      
      this.loadMemberLevelData()
    },
    
    // 加载会员等级数据
    loadMemberLevelData() {
      const userToken = localStorage.getItem('user_token')
      
      this.$http.get('/member/getLevel', {
        headers: {
          'authorization': userToken
        }
      })        .then(response => {
          if (response.data.code === 200) {
            const data = response.data.data
            if (data && data.user_info && data.level) {
              this.userInfo = data.user_info
              this.levels = data.level
            } else {
              this.$toast('数据格式错误')
            }
          } else {
            this.$toast(response.data.msg || '获取会员等级信息失败')
            if (response.data.code === 401 || response.data.code === 403) {
              localStorage.removeItem('user_token')
              this.$router.replace('/login')
            }
          }
        })
        .catch(error => {
          console.error('获取会员等级信息失败:', error)
          if (error.response && (error.response.status === 401 || error.response.status === 403)) {
            localStorage.removeItem('user_token')
            this.$router.replace('/login')
          } else {
            this.$toast('网络异常，请稍后重试')
          }        })
        .finally(() => {
          this.loading = false
        })
    },
    
    // 根据等级获取徽章图片
    getLevelBadgeImage(level) {
      try {
        return require(`@/assets/images/member-level/level-${level + 1}.png`)
      } catch (error) {
        // 如果图片不存在，返回默认图片
        return require('@/assets/images/member-level/level-1.png')
      }
    },
    
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style scoped>
.member-level {
  width: 375px;
  min-height: 812px;
  background: linear-gradient(to bottom, #F5F6E1 0%, #F8FAEE 49%, #F5F5F2 100%);
  position: relative;
  margin: 0 auto;
}

/* 头部 */
.header {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 44px;
  position: relative;
  padding: 0 16px;
}

.back-icon {
  position: absolute;
  left: 16px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.title {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 600;
  font-size: 16px;
  line-height: 22px;
  color: #000000;
  text-align: center;
}

/* 用户信息卡片 */
.user-info {
  position: relative;
  margin: 0;
  padding: 0;
}

.user-bg {
  width: 375px;
  height: 100px;
  background: linear-gradient(180deg, #F5F6E1 0%, #F5F5F2 100%);
  position: relative;
  overflow: hidden;
}

.background-pattern {
  position: absolute;
  top: 10px;
  right: 0;
  width: 120px;
  height: 80px;
  background-image: url('@/assets/images/member-level/background-pattern.png');
  background-size: cover;
  background-position: center;
  z-index: 1;
}

.user-bg::before {
  content: '';
  position: absolute;
  top: -30px;
  left: -60px;
  width: 160px;
  height: 160px;
  background: #FFFFEE;
  border-radius: 50%;
  filter: blur(15px);
  z-index: 0;
}

.user-avatar {
  position: absolute;
  left: 20px;
  top: 22px;
  width: 56px;
  height: 56px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3;
}

.user-avatar img {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
}

.username {
  position: absolute;
  left: 86px;
  top: 36px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 600;
  font-size: 18px;
  line-height: 28px;
  color: #333333;
  z-index: 3;
  width: 150px;
  height: 28px;
}

/* 等级标题 */
.level-title-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin: 20px 0 10px 37px;
}

.level-line {
  width: 34px;
  height: 10px;
  background: linear-gradient(90deg, #E3F760 0%, #B2E959 100%);
  margin-top: 8px;
}

.level-title {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 600;
  font-size: 16px;
  line-height: 22px;
  color: #333333;
}

/* 等级容器 */
.level-container {
  width: 347px;
  background: #FFFFFF;
  border-radius: 10px;
  margin: 0 14px 30px 14px;
  position: relative;
  padding: 20px 0 30px 0;
}

/* 等级项目 */
.level-item {
  position: absolute;
  width: 100px;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.3s ease;
}

/* 当前等级高亮 */
.level-item.current-level {
  transform: scale(1.05);
}

.level-item.current-level .level-badge {
  box-shadow: 0 0 15px rgba(255, 215, 0, 0.6);
  border-radius: 50%;
}

.level-item.current-level .level-name {
  color: #FFD700;
  font-weight: 700;
}

/* 等级徽章 */
.level-badge {
  width: 70px;
  height: 70px;
  margin-bottom: 8px;
}

.level-badge img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 等级信息 */
.level-info {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.level-header {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 20px;
  margin-bottom: 4px;
  gap: 4px;
}

.level-name {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 600;
  font-size: 12px;
  line-height: 16px;
  color: #333333;
  text-align: center;
}

.level-version {
  width: 18px;
  height: 14px;
  border: 1px solid #EA555F;
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.level-version span {
  font-family: 'Coda Caption', monospace;
  font-weight: 800;
  font-size: 8px;
  line-height: 12px;
  color: #EA555F;
}

.level-stats {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 9px;
  line-height: 12px;
  color: #999999;
  text-align: center;
}

.level-stats p {
  margin: 1px 0;
  padding: 0;
}

.highlight-number {
  color: #FF4444;
  font-weight: 600;
}

/* 加载状态 */
.loading-container {
  width: 347px;
  background: #FFFFFF;
  border-radius: 10px;
  margin: 0 14px 30px 14px;
  padding: 60px 20px;
  text-align: center;
}

.loading-text {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #666;
}
</style>

