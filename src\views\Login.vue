<template>
  <div class="login-page">
    <!-- 背景图形 -->
    <div class="background">
      <!-- 渐变背景 -->
      <div class="gradient-bg"></div>
      <!-- Logo区域 -->
      <div class="logo-container">
        <img src="@/assets/images/login/logo.png" alt="Logo" class="logo">
        <h1 class="brand-name">善之力</h1>
      </div>
    </div>

    <!-- 登录表单区域 -->
    <div class="login-container">
      <!-- 登录标题 -->
      <h2 class="login-title">登录</h2>

      <!-- 登录表单 -->
      <div class="login-form">
        <!-- 用户名输入框 -->
        <div class="input-group">
          <div class="input-box">
            <div class="input-icon phone-icon"></div>
            <input type="tel" v-model="phone" placeholder="请输入手机号码" maxlength="11">
          </div>
        </div>

        <!-- 密码输入框 -->
        <div class="input-group">
          <div class="input-box">
            <div class="input-icon lock-icon"></div>
            <input :type="passwordVisible ? 'text' : 'password'" v-model="password" placeholder="请输入密码" maxlength="20">
            <div class="eye-icon" @click="togglePasswordVisible">
              <img v-if="!passwordVisible" src="@/assets/images/login/eye-icon.svg" alt="显示密码">
              <img v-else src="@/assets/images/login/eye-open-icon.svg" alt="隐藏密码">
            </div>
          </div>
        </div>

        <!-- 记住密码和忘记密码 -->
        <div class="action-row">
          <div class="remember-pwd" @click="toggleRememberPassword">
            <div class="checkbox" :class="{ checked: rememberPassword }">
              <div class="checkbox-inner" v-if="rememberPassword"></div>
            </div>
            <span>记住密码</span>
          </div>
          <div class="forget-pwd" @click="goToCustomerService">忘记密码</div>
        </div> <!-- 登录按钮 -->
        <button class="login-btn" @click="login">登录</button>

        <!-- 注册链接 -->
        <div class="register-link" @click="goToRegister">
          <span>立即注册</span>
          <img src="@/assets/images/login/arrow.svg" alt="立即注册" class="arrow-icon">
        </div>

        <!-- 协议确认 -->
        <div class="agreement" @click="toggleAgreement">
          <div class="checkbox" :class="{ checked: agreeToTerms }">
            <div class="checkbox-inner" v-if="agreeToTerms"></div>
          </div>
          <span class="agreement-text">我已阅读同意《服务协议》和《隐私协议》</span>
        </div>
      </div>
    </div>

    <!-- 线路切换弹窗 -->
    <div class="line-switch-modal" v-if="showLineSwitch">
      <div class="line-switch-content">
        <div class="line-switch-title">线路切换</div>
        <div class="line-list">
          <div v-for="(line, index) in lines" :key="index" class="line-item"
            :class="{ 'active': currentLineIndex === index }" @click="switchLine(index)">
            <span>线路{{ index + 1 }}</span>
            <span class="ping-value">{{ line.ping }}ms</span>
          </div>
        </div>
        <div class="line-close-btn" @click="showLineSwitch = false">关闭</div>
      </div>
    </div>

    <!-- 线路切换按钮 -->
    <div class="line-switch-btn" @click="showLineSwitch = true">线路</div>
  </div>
</template>

<script>

export default {
  name: 'Login',
  data() {
    return {
      phone: '',
      password: '',
      passwordVisible: false,
      rememberPassword: true,
      agreeToTerms: true,
      deviceInfo: null, // 存储设备信息
      userToken: '', // 存储用户token
      showLineSwitch: false, // 控制线路切换弹窗显示
      currentLineIndex: 0, // 当前选中的线路索引
      lines: [
        { url: 'http://api.43bt.com', ping: '12' },
        { url: 'http://api2.43bt.com', ping: '25' },
        { url: 'http://api3.43bt.com', ping: '36' },
        { url: 'http://api4.43bt.com', ping: '47' }
      ]
    }
  },
  created() {
    // 从localStorage获取user_token用于请求头
    this.userToken = localStorage.getItem('user_token') || ''

    // 初始化设备信息
    this.initDeviceInfo()

    // 从本地存储中恢复账号密码
    const savedPhone = localStorage.getItem('phone')
    const savedPassword = localStorage.getItem('password')

    if (savedPhone && savedPassword) {
      this.phone = savedPhone
      this.password = savedPassword
      this.rememberPassword = true
    }

    // 尝试获取保存的线路配置
    const savedLineIndex = localStorage.getItem('current_line_index')
    if (savedLineIndex !== null) {
      this.currentLineIndex = parseInt(savedLineIndex)
      // 设置当前线路
      this.applyCurrentLine()
    }

    // 测试所有线路的响应时间
    this.testAllLines()
  },
  methods: {
    // 初始化设备信息
    initDeviceInfo() {
      try {
        // 生成设备ID（使用时间戳+随机数）
        const deviceId = Date.now().toString() + Math.floor(Math.random() * 1000000).toString()

        // 获取用户代理信息
        const userAgent = navigator.userAgent

        // 简单的平台检测
        let platform = 'web'
        let model = 'Unknown'
        let system = 'Unknown'

        if (/Android/i.test(userAgent)) {
          platform = 'android'
          const androidMatch = userAgent.match(/Android\s([0-9\.]+)/)
          system = androidMatch ? `Android ${androidMatch[1]}` : 'Android'

          // 尝试获取设备型号
          const modelMatch = userAgent.match(/\(([^\)]+)\)/)
          if (modelMatch && modelMatch[1]) {
            const parts = modelMatch[1].split(';')
            model = parts[parts.length - 1].trim() || 'Android Device'
          } else {
            model = 'Android Device'
          }
        } else if (/iPhone|iPad|iPod/i.test(userAgent)) {
          platform = 'ios'
          const iosMatch = userAgent.match(/OS ([0-9_]+)/)
          system = iosMatch ? `iOS ${iosMatch[1].replace(/_/g, '.')}` : 'iOS'

          if (/iPhone/i.test(userAgent)) {
            model = 'iPhone'
          } else if (/iPad/i.test(userAgent)) {
            model = 'iPad'
          } else {
            model = 'iPod'
          }
        } else {
          platform = 'web'
          system = navigator.platform || 'Web Browser'
          model = 'Web Browser'
        }

        this.deviceInfo = {
          deviceId: deviceId,
          platform: platform,
          model: model,
          system: system
        }
      } catch (error) {
        console.error('初始化设备信息失败:', error)
        // 如果获取失败，使用默认值
        this.deviceInfo = {
          deviceId: Date.now().toString(),
          platform: 'web',
          model: 'Unknown',
          system: 'Unknown'
        }
      }
    },

    togglePasswordVisible() {
      this.passwordVisible = !this.passwordVisible
    },
    toggleRememberPassword() {
      this.rememberPassword = !this.rememberPassword
    },
    toggleAgreement() {
      this.agreeToTerms = !this.agreeToTerms
    },
    goToRegister() {
      this.$router.push('/register')
    },
    goToCustomerService() {
      this.$router.push('/customer-service')
    },
    viewTerms() {
      // 查看服务协议
      this.$router.push('/terms')
    },
    viewPrivacy() {
      // 查看隐私协议
      this.$router.push('/privacy')
    },
    // 测试所有线路的响应时间
    testAllLines() {
      this.lines.forEach((line, index) => {
        this.testLineSpeed(index)
      })
    },
    // 测试单个线路的响应时间
    testLineSpeed(index) {
      const line = this.lines[index]
      const startTime = Date.now()

      // 使用一个简单的HEAD请求来测试响应速度
      fetch(line.url, { method: 'HEAD' })
        .then(() => {
          const endTime = Date.now()
          const pingTime = endTime - startTime
          this.$set(this.lines, index, { ...line, ping: pingTime.toString() })
        })
        .catch(() => {
          this.$set(this.lines, index, { ...line, ping: '超时' })
        })
    },
    // 切换线路
    switchLine(index) {
      if (this.currentLineIndex === index) return

      this.currentLineIndex = index

      // 保存当前线路索引到localStorage
      localStorage.setItem('current_line_index', index.toString())

      // 应用当前线路
      this.applyCurrentLine()

      // 关闭弹窗
      this.showLineSwitch = false

      // 提示用户已切换线路
      this.$toast(`已切换至线路${index + 1}`)
    },
    // 应用当前选中的线路
    applyCurrentLine() {
      const currentLine = this.lines[this.currentLineIndex]
      // 设置axios全局baseURL
      this.$http.defaults.baseURL = currentLine.url
    },
    async login() {
      // 表单验证
      if (!this.phone) {
        this.$toast('请输入手机号')
        return
      }
      if (!/^1[3-9]\d{9}$/.test(this.phone)) {
        this.$toast('请输入正确的手机号')
        return
      }
      if (!this.password) {
        this.$toast('请输入密码')
        return
      }
      if (this.password.length < 6) {
        this.$toast('密码长度不能少于6位')
        return
      }
      if (!this.agreeToTerms) {
        this.$toast('请勾选同意协议')
        return
      }

      // 检查设备信息是否初始化
      if (!this.deviceInfo) {
        this.initDeviceInfo()
      }

      try {
        // 获取user_token用于请求头
        const userToken = localStorage.getItem('user_token') || ''

        // 构建请求数据
        const requestData = {
          mobile: this.phone,
          password: this.password,
          deviceId: this.deviceInfo.deviceId,
          platform: this.deviceInfo.platform,
          model: this.deviceInfo.model,
          system: this.deviceInfo.system
        }

        // 调用登录API
        const response = await this.$http.post('/login/doSubmit', requestData, {
          headers: {
            'authorization': userToken
          }
        })

        if (response.data.code === 200) {
          // 登录成功，user_token在data字段中
          const token = response.data.data

          if (!token) {
            this.$toast('登录响应数据异常')
            return
          }

          // 保存到本地存储
          localStorage.setItem('user_token', token)

          // 保存用户信息（如果需要）
          localStorage.setItem('user_info', JSON.stringify({
            mobile: this.phone,
            loginTime: new Date().toISOString()
          }))

          // 如果选择记住密码，则保存账号密码
          if (this.rememberPassword) {
            localStorage.setItem('phone', this.phone)
            localStorage.setItem('password', this.password)
          } else {
            localStorage.removeItem('phone')
            localStorage.removeItem('password')
          }

          // 显示成功提示
          this.$toast('登录成功')

          // 确保 localStorage 操作完成，然后跳转到首页
          this.$nextTick(() => {
            setTimeout(() => {
              this.$router.replace('/')
            }, 200)  // 减少延迟时间，但确保异步操作完成
          })

        } else {
          // 登录失败
          this.$toast(response.data.msg || '登录失败，请重试')
        }
      } catch (error) {
        // 处理API请求错误
        console.error('登录请求失败', error)

        if (error.response) {
          // 服务器返回了错误响应
          const message = error.response.data?.msg || error.response.data?.message || '登录失败'
          this.$toast(message)
        } else if (error.request) {
          // 网络请求失败
          this.$toast('网络连接失败，请检查网络后重试')
        } else {
          // 其他错误
          this.$toast('登录失败，请稍后重试')
        }
      }
    }
  }
}
</script>

<style scoped>
/* 基础样式设置 */
.login-page {
  min-height: 100vh;
  font-family: PingFang SC, Arial, sans-serif;
  position: relative;
  overflow: hidden;
  background: #FFFFFF;
}

/* 状态栏样式已删除 */

/* 背景部分样式 */
.background {
  position: relative;
  height: 300px;
}

.gradient-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #EACFD8 2.88%, #50B9F9 25.48%, #0474FC 38.94%, #BAB8E0 78%, #95BAEB 95.19%);
}

.logo-container {
  position: absolute;
  top: 35%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 2;
}

.logo {
  width: 76.58px;
  height: 76.58px;
  background: #FFFFFF;
  border-radius: 25px;
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05), inset 0px 0px 2px 0px rgba(0, 0, 0, 0.25);
  margin-bottom: 15px;
  object-fit: contain;
}

.brand-name {
  color: #FFFFFF;
  font-size: 24px;
  font-weight: 700;
  margin: 0;
  font-family: 'Alibaba PuHuiTi 2.0', sans-serif;
}

/* 登录表单容器 */
.login-container {
  position: absolute;
  top: 210px;
  left: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(6.7px);
  border-radius: 20px 20px 0 0;
  padding: 30px 38px;
  box-sizing: border-box;
  min-height: calc(100vh - 210px);
}

/* 登录标题样式 */
.login-title {
  font-family: 'Coda Caption', sans-serif;
  font-weight: 800;
  font-size: 20px;
  line-height: 100%;
  letter-spacing: 0%;
  color: #333333;
  margin-bottom: 20px;
}

.login-form {
  display: flex;
  flex-direction: column;
}

/* 输入框样式 */
.input-group {
  margin-bottom: 14px;
}

.input-box {
  position: relative;
  height: 52px;
  background: #FAFAFA;
  border-radius: 35px;
  display: flex;
  align-items: center;
  padding: 0 20px;
  margin-bottom: 15px;
}

.input-icon {
  width: 100%;
  height: 100%;
  margin-right: 15px;
  background-repeat: no-repeat;
  background-position: center;
}

.phone-icon {
  background-image: url('@/assets/phone.svg');
}

.lock-icon {
  background-image: url('@/assets/password.svg');
}

.input-box input {
  flex: 1;
  height: 100%;
  border: none;
  outline: none;
  background: transparent;
  font-size: 16px;
  color: #333;
}

.input-box input::placeholder {
  color: #BBBCC0;
}

.eye-icon {
  cursor: pointer;
}

/* 操作行样式 */
.action-row {
  display: flex;
  justify-content: space-between;
  margin: 14px 0;
  padding: 0 10px;
}

.remember-pwd {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.forget-pwd {
  color: #0474FC;
  font-size: 12px;
  cursor: pointer;
  margin-right: 5px;
}

/* 复选框样式 */
.checkbox {
  width: 16px;
  height: 16px;
  border: 1px solid #999;
  border-radius: 50%;
  margin-right: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.checkbox.checked {
  background: #0474FC;
  border-color: #0474FC;
}

.checkbox-inner {
  width: 10px;
  height: 10px;
  background: #FFFFFF;
  border-radius: 50%;
}

.remember-pwd span {
  font-size: 12px;
  color: #BBBCC0;
}

/* 登录按钮样式 */
.login-btn {
  height: 44px;
  background: #0094FF;
  border-radius: 35px;
  border: none;
  color: #FFFFFF;
  font-size: 20px;
  font-weight: 800;
  cursor: pointer;
  margin-top: 30px;
  font-family: 'Coda Caption', sans-serif;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-btn:active {
  opacity: 0.9;
}

/* 协议确认样式 */
.agreement {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 15px;
  cursor: pointer;
}

.agreement-text {
  font-size: 12px;
  color: #333333;
}

/* 注册链接样式 */
.register-link {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 25px;
  margin-bottom: 10px;
  cursor: pointer;
}

.register-link span {
  color: #0474FC;
  font-size: 14px;
  margin-right: 5px;
}

.arrow-icon {
  width: 16px;
  height: 12px;
}

/* 线路切换按钮样式 */
.line-switch-btn {
  position: absolute;
  top: 16px;
  right: 16px;
  padding: 6px 12px;
  background-color: rgba(0, 116, 252, 0.7);
  color: white;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  z-index: 10;
}

/* 线路切换弹窗样式 */
.line-switch-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
}

.line-switch-content {
  width: 80%;
  max-width: 300px;
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.line-switch-title {
  padding: 15px;
  background-color: #f8f8f8;
  text-align: center;
  font-size: 16px;
  font-weight: bold;
  border-bottom: 1px solid #eee;
}

.line-list {
  padding: 0;
}

.line-item {
  padding: 15px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  cursor: pointer;
}

.line-item:last-child {
  border-bottom: none;
}

.line-item.active {
  background-color: #EFF8FF;
  color: #0474FC;
}

.ping-value {
  color: #666;
}

.line-close-btn {
  padding: 15px;
  text-align: center;
  background-color: #0474FC;
  color: white;
  font-weight: bold;
  cursor: pointer;
}
</style>
