<template>
  <div class="project-detail-ok"> <!-- 遮罩层已不需要，弹窗组件内部已包含遮罩层 -->

    <!-- 头部导航 -->
    <div class="header">
      <div class="back-icon" @click="goBack">
        <img src="@/assets/images/project-order/back-icon.svg" @error="handleImageError($event, 'icon')" alt="返回">
      </div>
      <div class="title">认购订单</div>
      <div class="right-placeholder"></div>
    </div>

    <!-- 顶部蓝色渐变背景 -->
    <div class="top-gradient"></div>

    <!-- 项目信息卡片 -->
    <div class="project-card">
      <div class="project-card-content">
        <!-- 圆形模糊背景 -->
        <div class="blur-circles">
          <div class="blur-circle-1"></div>
          <div class="blur-circle-2"></div>
        </div>

        <!-- 项目标题 -->
        <div class="project-title-section">
          <div class="project-tag">{{ project.title }}</div>
        </div> <!-- 项目标签 -->
        <div class="project-labels">
          <div class="label return-frequency">{{ project.returnFrequency }}</div>
          <div class="label period">{{ project.period }}</div>
          <div class="label yield">{{ project.yield }} 收益</div>
        </div> <!-- 认购金额及操作区域 -->
        <div class="amount-section">
          <div class="amount-label">认购金额</div>
          <div class="amount-row">
            <div class="control-btn minus" @click="decreaseAmount">
              <span>-</span>
            </div>
            <div class="amount-input">{{ formatAmount(orderAmount) }}</div>
            <div class="control-btn plus" @click="increaseAmount">
              <span>+</span>
            </div>
          </div>
        </div>

        <!-- 认购金额 -->
        <div class="amount-info">
          <div class="amount-title">认购金额：</div>
          <div class="amount-description">项目收益到期回款</div>
        </div>
      </div>
    </div> <!-- 加息券选择卡片 -->
    <div class="voucher-card">
      <div class="voucher-item" @click="toggleInterestVouchers($event)">
        <div class="voucher-title">加息券</div>
        <div class="voucher-value">
          {{ vouchers.interestVoucher ? vouchers.interestVoucher.title : (interestVouchersList.length > 0 ? "请选择加息券" :
            "暂无可用加息券") }}
        </div>
        <div class="voucher-arrow" :class="{ 'arrow-rotate': showInterestVouchers }"
          v-if="interestVouchersList.length > 0">
          <img src="@/assets/images/project-order/arrow-right.svg" @error="handleImageError($event, 'icon')" alt="选择">
        </div>
      </div>
    </div> <!-- 现金券卡片 -->
    <div class="voucher-card">
      <div class="voucher-item" @click="toggleCashVouchers($event)">
        <div class="voucher-title">现金券</div>
        <div class="voucher-value">
          {{ vouchers.cashVoucher ? vouchers.cashVoucher.title : (cashVouchersList.length > 0 ? "请选择现金券" : "暂无可用现金券") }}
        </div>
        <div class="voucher-arrow" :class="{ 'arrow-rotate': showCashVouchers }" v-if="cashVouchersList.length > 0">
          <img src="@/assets/images/project-order/arrow-right.svg" @error="handleImageError($event, 'icon')" alt="选择">
        </div>
      </div>
    </div>

    <!-- 底部确认按钮 -->
    <div class="purchase-button" @click="confirmPurchase">
      <div class="button-text">确认</div>
    </div>

    <!-- 加载中遮罩 -->
    <div class="loading-overlay" v-if="loadingOverlay">
      <div class="loading-spinner"></div>
      <div class="loading-text">加载中..</div>
    </div>

    <!-- 密码输入弹窗 -->
    <div class="password-modal" v-if="showPasswordModal">
      <div class="password-content">
        <div class="password-title">资金密码</div>
        <div class="password-input-container">
          <input type="password" v-model="fundPassword" placeholder="●●●●●●" class="password-input" />
        </div>
        <div class="password-buttons">
          <div class="cancel-button" @click="closePasswordModal">取消</div>
          <div class="confirm-button" @click="submitPurchase" :class="{ 'disabled': isSubmitting }">
            {{ isSubmitting ? '提交中..' : '确认付款' }}
          </div>
        </div>
      </div>
    </div> <!-- 加息券弹窗 -->
    <div class="voucher-popup" v-if="showInterestVouchers" @click.self="closeAllDropdowns">
      <div class="voucher-popup-overlay" @click="closeAllDropdowns"></div>
      <div class="voucher-popup-content">
        <div class="voucher-popup-header">
          <div class="voucher-popup-title">选择加息券</div>
          <div class="voucher-popup-close" @click="closeAllDropdowns">×</div>
        </div>
        <div class="voucher-popup-list">
          <div v-for="voucher in interestVouchersList" :key="voucher.id" class="voucher-popup-item"
            :class="{ 'active': vouchers.interestVoucher && vouchers.interestVoucher.id === voucher.id }"
            @click="selectInterestVoucher(voucher, $event)">
            <div class="voucher-popup-info">
              <div class="voucher-popup-name">{{ voucher.title }}</div>
              <div class="voucher-popup-desc" v-if="voucher.description">{{ voucher.description }}</div>
              <div class="voucher-popup-time" v-if="voucher.validTime">{{ voucher.validTime }}</div>
            </div>
            <div class="voucher-popup-check"
              v-if="vouchers.interestVoucher && vouchers.interestVoucher.id === voucher.id">
              <span class="check-icon">✓</span>
            </div>
          </div>
          <div class="voucher-popup-empty" v-if="interestVouchersList.length === 0">
            暂无可用加息券
          </div>
        </div>
      </div>
    </div> <!-- 现金券弹窗 -->
    <div class="voucher-popup" v-if="showCashVouchers" @click.self="closeAllDropdowns">
      <div class="voucher-popup-overlay" @click="closeAllDropdowns"></div>
      <div class="voucher-popup-content">
        <div class="voucher-popup-header">
          <div class="voucher-popup-title">选择现金券</div>
          <div class="voucher-popup-close" @click="closeAllDropdowns">×</div>
        </div>
        <div class="voucher-popup-list">
          <div v-for="voucher in cashVouchersList" :key="voucher.id" class="voucher-popup-item"
            :class="{ 'active': vouchers.cashVoucher && vouchers.cashVoucher.id === voucher.id }"
            @click="selectCashVoucher(voucher, $event)">
            <div class="voucher-popup-info">
              <div class="voucher-popup-name">{{ voucher.title }}</div>
              <div class="voucher-popup-desc" v-if="voucher.description">{{ voucher.description }}</div>
              <div class="voucher-popup-time" v-if="voucher.validTime">{{ voucher.validTime }}</div>
            </div>
            <div class="voucher-popup-check" v-if="vouchers.cashVoucher && vouchers.cashVoucher.id === voucher.id">
              <span class="check-icon">✓</span>
            </div>
          </div>
          <div class="voucher-popup-empty" v-if="cashVouchersList.length === 0">
            暂无可用现金券
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ImageErrorHandler from '@/utils/ImageErrorHandler'

export default {
  name: "ProjectDetailOk",
  data() {
    return {
      projectId: null,
      loading: false,
      loadingOverlay: false,
      userBalance: 0, // 用户余额
      project: {
        id: 0,
        title: "",
        pic: "",
        yield: "",
        returnType: "",
        returnFrequency: "",
        period: "",
        periodValue: 0,
        minAmount: 1000,
        maxAmount: 3000000,
        step: 1000,
        rateText: "" // 结算说明
      },
      orderAmount: 1000, // 订单金额
      amountStep: 100, // 金额调整步长
      vouchers: {
        interestVoucher: { id: 0, title: "不使用加息券", value: 0 }, // 默认选择不使用
        cashVoucher: { id: 0, title: "不使用现金券", value: 0 } // 默认选择不使用
      },
      showInterestVouchers: false,
      showCashVouchers: false, interestVouchersList: [], // 加息券列表
      cashVouchersList: [], // 现金券列表
      needPassword: false, // 是否需要密码

      // 密码弹窗相关
      showPasswordModal: false, // 是否显示密码弹窗
      fundPassword: "", // 资金密码
      isSubmitting: false // 是否正在提交
    }
  }, created() {
    // 获取从ProjectDetail传递过来的参数
    this.projectId = this.$route.query.id;
    const initialAmount = parseFloat(this.$route.query.amount) || 1000;

    if (this.projectId) {
      this.orderAmount = initialAmount;
      this.fetchProjectBuyInfo();
    } else {
      this.$toast("项目ID不存在");
      this.goBack();
    }
    // 添加全局点击事件，点击其他区域关闭下拉列表
    document.addEventListener("click", this.handleGlobalClick);
  },
  beforeDestroy() {
    // 组件销毁时移除事件监听
    document.removeEventListener("click", this.handleGlobalClick);

    // 确保组件销毁时恢复页面滚动
    if (this.showInterestVouchers || this.showCashVouchers) {
      this.resetBodyScroll();
    }
  }, methods: {
    // 处理图片加载错误
    handleImageError(event, type) {
      ImageErrorHandler.handleImageError(event, type)
    },

    goBack() {
      this.$router.go(-1)
    },
    // 获取项目认购信息
    fetchProjectBuyInfo() {
      this.loadingOverlay = true;

      // 获取token
      const userToken = localStorage.getItem('user_token');
      if (!userToken) {
        this.$toast('请先登录');
        // 使用 $nextTick 确保 toast 显示后再跳转
        this.$nextTick(() => {
          this.$router.replace('/login');
        })
        this.loadingOverlay = false;
        return;
      }

      // 发起API请求
      this.$http.get(`/product/getBuy?id=${this.projectId}`, {
        headers: {
          'authorization': userToken
        }
      })
        .then(response => {
          if (response.data.code === 200) {
            const data = response.data.data;
            const info = data.info;

            // 更新用户余额
            this.userBalance = parseFloat(data.balance);
            // 更新项目信息
            this.project = {
              id: info.id,
              title: info.name,
              pic: info.pic || '',
              yield: info.rate + '%',
              returnType: '到期返本',
              returnFrequency: '每' + info.cycle + info.cycle_type + '返息',
              period: info.cycle + info.cycle_type + '期限',
              periodValue: parseInt(info.cycle) || 10,
              minAmount: parseFloat(info.min_amount),
              maxAmount: parseFloat(info.max_amount),
              step: 100,
              rateText: info.rate_text || '项目收益到期回款'
            };

            // 设置金额调整步长为最小购买金额
            this.amountStep = parseFloat(info.min_amount);

            // 确保订单金额在范围内
            this.orderAmount = Math.max(this.project.minAmount, Math.min(this.orderAmount, this.project.maxAmount));

            // 是否需要密码
            this.needPassword = data.password;

            // 处理加息券列表
            this.interestVouchersList = data.rate_card || [];

            // 处理现金券列表
            this.cashVouchersList = data.cash_card || [];

            // 设置默认值
            if (this.interestVouchersList.length > 0) {
              // 默认选择第一项（不使用加息券）
              this.vouchers.interestVoucher = this.interestVouchersList[0];
            }

            if (this.cashVouchersList.length > 0) {
              // 默认选择第一项（不使用现金券）
              this.vouchers.cashVoucher = this.cashVouchersList[0];
            }

          } else {
            this.$toast(response.data.msg || '获取项目信息失败');
            setTimeout(() => {
              this.goBack();
            }, 1500);
          }

          this.loadingOverlay = false;
        })
        .catch(error => {
          console.error('获取项目信息失败:', error);
          this.$toast('网络异常，请稍后重试');
          this.loadingOverlay = false;
          setTimeout(() => {
            this.goBack();
          }, 1500);
        });
    },    // 增加订单金额
    increaseAmount() {
      const minAmount = this.project.minAmount;
      if (this.orderAmount + minAmount <= this.project.maxAmount) {
        this.orderAmount += minAmount;
      } else {
        this.$toast('已达到最大购买金额');
      }
    },
    // 减少订单金额
    decreaseAmount() {
      const minAmount = this.project.minAmount;
      if (this.orderAmount - minAmount >= minAmount) {
        this.orderAmount -= minAmount;
      } else {
        this.$toast('已达到最小购买金额');
      }
    }, confirmPurchase() {
      // 检查金额是否合理
      if (this.orderAmount < this.project.minAmount) {
        this.$toast(`认购金额不能小于${this.project.minAmount}元`);
        return;
      }

      if (this.orderAmount > this.project.maxAmount) {
        this.$toast(`认购金额不能大于${this.project.maxAmount}元`);
        return;
      }

      if (this.orderAmount > this.userBalance) {
        this.$toast('余额不足，请先充值');
        setTimeout(() => {
          this.$router.push('/recharge');
        }, 1500);
        return;
      }
      // 弹出密码输入框
      this.fundPassword = ''; // 清空之前的密码输入
      this.showPasswordModal = true;
    },

    // 关闭密码弹窗
    closePasswordModal() {
      this.showPasswordModal = false;
      this.fundPassword = '';
    },

    // 提交订单
    submitPurchase() {
      // 验证密码不能为空
      if (!this.fundPassword) {
        this.$toast('请输入资金密码');
        return;
      }

      if (this.isSubmitting) {
        return; // 防止重复提交
      }

      this.isSubmitting = true;

      // 构建订单参数
      const orderData = {
        id: this.projectId,
        amount: this.orderAmount,
        number: 1, // 购买数量，默认为1
        password: this.fundPassword,
        card_rate_id: this.vouchers.interestVoucher.id,
        card_cash_id: this.vouchers.cashVoucher.id
      };

      // 获取token
      const userToken = localStorage.getItem('user_token');
      if (!userToken) {
        this.$toast('请先登录');
        // 使用 $nextTick 确保 toast 显示后再跳转
        this.$nextTick(() => {
          this.$router.replace('/login');
        })
        this.isSubmitting = false;
        return;
      }

      // 发起API请求
      this.$http.post('/product/doBuy', orderData, {
        headers: {
          'authorization': userToken
        }
      })
        .then(response => {
          if (response.data.code === 200) {
            this.$toast(response.data.msg || '认购成功');
            this.closePasswordModal(); // 关闭密码弹窗

            // 跳转到订单详情页
            setTimeout(() => {
              this.$router.push({
                path: '/project-order',
                query: {
                  id: this.project.id,
                  amount: this.orderAmount
                }
              });
            }, 1500);
          } else {
            this.$toast(response.data.msg || '认购失败');
          }

          this.isSubmitting = false;
        })
        .catch(error => {
          console.error('提交订单失败:', error);
          this.$toast('网络异常，请稍后重试');
          this.isSubmitting = false;
        });
    }, toggleInterestVouchers(event) {
      // 阻止事件冒泡
      if (event) {
        event.stopPropagation();
      }

      if (this.interestVouchersList.length > 0) {
        this.showInterestVouchers = !this.showInterestVouchers;

        if (this.showInterestVouchers) {
          this.showCashVouchers = false; // 关闭另一个弹窗
          // 防止页面滚动
          document.body.style.overflow = 'hidden';
          document.body.style.position = 'fixed';
          document.body.style.width = '100%';
          document.body.style.top = `-${window.scrollY}px`;
        } else {
          this.resetBodyScroll();
        }
      }
    },
    toggleCashVouchers(event) {
      // 阻止事件冒泡
      if (event) {
        event.stopPropagation();
      }

      if (this.cashVouchersList.length > 0) {
        this.showCashVouchers = !this.showCashVouchers;

        if (this.showCashVouchers) {
          this.showInterestVouchers = false; // 关闭另一个弹窗
          // 防止页面滚动
          document.body.style.overflow = 'hidden';
          document.body.style.position = 'fixed';
          document.body.style.width = '100%';
          document.body.style.top = `-${window.scrollY}px`;
        } else {
          this.resetBodyScroll();
        }
      }
    }, selectInterestVoucher(voucher, event) {
      // 阻止冒泡避免触发父元素点击事件
      if (event) {
        event.stopPropagation();
        event.preventDefault();
      }
      // 选择券并关闭弹窗
      this.vouchers.interestVoucher = { ...voucher };
      this.showInterestVouchers = false;

      // 恢复页面滚动
      this.resetBodyScroll();

      // 显示提示信息
      if (voucher.id !== 0) {
        this.$toast('已选择加息券: ' + voucher.title);
      }
    },

    selectCashVoucher(voucher, event) {
      // 阻止冒泡避免触发父元素点击事件
      if (event) {
        event.stopPropagation();
        event.preventDefault();
      }
      // 选择券并关闭弹窗
      this.vouchers.cashVoucher = { ...voucher };
      this.showCashVouchers = false;

      // 恢复页面滚动
      this.resetBodyScroll();

      // 显示提示信息
      if (voucher.id !== 0) {
        this.$toast('已选择现金券: ' + voucher.title);
      }
    },
    // 格式化金额显示
    formatAmount(amount) {
      if (!amount && amount !== 0) return '0';
      return parseInt(amount).toLocaleString();
    }, handleGlobalClick(event) {
      // 由于我们现在使用全屏弹窗，点击事件处理已经移到弹窗本身
      // 此方法保留以兼容其他功能
    }, resetBodyScroll() {
      // 恢复页面滚动状态
      const scrollY = document.body.style.top;
      document.body.style.overflow = '';
      document.body.style.position = '';
      document.body.style.width = '';
      document.body.style.top = '';
      if (scrollY) {
        window.scrollTo(0, parseInt(scrollY || '0') * -1);
      }
    },

    closeAllDropdowns() {
      if (this.showInterestVouchers || this.showCashVouchers) {
        this.showInterestVouchers = false;
        this.showCashVouchers = false;
        this.resetBodyScroll();
      }
    }
  }
}
</script>

<style scoped>
.project-detail-ok {
  min-height: 100vh;
  background-color: #F6F6F6;
  padding-bottom: 80px;
  position: relative;
  overflow: hidden;
}

/* 头部导航 */
.header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  padding: 0 16px;
  z-index: 10;
  background: #FFFFFF;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.back-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.back-icon img {
  width: 10px;
  height: 16px;
}

.title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 17px;
  font-weight: 500;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  color: #111827;
}

.right-placeholder {
  width: 24px;
  height: 24px;
}

/* 顶部蓝色渐变背景 */
.top-gradient {
  position: absolute;
  top: 44px;
  left: 0;
  width: 100%;
  height: 202px;
  background: linear-gradient(to right, #EACFD8, #50B9F9, #0474FC, #BAB8E0, #95BAEB);
  z-index: 1;
}

/* 项目信息卡片 */
.project-card {
  position: relative;
  margin: 82px 16px 0;
  z-index: 3;
}

.project-card-content {
  background-color: white;
  border-radius: 10px;
  padding: 24px;
  position: relative;
  overflow: hidden;
  min-height: 182px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

/* 圆形模糊背景 */
.blur-circles {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  overflow: hidden;
  z-index: 0;
}

.blur-circle-1 {
  position: absolute;
  width: 148px;
  height: 140px;
  left: -32px;
  top: -74px;
  background-color: #FEEEEE;
  border-radius: 50%;
  filter: blur(29px);
}

.blur-circle-2 {
  position: absolute;
  width: 254px;
  height: 122px;
  right: -22px;
  top: -57px;
  background-color: #E8EFFB;
  border-radius: 50%;
  filter: blur(20px);
}

/* 项目标题区域 */
.project-title-section {
  position: relative;
  z-index: 1;
  margin-bottom: 15px;
  text-align: center;
}

.project-tag {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* 项目标签 */
.project-labels {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
  position: relative;
  z-index: 1;
}

.label {
  border-radius: 4px;
  padding: 3px 10px;
  margin: 0 4px;
  font-size: 12px;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.label.return-frequency {
  background-color: #FFFAEE;
  color: #EE8300;
}

.label.period {
  background-color: #E8EFFB;
  color: #0094FF;
}

.label.yield {
  background-color: #FC0405;
  color: #FFFFFF;
  font-family: 'DIN Black', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 14px;
  font-weight: 900;
}

/* 认购金额及操作区域 */
.amount-section {
  position: relative;
  z-index: 1;
  margin-bottom: 15px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
}

.amount-label {
  font-size: 14px;
  color: #666;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 400;
}

/* 加减按钮控制区 */
.amount-row {
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  border-radius: 4px;
}

.control-btn {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  user-select: none;
  font-size: 18px;
}

.control-btn.minus {
  background-color: #0474FC;
  border-radius: 4px 0 0 4px;
  color: white;
}

.control-btn.plus {
  background-color: #0474FC;
  border-radius: 0 4px 4px 0;
  color: white;
}

.amount-input {
  width: 80px;
  height: 28px;
  background-color: #F7F7F7;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #FC0405;
  font-weight: 500;
  font-family: 'DIN', 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* 金额信息 */
.amount-info {
  display: flex;
  align-items: center;
  margin-top: 20px;
  position: relative;
  z-index: 1;
  color: #0474FC;
}

.amount-title {
  font-size: 14px;
  font-weight: 400;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
}

.amount-description {
  font-size: 14px;
  font-weight: 400;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* 券选择卡片 */
.voucher-card {
  margin: 12px 16px 0;
  background-color: white;
  border-radius: 10px;
  position: relative;
  z-index: 3;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  overflow: visible;
}

.voucher-item {
  padding: 18px 20px;
  display: flex;
  align-items: center;
  cursor: pointer;
  border-radius: 10px;
  transition: background-color 0.2s;
}

.voucher-item:active {
  background-color: #F5F9FF;
  transform: scale(0.98);
  transition: all 0.15s ease;
}

.voucher-item {
  position: relative;
}

.voucher-title {
  font-size: 16px;
  color: #333;
  font-weight: 600;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  min-width: 65px;
}

.voucher-value {
  flex: 1;
  font-size: 15px;
  color: #0474FC;
  text-align: right;
  margin-right: 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  transition: color 0.2s;
  font-weight: 500;
}

.voucher-arrow {
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
}

.voucher-arrow img {
  width: 8px;
  height: 12px;
}

.arrow-rotate {
  transform: rotate(90deg);
}

.voucher-item:hover .voucher-value {
  color: #0474FC;
}

/* 券下拉列表 */
.voucher-dropdown {
  position: absolute;
  width: 100%;
  max-height: 210px;
  background-color: white;
  border-radius: 0 0 10px 10px;
  overflow-y: auto;
  z-index: 101;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
  margin-top: 0;
  left: 0;
  right: 0;
  scrollbar-width: thin;
  scrollbar-color: #C1D5F0 #F5F9FF;
  border: 1px solid #E0E7F0;
  border-top: none;
}

.voucher-dropdown::-webkit-scrollbar {
  width: 6px;
}

.voucher-dropdown::-webkit-scrollbar-track {
  background: #F5F9FF;
  border-radius: 3px;
}

.voucher-dropdown::-webkit-scrollbar-thumb {
  background-color: #C1D5F0;
  border-radius: 3px;
}

.voucher-dropdown::-webkit-scrollbar-thumb:hover {
  background-color: #A8C4E8;
}

.voucher-list {
  padding: 10px;
  background-color: #FFFFFF;
  position: relative;
  z-index: 102;
}

.voucher-list-item {
  padding: 15px 12px;
  border-bottom: 1px solid #EAEAEA;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 6px;
  margin-bottom: 6px;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

.voucher-list-item:active {
  background-color: #E6F1FF;
  transform: scale(0.98);
}

.voucher-list-item:hover {
  background-color: #F5F9FF;
}

/* 选中项样式 */
.voucher-item-selected {
  background-color: #EBF5FF;
  border-left: 3px solid #0474FC;
  padding-left: 9px;
}

.voucher-list-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.voucher-info {
  flex: 1;
}

.voucher-name {
  font-size: 16px;
  color: #333;
  font-weight: 500;
  font-family: 'PingFang SC', 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  margin-bottom: 4px;
  text-shadow: 0 0.5px 0 rgba(0, 0, 0, 0.05);
}

.voucher-desc {
  font-size: 13px;
  color: #666;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  margin-bottom: 3px;
  line-height: 1.3;
}

.voucher-time {
  font-size: 12px;
  color: #999;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  line-height: 1.3;
}

.voucher-check {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #FFFFFF;
  background-color: #0474FC;
  border-radius: 50%;
  margin-left: 10px;
  box-shadow: 0 2px 4px rgba(4, 116, 252, 0.3);
  flex-shrink: 0;
}

.check-icon {
  font-size: 14px;
  font-weight: bold;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
}

/* 遮罩层已集成到弹窗组件中 */

/* 下拉动画 */
.dropdown-enter-active,
.dropdown-leave-active {
  transition: all 0.22s cubic-bezier(0.4, 0.0, 0.2, 1);
  transform-origin: top center;
}

.dropdown-enter,
.dropdown-leave-to {
  transform: scaleY(0.95);
  opacity: 0;
}

/* 使下拉列表容器具有更高的层叠序列和更明确的位置 */
.voucher-card {
  isolation: isolate;
}

/* 确认按钮 */
.purchase-button {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 73px;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.button-text {
  width: 355px;
  height: 47px;
  background-color: #0170FD;
  border-radius: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 15px;
  font-weight: 400;
  color: #F8F9FA;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* 加载中遮罩 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(4, 116, 252, 0.1);
  border-top: 3px solid #0474FC;
  border-radius: 50%;
  animation: spin 1s cubic-bezier(0.55, 0.25, 0.25, 0.7) infinite;
  margin-bottom: 15px;
}

.loading-text {
  font-size: 14px;
  color: #0474FC;
  font-weight: 500;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* 旋转动画 */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* 密码输入弹窗 */
.password-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.password-content {
  width: 80%;
  max-width: 320px;
  background: #FFFFFF;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.password-title {
  text-align: center;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 20px;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
}

.password-input-container {
  margin-bottom: 20px;
}

.password-input {
  width: 100%;
  height: 40px;
  border: 1px solid #E0E0E0;
  border-radius: 5px;
  padding: 0 12px;
  font-size: 16px;
  color: #333;
  text-align: center;
  letter-spacing: 5px;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.password-buttons {
  display: flex;
  justify-content: space-between;
}

.cancel-button {
  flex: 1;
  height: 40px;
  line-height: 40px;
  text-align: center;
  color: #666;
  background-color: #F7F7F7;
  border-radius: 20px;
  margin-right: 10px;
  font-size: 15px;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
}

.confirm-button {
  flex: 1;
  height: 40px;
  line-height: 40px;
  text-align: center;
  color: white;
  background-color: #0170FD;
  border-radius: 20px;
  font-size: 15px;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
}

.confirm-button.disabled {
  opacity: 0.7;
  pointer-events: none;
}

/* 密码弹窗关闭按钮 */
.close-button {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.close-button img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 箭头旋转效果 */
.arrow-rotate {
  transform: rotate(90deg);
}

/* 为列表项添加点击波纹效果 */
@keyframes ripple {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }

  100% {
    transform: scale(2);
    opacity: 0;
  }
}

.voucher-list-item::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  background-color: rgba(4, 116, 252, 0.1);
  border-radius: 6px;
  transform: translate(-50%, -50%) scale(0);
  opacity: 0;
  pointer-events: none;
}

.voucher-list-item:active::after {
  animation: ripple 0.4s ease-out;
}

/* 弹窗样式 */
.voucher-popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.voucher-popup-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.65);
  backdrop-filter: blur(3px);
  animation: fade-in 0.3s ease-out;
}

@keyframes fade-in {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.voucher-popup-content {
  position: relative;
  width: 85%;
  max-height: 82%;
  background-color: #FFFFFF;
  border-radius: 16px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.25);
  z-index: 1001;
  animation: popup-enter 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

@keyframes popup-enter {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(20px);
  }

  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.voucher-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 18px 20px;
  border-bottom: 1px solid #EAEAEA;
  background: linear-gradient(to right, #F5F9FF, #FFFFFF);
}

.voucher-popup-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.8);
}

.voucher-popup-close {
  font-size: 24px;
  color: #666;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
}

.voucher-popup-close:active {
  background-color: rgba(0, 0, 0, 0.05);
  transform: scale(0.95);
}

.voucher-popup-list {
  flex: 1;
  overflow-y: auto;
  padding: 14px 20px;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: thin;
  scrollbar-color: #C1D5F0 #F5F9FF;
}

.voucher-popup-list::-webkit-scrollbar {
  width: 6px;
}

.voucher-popup-list::-webkit-scrollbar-track {
  background: #F5F9FF;
  border-radius: 3px;
}

.voucher-popup-list::-webkit-scrollbar-thumb {
  background-color: #C1D5F0;
  border-radius: 3px;
}

.voucher-popup-list::-webkit-scrollbar-thumb:hover {
  background-color: #A8C4E8;
}

.voucher-popup-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 150px;
  color: #999;
  font-size: 16px;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
}

.voucher-popup-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 15px;
  border-bottom: 1px solid #EAEAEA;
  position: relative;
  border-radius: 10px;
  margin-bottom: 6px;
  transition: all 0.2s ease;
}

.voucher-popup-item:last-child {
  border-bottom: none;
}

.voucher-popup-item.active {
  background-color: #E8F1FE;
  border-left: 4px solid #0474FC;
  padding-left: 12px;
  box-shadow: 0 2px 6px rgba(4, 116, 252, 0.1);
}

.voucher-popup-info {
  flex: 1;
}

.voucher-popup-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 6px;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
}

.voucher-popup-desc {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  line-height: 1.4;
}

.voucher-popup-time {
  font-size: 12px;
  color: #999;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
}

.voucher-popup-check {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #0474FC;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 2px 6px rgba(4, 116, 252, 0.3);
  margin-left: 12px;
  animation: check-appear 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

@keyframes check-appear {
  from {
    opacity: 0;
    transform: scale(0.5) rotate(-45deg);
  }

  to {
    opacity: 1;
    transform: scale(1) rotate(0);
  }
}

.check-icon {
  font-size: 14px;
  font-weight: bold;
}

/* 触摸反馈效果 */
.voucher-popup-item:active {
  background-color: #E8F1FF;
  transform: scale(0.99);
}
</style>
