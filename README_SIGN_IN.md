# 签到抽奖页面说明

## 功能特性

### 🎯 核心功能
- **幸运转盘**: 8个奖品扇形区域，包含不同积分和"再接再厉"
- **智能概率**: 采用权重算法，高价值奖品概率较低
- **每日限制**: 每人每天只能抽奖一次，防止重复抽奖
- **奖品记录**: 自动记录中奖历史，本地存储持久化

### 🎨 视觉设计
- **响应式布局**: 适配不同屏幕尺寸（375px, 320px等）
- **动态效果**: 转盘旋转动画，发光阴影效果
- **状态反馈**: Toast提示、弹窗确认
- **美观界面**: 渐变背景，圆润卡片设计

### 📱 用户体验
- **操作简单**: 点击中央按钮即可开始抽奖
- **状态清晰**: 转盘旋转时禁用操作，防误触
- **结果明确**: 中奖后弹窗显示，奖品自动记录
- **历史查询**: 可查看"我的奖品"和中奖记录

## 奖品设置

| 奖品 | 权重 | 概率 |
|------|------|------|
| 500积分 | 1 | ~0.8% |
| 100积分 | 5 | ~3.8% |
| 50积分 | 10 | ~7.6% |
| 10积分 | 20 | ~15.2% |
| 5积分 | 15 | ~11.4% |
| 3积分 | 25 | ~19.0% |
| 1积分 | 25 | ~19.0% |
| 再接再厉 | 30 | ~22.8% |

## 技术实现

### 前端技术栈
- **Vue 2.7.16**: 主框架
- **Vant UI**: 移动端组件库
- **CSS3**: 转盘动画、渐变效果
- **Local Storage**: 本地数据持久化

### 核心算法
```javascript
// 权重随机算法
const totalWeight = prizes.reduce((sum, prize) => sum + prize.weight, 0)
let random = Math.random() * totalWeight
for (const prize of prizes) {
  random -= prize.weight
  if (random <= 0) {
    selectedPrize = prize
    break
  }
}
```

### 转盘动画
```css
.wheel {
  transition: transform 3s cubic-bezier(0.23, 1, 0.32, 1);
  background: conic-gradient(/* 8个扇形颜色 */);
}
```

## 路由配置

访问路径: `/sign-in`

首页快捷入口: 点击"签到抽奖"卡片

## 数据存储

### LocalStorage 键值
- `lastSpinDate`: 最后抽奖日期（用于每日限制）
- `myPrizes`: 我的奖品列表（JSON格式）

### 数据格式
```javascript
// 奖品记录格式
{
  name: "50积分",
  time: "2025-05-27 10:30", 
  status: "已到账"
}
```

## 部署说明

1. **开发环境启动**
   ```bash
   cd d:\work\work\shanzhili\h5code
   npm run serve
   ```

2. **生产环境构建**
   ```bash
   npm run build
   ```

3. **访问地址**
   - 本地: http://localhost:8082/#/sign-in
   - 网络: http://************:8082/#/sign-in

## 注意事项

⚠️ **重要提醒**
- 抽奖记录存储在浏览器本地，清除浏览器数据会丢失记录
- 每日抽奖限制基于本地时间，更换设备可重新抽奖
- 图片资源需确保路径正确：`src/assets/images/sign-in/`

## 后续优化建议

### 功能扩展
- [ ] 接入后端API，实现真实的积分系统
- [ ] 添加音效和震动反馈
- [ ] 增加分享功能和邀请奖励
- [ ] 添加VIP用户额外抽奖次数

### 性能优化
- [ ] 图片懒加载和压缩
- [ ] 转盘用Canvas重绘，提升性能
- [ ] 增加PWA支持，离线可用

### 用户体验
- [ ] 添加新手引导动画
- [ ] 优化loading状态显示
- [ ] 增加手势滑动交互
