<template>
  <div class="team-page">
    <!-- 头部 -->
    <div class="header">
      <div class="back-icon" @click="goBack">
        <img src="@/assets/icons/back-icon.svg" alt="返回">
      </div>
      <div class="title">我的团队</div>
      <div class="right-icon"></div>
    </div>

    <!-- 固定区域容器 -->
    <div class="sticky-container" :class="{ 'is-sticky': isSticky }">
      <!-- 团队人数 -->
      <div class="team-members-count">
        <img src="@/assets/images/team/team-icon.svg" alt="团队" class="team-icon">
        <span class="count-label">团队人数</span>
        <span class="count-value">{{ teamInfo.total_num || '0' }}</span>
      </div>

      <!-- 团队信息统计卡片 -->
      <div class="team-stats-card">
        <div class="stats-area">
          <div class="level-stats-row">
            <div class="level-stat">
              <div class="level-label">一级有效人数</div>
              <div class="level-value">{{ teamInfo.valid_1 || '0' }}</div>
            </div>
            <div class="level-stat">
              <div class="level-label">二级有效人数</div>
              <div class="level-value">{{ teamInfo.valid_2 || '0' }}</div>
            </div>
            <div class="level-stat">
              <div class="level-label">三级有效人数</div>
              <div class="level-value">{{ teamInfo.valid_3 || '0' }}</div>
            </div>
          </div>

          <div class="income-stats-row">
            <div class="income-stat">
              <div class="income-value">{{ formatAmount(teamInfo.investment_amount) || '0' }}</div>
              <div class="income-label">个人总收益</div>
            </div>
            <div class="income-stat">
              <div class="income-value">{{ formatAmount(teamInfo.income_amount) || '0' }}</div>
              <div class="income-label">团队总收益</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 三级切换标签 -->
      <div class="level-tabs">
        <div class="tab-item" :class="{ active: activeTab === 1 }" @click="changeTab(1)">
          <span>一级</span>
          <div class="tab-indicator" v-if="activeTab === 1"></div>
        </div>
        <div class="tab-item" :class="{ active: activeTab === 2 }" @click="changeTab(2)">
          <span>二级</span>
          <div class="tab-indicator" v-if="activeTab === 2"></div>
        </div>
        <div class="tab-item" :class="{ active: activeTab === 3 }" @click="changeTab(3)">
          <span>三级</span>
          <div class="tab-indicator" v-if="activeTab === 3"></div>
        </div>
      </div>
    </div>

    <!-- 占位元素，用于防止内容跳动 -->
    <div class="sticky-placeholder" v-if="isSticky" :style="{ height: stickyHeight + 'px' }"></div>

    <!-- 成员列表 -->
    <div class="members-container">
      <div class="member-card" v-for="(member, index) in currentLevelMembers" :key="member.id || index">
        <!-- 用户头像和基本信息 -->
        <div class="member-avatar">
          <img :src="member.avatar || require('@/assets/user-avatar.png')" alt="成员头像"
            @error="handleImageError($event, 'avatar')" />
        </div>

        <div class="member-info">
          <div class="member-basic">
            <div class="member-name">{{ member.nickname || 'User5835291(1级)' }}</div>
            <div class="member-phone">{{ member.mobile || '138*****123' }}</div>
          </div>

          <!-- 会员等级标签 -->
          <div class="level-tag">
            <div class="level-tag-inner">
              <img :src="getLevelIcon(member.level)" @error="handleImageError($event, 'level-icon')" alt="会员等级">
            </div>
          </div>
        </div>

        <!-- 分隔线 -->
        <div class="member-divider"></div>

        <!-- 数据统计 -->
        <div class="member-stats">
          <div class="stat-col">
            <div class="stat-value">{{ formatAmount(member.deposit_amount) || '0.00' }}</div>
            <div class="stat-label">充值</div>
          </div>
          <div class="stat-col">
            <div class="stat-value">{{ formatAmount(member.withdraw_amount) || '0.00' }}</div>
            <div class="stat-label">提现</div>
          </div>
          <div class="stat-col">
            <div class="stat-value">{{ formatAmount(member.investment_amount) || '0.00' }}</div>
            <div class="stat-label">投资</div>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div class="loading-state" v-if="loading">
        <div class="loading-spinner"></div>
        <div class="loading-text">加载中...</div>
      </div>

      <!-- 空状态 -->
      <div class="empty-state" v-if="currentLevelMembers.length === 0 && !loading">
        <div class="empty-icon">
          <img src="@/assets/images/team/empty-team.svg" alt="暂无成员" @error="handleImageError($event, 'empty-icon')" />
        </div>
        <div class="empty-text">暂无{{ activeTab === 1 ? '一' : activeTab === 2 ? '二' : '三' }}级成员</div>
        <div class="empty-tip">您可以邀请更多好友加入团队</div>
      </div>

      <!-- 没有更多数据 -->
      <div class="no-more-state" v-if="noMore && currentLevelMembers.length > 0">
        <div class="no-more-text">已加载全部数据</div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import ImageErrorHandler from '@/utils/ImageErrorHandler'

export default {
  name: 'Team',
  data() {
    return {
      // 团队信息
      teamInfo: {
        investment_amount: 0,
        income_amount: 0,
        level_name: 'VIP 0',
        valid_1: 0,
        valid_2: 0,
        valid_3: 0,
        total_num: 14
      },
      // 成员列表数据
      memberList: [],
      // 当前选中的级别
      activeTab: 1,
      // 分页信息
      page: 1,
      // 加载状态
      loading: false,
      // 是否没有更多数据
      noMore: false,
      // 滚动节流定时器
      scrollTimeout: null,
      // 控制吸顶效果
      isSticky: false,
      // 吸顶容器的高度
      stickyHeight: 0,
      // 吸顶触发的滚动位置
      stickyTriggerPosition: 0
    }
  },
  computed: {
    // 当前级别的成员列表
    currentLevelMembers() {
      if (!this.memberList || this.memberList.length === 0) return []

      return this.memberList.filter(member => {
        return member.find === this.activeTab
      })
    }
  },
  mounted() {
    this.initPage()
    this.setupScrollListener()
    this.initStickyElements()
    this.setupResizeListener()
  },
  beforeDestroy() {
    this.cleanupScrollListener()
    this.cleanupResizeListener()
  },
  methods: {
    // 初始化页面
    async initPage() {
      await this.getTeamInfo()
      await this.getTeamMembers()
    },

    // 获取团队信息
    async getTeamInfo() {
      try {
        const userToken = localStorage.getItem('user_token') || ''

        const response = await axios.get('/team/getInfo', {
          headers: {
            'authorization': userToken
          }
        })

        if (response.data.code === 200) {
          // 根据API数据结构更新团队信息
          this.teamInfo = {
            ...this.teamInfo,
            ...response.data.data
          }
        } else {
          this.showToast(response.data.msg || '获取团队信息失败')
        }
      } catch (error) {
        console.error('获取团队信息出错:', error)
        this.showToast('网络错误，请稍后重试')
      }
    },

    // 获取团队成员列表
    async getTeamMembers(isLoadMore = false) {
      if (this.loading) return

      try {
        this.loading = true
        const userToken = localStorage.getItem('user_token') || ''

        const response = await axios.get('/team/getList', {
          params: {
            page: this.page
          },
          headers: {
            'authorization': userToken
          }
        })

        if (response.data.code === 200) {
          const newMembers = response.data.data.list || []

          if (isLoadMore) {
            // 追加数据
            this.memberList = [...this.memberList, ...newMembers]
          } else {
            // 重新加载
            this.memberList = newMembers
          }

          // 判断是否还有更多数据
          this.noMore = newMembers.length === 0 || response.data.data.is_end === 1
        } else {
          this.showToast(response.data.msg || '获取成员列表失败')
        }
      } catch (error) {
        console.error('获取成员列表出错:', error)
        this.showToast('网络错误，请稍后重试')
      } finally {
        this.loading = false
      }
    },

    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },

    // 获取会员等级图标
    getLevelIcon(level) {
      try {
        // 我们知道目录中有SVG格式的等级图标
        return require(`@/assets/images/mine/icons/level${level}.svg`)
      } catch (error) {
        // 如果找不到特定等级的图标，返回默认图标
        console.warn(`未找到等级${level}的图标，使用默认图标替代`)
        return require('@/assets/images/mine/icons/level.svg')
      }
    },

    // 图片错误处理
    handleImageError(event, type) {
      ImageErrorHandler.handleImageError(event, type);
    },

    // 切换级别标签
    changeTab(level) {
      if (this.activeTab === level) return

      this.activeTab = level

      // 检查当前级别是否有数据，如果没有则重新请求
      const hasCurrentLevelData = this.memberList.some(member => member.find === level)

      if (!hasCurrentLevelData && this.memberList.length === 0) {
        this.resetPagination()
        this.getTeamMembers()
      }
    },

    // 重置分页信息
    resetPagination() {
      this.page = 1
      this.noMore = false
      this.memberList = []
    },

    // 初始化吸顶元素
    initStickyElements() {
      this.$nextTick(() => {
        const stickyContainer = document.querySelector('.sticky-container')
        if (stickyContainer) {
          // 获取头部高度
          const header = document.querySelector('.header')
          const headerHeight = header ? header.offsetHeight : 0

          // 计算触发吸顶的位置 - 添加10px的缓冲，使切换更平滑
          this.stickyTriggerPosition = stickyContainer.offsetTop - headerHeight - 10

          // 保存吸顶容器的高度，用于设置占位元素高度
          // 在吸顶状态下，高度会略有不同，所以稍微增加一点缓冲
          this.stickyHeight = stickyContainer.offsetHeight + 5
        }
      })
    },

    // 设置滚动监听
    setupScrollListener() {
      window.addEventListener('scroll', this.handleScroll, { passive: true })
    },

    // 清理滚动监听
    cleanupScrollListener() {
      window.removeEventListener('scroll', this.handleScroll)
      if (this.scrollTimeout) {
        clearTimeout(this.scrollTimeout)
      }
    },

    // 设置窗口大小调整的监听
    setupResizeListener() {
      window.addEventListener('resize', this.handleResize, { passive: true })
    },

    // 清理窗口大小调整的监听
    cleanupResizeListener() {
      window.removeEventListener('resize', this.handleResize)
    },

    // 处理窗口大小变化
    handleResize() {
      // 重新计算吸顶元素的高度和位置
      this.initStickyElements()
    },

    // 处理滚动事件（分页加载和吸顶效果）
    handleScroll() {
      if (this.scrollTimeout) clearTimeout(this.scrollTimeout)

      this.scrollTimeout = setTimeout(() => {
        const scrollTop = document.documentElement.scrollTop || document.body.scrollTop
        const scrollHeight = document.documentElement.scrollHeight || document.body.scrollHeight
        const clientHeight = document.documentElement.clientHeight || window.innerHeight

        // 处理吸顶效果
        this.isSticky = scrollTop > this.stickyTriggerPosition

        // 距离底部50px时触发加载
        if (scrollTop + clientHeight >= scrollHeight - 50) {
          if (!this.loading && !this.noMore && this.currentLevelMembers.length > 0) {
            this.page++
            this.getTeamMembers(true)
          }
        }
      }, 100) // 减小节流时间，使吸顶效果更流畅
    },

    // 金额格式化
    formatAmount(amount) {
      if (!amount || isNaN(amount)) return '0.00'
      return parseFloat(amount).toFixed(2)
    },

    // 显示提示信息
    showToast(message) {
      // 这里可以根据具体的UI库实现toast提示
      console.log('Toast:', message)
      // 如果有全局的toast方法，可以调用
      if (this.$toast) {
        this.$toast(message)
      } else if (this.$message) {
        this.$message(message)
      } else {
        alert(message)
      }
    }
  }
}
</script>

<style scoped>
/* 页面主容器 */
.team-page {
  min-height: 100vh;
  background-color: #F8F9FA;
  padding-bottom: 20px;
  position: relative;
}

/* 头部样式 */
.header {
  height: 44px;
  background-color: #FFF;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  position: sticky;
  top: 0;
  z-index: 101;
  /* 头部层级要高于吸顶容器 */
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.back-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.back-icon img {
  width: 10px;
  height: 16px;
}

.title {
  font-size: 17px;
  font-weight: 500;
  color: #111827;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.right-icon {
  width: 24px;
  height: 24px;
}

/* 吸顶容器样式 */
.sticky-container {
  position: relative;
  z-index: 99;
  background-color: #F8F9FA;
  transition: all 0.3s ease;
}

.sticky-container.is-sticky {
  position: fixed;
  top: 44px;
  /* 头部高度 */
  left: 0;
  right: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  z-index: 100;
  padding-bottom: 10px;
}

/* 吸顶状态下的内部元素样式调整 */
.sticky-container.is-sticky .team-members-count {
  padding: 8px 16px 6px;
  background-color: #F8F9FA;
}

.sticky-container.is-sticky .team-stats-card {
  margin: 8px 16px;
  transform: scale(0.98);
  transform-origin: top center;
}

.sticky-container.is-sticky .level-tabs {
  margin: 10px 16px 5px;
}

/* 吸顶容器的占位元素 */
.sticky-placeholder {
  width: 100%;
  transition: height 0.3s ease;
}

/* 团队人数 */
.team-members-count {
  display: flex;
  align-items: center;
  padding: 14px 16px 10px;
  background-color: #FFFFFF;
}

.team-icon {
  width: 22px;
  height: 22px;
  margin-right: 10px;
  filter: drop-shadow(0 1px 2px rgba(4, 116, 252, 0.1));
}

.count-label {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
  margin-right: 8px;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.count-value {
  font-size: 16px;
  font-weight: 600;
  color: #0474FC;
  font-family: 'DIN', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* 团队统计卡片 */
.team-stats-card {
  margin: 12px 16px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background: #FFFFFF;
}

.stats-area {
  background: linear-gradient(180deg, #0094FF 0%, #0474FC 100%);
  padding: 0;
  color: #FFFFFF;
  position: relative;
  border-radius: 12px;
  overflow: hidden;
}

/* 等级统计行 */
.level-stats-row {
  display: flex;
  justify-content: space-around;
  background: transparent;
  padding: 20px 16px;
  margin-bottom: 0;
  position: relative;
  z-index: 2;
}

.level-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  padding: 0;
  position: relative;
}

.level-stat:hover {
  transform: none;
  box-shadow: none;
  background: none;
}

.level-stat:not(:last-child)::after {
  display: none;
}

.level-label {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 8px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  text-align: center;
  line-height: 1.2;
}

.level-value {
  font-size: 24px;
  font-weight: 600;
  color: #FFFFFF;
  font-family: 'DIN', sans-serif;
  position: relative;
}

.level-value::after {
  display: none;
}

/* 收益统计行 */
.income-stats-row {
  display: flex;
  justify-content: space-around;
  padding: 0 16px 20px;
  position: relative;
  z-index: 2;
  background: #FFFFFF;
  border-radius: 0 0 12px 12px;
}

.income-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;
  padding: 16px 0;
}

.income-stat:hover {
  transform: none;
  background: none;
  box-shadow: none;
}

.income-stat:first-child::after {
  display: none;
}

.income-value {
  font-size: 12px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 4px;
  font-family: 'DIN', sans-serif;
  position: relative;
}

.income-value::before {
  display: none;
}

.income-label {
  font-size: 12px;
  color: #999999;
  font-weight: 400;
  font-family: 'PingFang SC', sans-serif;
}

/* 三级切换标签 */
.level-tabs {
  display: flex;
  justify-content: space-around;
  margin: 20px 15px 15px;
  position: relative;
  background: #FFFFFF;
  border-radius: 8px;
  padding: 2px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 12px 8px;
  font-size: 14px;
  color: #666666;
  position: relative;
  transition: all 0.3s ease;
  font-family: 'PingFang SC', sans-serif;
  border-radius: 8px;
  margin: 2px;
  cursor: pointer;
}

.tab-item:hover {
  color: #0474FC;
  background-color: rgba(4, 116, 252, 0.05);
}

.tab-item.active {
  color: #FFFFFF;
  font-weight: 500;
  background: linear-gradient(135deg, #0474FC, #1F68D6);
  box-shadow: 0 2px 8px rgba(4, 116, 252, 0.25);
}

.tab-item span {
  font-size: 14px;
  font-weight: 600;
  transition: color 0.3s ease;
}

.tab-indicator {
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: #0474FC;
  border-radius: 3px 3px 0 0;
  display: none;
  /* 不需要这个指示器，因为我们已经有了背景色 */
}

/* 已在上方重新定义了 .tab-item.active 和 .tab-item.active span 样式 */

/* 成员列表容器 */
.members-container {
  position: relative;
  z-index: 10;
  padding: 0 16px 20px;
}

/* 成员卡片 */
.member-card {
  background: #FFFFFF;
  border-radius: 10px;
  margin-bottom: 16px;
  position: relative;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.member-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

/* 成员头像 */
.member-avatar {
  width: 40px;
  height: 40px;
  position: absolute;
  top: 16px;
  left: 16px;
  border-radius: 50%;
  overflow: hidden;
  background-color: #E8EFFB;
  box-shadow: 0 2px 6px rgba(4, 116, 252, 0.15);
}

.member-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 成员信息 */
.member-info {
  margin-left: 52px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.member-basic {
  flex: 1;
}

.member-name {
  font-size: 14px;
  color: #000000;
  margin-bottom: 4px;
}

.member-phone {
  font-size: 12px;
  color: #9E9E9E;
}

/* 等级标签 */
.level-tag {
  flex-shrink: 0;
}

.level-tag-inner {
  background: linear-gradient(to right, #F8F8F8, #EFEFEF);
  border-radius: 12px;
  padding: 4px 10px;
  display: flex;
  align-items: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.level-icon {
  width: 12px;
  height: 12px;
  margin-right: 4px;
}

.level-tag span {
  font-size: 11px;
  font-weight: 500;
  color: #666666;
}

/* 分隔线 */
.member-divider {
  height: 1px;
  background-color: #F0F0F0;
  margin: 12px 0;
}

/* 成员统计 */
.member-stats {
  display: flex;
  justify-content: space-between;
}

.stat-col {
  flex: 1;
  text-align: center;
}

.stat-col:not(:last-child) {
  border-right: 1px solid #F0F0F0;
}

.stat-value {
  font-size: 13px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 4px;
  font-family: 'DIN', -apple-system, BlinkMacSystemFont, sans-serif;
}

.stat-label {
  font-size: 11px;
  color: #9CA3AF;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30px 20px;
  gap: 12px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(4, 116, 252, 0.1);
  border-top: 3px solid #0474FC;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  color: #666666;
  font-size: 14px;
  font-weight: 500;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #666666;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.empty-icon {
  margin-bottom: 16px;
  width: 64px;
  height: 64px;
  opacity: 0.8;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.empty-text {
  color: #666666;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
}

.empty-tip {
  color: #999999;
  font-size: 13px;
  max-width: 240px;
  line-height: 1.5;
}

/* 没有更多数据状态 */
.no-more-state {
  text-align: center;
  padding: 20px;
}

.no-more-text {
  color: #999999;
  font-size: 13px;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .team-stats-card {
    margin: 10px 12px;
    border-radius: 10px;
  }

  .stats-area {
    border-radius: 10px;
  }

  .level-stats-row {
    padding: 16px 12px;
  }

  .level-label {
    font-size: 12px;
    margin-bottom: 6px;
  }

  .level-value {
    font-size: 20px;
  }

  .income-stats-row {
    padding: 0 12px 16px;
    border-radius: 0 0 10px 10px;
  }

  .income-stat {
    padding: 12px 0;
  }

  .income-value {
    font-size: 12px;
  }

  .income-label {
    font-size: 11px;
  }

  .level-tabs {
    margin: 15px 12px 12px;
  }

  .tab-item {
    padding: 10px 6px;
  }

  .members-container {
    padding: 0 12px 20px;
  }

  .member-card {
    padding: 14px;
  }

  .member-name {
    font-size: 13px;
  }

  .member-phone {
    font-size: 11px;
  }

  .level-tag-inner {
    padding: 3px 8px;
  }
}

/* 当没有数据时的额外样式 */
.members-container:empty {
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
