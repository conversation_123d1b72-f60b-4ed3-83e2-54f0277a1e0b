<template>
  <div class="project-order-detail">
    <!-- 头部导航 -->
    <div class="header">
      <div class="back-icon" @click="goBack">
        <img src="@/assets/icons/back-icon.svg" @error="handleImageError($event, 'icon')" alt="返回">
      </div>
      <div class="title">订单详情</div>
      <div class="right-placeholder"></div>
    </div>

    <!-- 加载状态 -->
    <div class="loading-overlay" v-if="loading">
      <div class="loading-spinner"></div>
      <div class="loading-text">加载中...</div>
    </div> <!-- 订单状态 -->
    <div class="order-status-card">
      <div class="status-content">
        <div class="status-icon" :class="getStatusClass(orderInfo.status)">
          <van-icon name="checked" v-if="orderInfo.status === 2" />
          <van-icon name="clock-o" v-else />
        </div>
        <div class="status-text">{{ getStatusText(orderInfo.status) }}</div>
        <div class="status-desc" v-if="orderInfo.status === 1">
          预计 {{ orderInfo.end_time }} 结束
        </div>
        <div class="status-desc" v-else-if="orderInfo.status === 2">
          已于 {{ orderInfo.end_time }} 结束
        </div>
      </div>
    </div>

    <!-- 订单信息 -->
    <div class="order-info-card">
      <div class="card-title">订单信息</div>

      <div class="info-row">
        <div class="info-label">项目名称</div>
        <div class="info-value">{{ orderInfo.product_name }}</div>
      </div>

      <div class="info-row">
        <div class="info-label">订单编号</div>
        <div class="info-value">{{ orderInfo.order_id }}</div>
      </div>

      <div class="info-row">
        <div class="info-label">订单金额</div>
        <div class="info-value highlight">{{ orderInfo.amount }}</div>
      </div>

      <div class="info-row">
        <div class="info-label">收益规则</div>
        <div class="info-value">{{ orderInfo.rate_type }}</div>
      </div>

      <div class="info-row">
        <div class="info-label">收益率</div>
        <div class="info-value">{{ orderInfo.rate }}%</div>
      </div>

      <div class="info-row">
        <div class="info-label">项目周期</div>
        <div class="info-value">{{ orderInfo.cycle }}</div>
      </div>

      <div class="info-row">
        <div class="info-label">预期收益</div>
        <div class="info-value highlight">{{ orderInfo.earnings }}</div>
      </div>

      <div class="info-row">
        <div class="info-label">起息时间</div>
        <div class="info-value">{{ orderInfo.create_time }}</div>
      </div>

      <div class="info-row">
        <div class="info-label">到期时间</div>
        <div class="info-value">{{ orderInfo.end_time }}</div>
      </div>

      <div class="info-row">
        <div class="info-label">总额</div>
        <div class="info-value highlight bold">{{ orderInfo.total }}</div>
      </div>
    </div>

    <!-- 收益明细 -->
    <div class="earnings-detail-card">
      <div class="card-title">收益明细</div>

      <div class="earnings-list">
        <div class="earnings-item" v-for="(item, index) in earningsList" :key="index"
          :class="{ 'last-item': index === earningsList.length - 1 }">
          <div class="earnings-info">
            <div class="earnings-date">{{ item.date }}</div>
            <div class="earnings-type" v-if="getItemDisplayType(item) === 'amount'">本金返还</div>
            <div class="earnings-type earnings-type-interest" v-else-if="getItemDisplayType(item) === 'rate'">利息收益</div>
          </div>
          <div class="earnings-amount">
            <span class="earnings-amount-value">{{ getItemDisplayValue(item) }}</span>
            <span class="earnings-status" :class="getEarningsStatusClass(item.status)">
              {{ getEarningsStatusText(item.status) }}
            </span>
          </div>
        </div>

        <!-- 无数据状态 -->
        <div class="empty-state" v-if="!earningsList || earningsList.length === 0">
          <div class="empty-icon">📝</div>
          <div class="empty-text">暂无收益明细</div>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="bottom-buttons">
      <div class="action-button" @click="viewContract">查看合同</div>
    </div>
  </div>
</template>

<script>
import ImageErrorHandler from '@/utils/ImageErrorHandler'

export default {
  name: 'ProjectOrderDetail', data() {
    return {
      id: null,
      loading: true,
      orderInfo: {
        order_id: '',
        product_name: '',
        amount: '',
        rate_type: '',
        rate: '',
        cycle: '',
        earnings: '',
        create_time: '',
        update_time: '',
        end_time: '',
        total: '',
        status: 1
      },
      earningsList: []
    }
  },
  created() {
    this.id = this.$route.query.id
    if (!this.id) {
      this.$toast('订单ID不存在')
      this.goBack()
      return
    }
    this.loadOrderDetail()
  }, methods: {
    // 处理图片加载错误
    handleImageError(event, type) {
      ImageErrorHandler.handleImageError(event, type)
    },

    // 返回上一页
    goBack() {
      this.$router.go(-1)
    },
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        1: '项目进行中',
        2: '项目已完成'
      }
      return statusMap[status] || '未知状态'
    },

    // 获取状态类名
    getStatusClass(status) {
      return status === 1 ? 'status-active' : 'status-finished'
    },
    // 获取收益状态文本
    getEarningsStatusText(status) {
      const statusMap = {
        1: '待发放',
        2: '已发放'
      }
      return statusMap[status] || ''
    },    // 获取收益状态类名
    getEarningsStatusClass(status) {
      return status === 1 ? 'earnings-pending' : 'earnings-received'
    },

    // 获取项目显示类型
    getItemDisplayType(item) {
      // 如果有明确的 displayType 属性，直接使用
      if (item.displayType) {
        return item.displayType;
      }

      // 否则根据 amount 值判断
      return item.amount && parseFloat(item.amount) > 0 ? 'amount' : 'rate';
    },

    // 获取项目显示值
    getItemDisplayValue(item) {
      if (item.displayType === 'amount' || (item.amount && parseFloat(item.amount) > 0 && !item.displayType)) {
        return item.amount;
      } else {
        return item.rate;
      }
    },// 加载订单详情数据
    loadOrderDetail() {
      this.loading = true
      // 从localStorage获取token
      const userToken = localStorage.getItem('user_token')

      if (!userToken) {
        this.$toast('请先登录')
        this.$nextTick(() => { this.$router.replace('/login') })
        this.loading = false
        return
      }

      // 发起API请求获取订单详情
      this.$http.get(`/order/getProductDetail?id=${this.id}`, {
        headers: {
          'authorization': userToken
        }
      })
        .then(response => {
          if (response.data.code === 200) {
            const data = response.data.data

            if (data.info) {
              this.orderInfo = data.info
            } if (data.list) {
              // 如果只有一条记录，处理成两条记录，一条显示 amount，另一条显示 rate
              if (data.list.length === 1 && data.list[0]) {
                const item = data.list[0];
                // 只有当同时存在 amount 和 rate 时才分为两条显示
                if (item.amount && item.rate && parseFloat(item.amount) > 0 && parseFloat(item.rate) > 0) {
                  const amountItem = { ...item, displayType: 'amount' };
                  const rateItem = { ...item, displayType: 'rate' };
                  this.earningsList = [amountItem, rateItem];
                } else {
                  this.earningsList = data.list;
                }
              } else {
                this.earningsList = data.list;
              }
            }
          } else {
            this.$toast(response.data.msg || '获取订单详情失败')
          }
          this.loading = false
        })
        .catch(error => {
          console.error('获取订单详情失败:', error)
          this.$toast('网络异常，请稍后重试')
          this.loading = false
        })
    },

    // 查看合同
    viewContract() {
      this.$router.push(`/product-contract?id=${this.id}`)
    }
  }
}
</script>

<style scoped>
.project-order-detail {
  min-height: 100vh;
  background-color: #F8F9FA;
  padding-bottom: 80px;
}

/* 头部样式 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background-color: #fff;
  position: relative;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.back-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.back-icon img {
  width: 10px;
  height: 16px;
}

.title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 17px;
  font-weight: 500;
  color: #111827;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
}

.right-placeholder {
  width: 24px;
  height: 24px;
}

/* 订单状态卡片 */
.order-status-card {
  margin: 16px;
  background: linear-gradient(103.91deg, #EACFD8 4.98%, #50B9F9 27.83%, #0474FC 41.44%, #BAB8E0 80.93%, #95BAEB 98.32%);
  border-radius: 12px;
  padding: 0;
  text-align: center;
  box-shadow: 0 4px 12px rgba(4, 116, 252, 0.2);
  overflow: hidden;
}

.status-content {
  padding: 24px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.status-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 54px;
  height: 54px;
  border-radius: 50%;
  margin-bottom: 12px;
  font-size: 26px;
  background-color: rgba(255, 255, 255, 0.2);
  color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.status-icon.status-active {
  background-color: rgba(255, 255, 255, 0.2);
  color: #fff;
}

.status-icon.status-finished {
  background-color: rgba(255, 255, 255, 0.2);
  color: #fff;
}

.status-text {
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 600;
  font-size: 18px;
  color: #fff;
  margin-bottom: 6px;
}

.status-desc {
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.85);
}

/* 订单信息卡片 */
.order-info-card,
.earnings-detail-card {
  margin: 0 16px 16px;
  background-color: #fff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.card-title {
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 600;
  font-size: 16px;
  color: #222;
  margin-bottom: 16px;
  position: relative;
  padding-left: 12px;
}

.card-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background-color: #0474FC;
  border-radius: 2px;
}

.card-title::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: -8px;
  width: 100%;
  height: 1px;
  background-color: #f0f0f0;
}

.info-row {
  display: flex;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px dashed rgba(0, 0, 0, 0.06);
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #666;
}

.info-value {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #333;
  text-align: right;
}

.info-value.highlight {
  color: #0474FC;
  font-weight: 500;
}

.info-value.bold {
  font-weight: 700;
  font-size: 16px;
}

/* 收益明细 */
.earnings-list {
  margin-top: 16px;
}

.earnings-item {
  display: flex;
  justify-content: space-between;
  padding: 15px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
}

.earnings-item:after {
  content: '';
  position: absolute;
  left: -6px;
  top: 50%;
  transform: translateY(-50%);
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #0474FC;
  opacity: 0.6;
}

.earnings-item:last-child {
  border-bottom: none;
}

.earnings-item.last-item .earnings-amount-value {
  font-size: 18px;
  font-weight: 700;
}

.earnings-info {
  display: flex;
  flex-direction: column;
}

.earnings-date {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #222;
  margin-bottom: 6px;
}

.earnings-type {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 500;
  font-size: 12px;
  color: #0474FC;
  background-color: rgba(4, 116, 252, 0.1);
  padding: 2px 8px;
  border-radius: 10px;
  display: inline-block;
}

.earnings-type-interest {
  color: #6BA771;
  background-color: rgba(107, 167, 113, 0.1);
}

.earnings-amount {
  display: flex;
  align-items: center;
}

.earnings-amount-value {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 500;
  font-size: 16px;
  color: #0474FC;
  margin-right: 8px;
}

.earnings-status {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 400;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
}

.earnings-pending {
  color: #0474FC;
  background-color: rgba(4, 116, 252, 0.1);
}

.earnings-received {
  color: #6BA771;
  background-color: rgba(107, 167, 113, 0.1);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #666666;
  background: rgba(4, 116, 252, 0.02);
  border-radius: 10px;
  margin: 16px 0;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
  color: #0474FC;
  animation: pulse 2s infinite ease-in-out;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.6;
  }

  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }

  100% {
    transform: scale(1);
    opacity: 0.6;
  }
}

.empty-text {
  color: #666666;
  font-size: 15px;
  font-weight: 500;
}

/* 底部按钮 */
.bottom-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 16px;
  background-color: #fff;
  box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.08);
}

.action-button {
  height: 48px;
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 600;
  font-size: 17px;
  color: #fff;
  background: linear-gradient(90deg, #0474FC 0%, #0056BC 100%);
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(4, 116, 252, 0.3);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.action-button:active {
  transform: translateY(2px);
  box-shadow: 0 2px 8px rgba(4, 116, 252, 0.2);
}

/* 加载状态覆盖层 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.92);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 16px;
  z-index: 999;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(4, 116, 252, 0.1);
  border-top: 3px solid #0474FC;
  border-right: 3px solid #0474FC;
  border-radius: 50%;
  animation: spin 1s cubic-bezier(0.55, 0.25, 0.25, 0.7) infinite;
  box-shadow: 0 0 12px rgba(4, 116, 252, 0.2);
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  color: #0474FC;
  font-size: 15px;
  font-weight: 500;
  letter-spacing: 1px;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  animation: fadeInOut 1.5s infinite ease-in-out;
}

@keyframes fadeInOut {
  0% {
    opacity: 0.7;
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0.7;
  }
}
</style>
