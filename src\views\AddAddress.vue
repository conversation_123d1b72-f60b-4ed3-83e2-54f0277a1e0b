<template>
  <div class="add-address">
    <!-- 头部 -->
    <div class="header">
      <div class="back-icon" @click="goBack">
        <img src="@/assets/icons/back-icon.svg" alt="返回">
      </div>
      <div class="title">{{ isEdit ? '编辑地址' : '添加地址' }}</div>
    </div>

    <!-- 表单 -->
    <div class="form-section">
      <!-- 收货人 -->
      <div class="form-item">
        <div class="item-label">收货人</div>
        <div class="item-input">
          <input type="text" v-model="addressInfo.name" placeholder="请输入收货人姓名">
        </div>
      </div> <!-- 手机号码 -->
      <div class="form-item">
        <div class="item-label">手机号码</div>
        <div class="item-input">
          <input type="tel" v-model="addressInfo.mobile" placeholder="请输入手机号码">
        </div>
      </div> <!-- 省市 -->
      <div class="form-item">
        <div class="item-label">省市</div>
        <div class="item-input">
          <input type="text" v-model="addressInfo.area" placeholder="请输入省市">
        </div>
      </div><!-- 详细地址 -->
      <div class="form-item">
        <div class="item-label">详细地址</div>
        <div class="item-input">
          <textarea v-model="addressInfo.address" placeholder="请输入详细地址，如道路名、门牌号、小区名、楼号、单元号等"></textarea>
        </div>
      </div>
    </div> <!-- 底部按钮 -->
    <div class="bottom-actions">
      <div class="save-btn" @click="saveAddress">保存</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AddAddress',
  data() {
    return {
      isEdit: false, addressInfo: {
        id: null,
        name: '',
        mobile: '',
        area: '',
        address: ''
      },
      loading: false
    };
  }, created() {
    // 判断是否为编辑模式
    const addressId = this.$route.params.id;
    if (addressId && addressId !== 'add') {
      this.isEdit = true;
      this.addressInfo.id = parseInt(addressId);
      this.getAddressDetail(addressId);
    } else {
      // 新增模式，获取用户基本信息
      this.getAddressDetail('');
    }
  }, methods: {
    // 检查登录状态
    checkLoginStatus() {
      const userToken = localStorage.getItem('user_token')
      if (!userToken) {
        this.$toast('请先登录')
        this.$nextTick(() => { this.$router.replace('/login') })
        return false
      }
      return true
    },

    goBack() {
      this.$router.go(-1);
    },

    // 获取地址详情或用户基本信息
    async getAddressDetail(id) {
      if (!this.checkLoginStatus()) return

      this.loading = true
      const userToken = localStorage.getItem('user_token')

      try {
        const response = await this.$http.get(`/member/doAddress?id=${id}`, {
          headers: {
            'authorization': userToken
          }
        })

        if (response.data.code === 200) {
          const info = response.data.data.info
          this.addressInfo = {
            id: info.id,
            name: info.name || '',
            mobile: info.mobile || '',
            area: info.area || '',
            address: info.address || ''
          }
        } else {
          this.$toast(response.data.msg || '获取地址信息失败')
        }
      } catch (error) {
        console.error('获取地址信息失败:', error)

        if (error.response && (error.response.status === 401 || error.response.status === 403)) {
          this.$toast('登录已过期，请重新登录')
          localStorage.removeItem('user_token')
          this.$nextTick(() => { this.$router.replace('/login') })
        } else {
          this.$toast('网络异常，请稍后重试')
        }
      } finally {
        this.loading = false
      }
    },

    validateForm() {
      if (!this.addressInfo.name) {
        this.$toast('请输入收货人姓名');
        return false;
      }

      if (!this.addressInfo.mobile) {
        this.$toast('请输入手机号码');
        return false;
      }

      // 验证手机号格式
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(this.addressInfo.mobile)) {
        this.$toast('手机号格式不正确');
        return false;
      } if (!this.addressInfo.area) {
        this.$toast('请输入省市');
        return false;
      }

      if (!this.addressInfo.address) {
        this.$toast('请输入详细地址');
        return false;
      }

      return true;
    },

    // 保存地址
    async saveAddress() {
      if (!this.validateForm()) return
      if (!this.checkLoginStatus()) return

      this.$toast.loading({
        message: '保存中...',
        forbidClick: true,
      })

      const userToken = localStorage.getItem('user_token')

      try {
        const response = await this.$http.post('/member/doAddress', {
          id: this.addressInfo.id,
          name: this.addressInfo.name,
          mobile: this.addressInfo.mobile,
          area: this.addressInfo.area,
          address: this.addressInfo.address
        }, {
          headers: {
            'authorization': userToken,
            'Content-Type': 'application/json'
          }
        })

        this.$toast.clear()

        if (response.data.code === 200) {
          this.$toast.success(this.isEdit ? '修改成功' : '添加成功')
          setTimeout(() => {
            this.$router.go(-1)
          }, 1500)
        } else {
          this.$toast(response.data.msg || '保存失败')
        }
      } catch (error) {
        this.$toast.clear()
        console.error('保存地址失败:', error)

        if (error.response && (error.response.status === 401 || error.response.status === 403)) {
          this.$toast('登录已过期，请重新登录')
          localStorage.removeItem('user_token')
          this.$nextTick(() => { this.$router.replace('/login') })
        } else {
          this.$toast('网络异常，请稍后重试')
        }
      }
    }
  },
  watch: {
    showCustomTagInput(val) {
      if (val) {
        this.$nextTick(() => {
          this.$refs.customTagInput.focus();
        });
      }
    }
  }
};
</script>

<style scoped>
.add-address {
  min-height: 100vh;
  background-color: #f6f6f6;
  padding-bottom: 80px;
}

.header {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  background-color: #fff;
  position: relative;
}

.back-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon img {
  width: 20px;
  height: 20px;
}

.title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

.form-section {
  background-color: #fff;
  margin-top: 10px;
}

.form-item {
  padding: 15px 16px;
  border-bottom: 1px solid #f5f5f5;
}

.form-item:last-child {
  border-bottom: none;
}

.item-label {
  font-size: 14px;
  color: #333;
  margin-bottom: 10px;
}

.item-input {
  display: flex;
  align-items: center;
}

.item-input input,
.item-input textarea {
  flex: 1;
  border: none;
  outline: none;
  font-size: 14px;
  color: #333;
}

.item-input textarea {
  height: 60px;
  resize: none;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
}

.tag-item {
  height: 30px;
  padding: 0 15px;
  background-color: #f5f5f5;
  border-radius: 15px;
  font-size: 12px;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  margin-bottom: 10px;
}

.tag-item.active {
  background-color: rgba(255, 97, 139, 0.1);
  color: #0474FC;
}

.tag-item.custom {
  padding: 0 10px;
}

.tag-item.custom .van-icon {
  margin-right: 3px;
}

.custom-tag-input {
  height: 30px;
  padding: 0 10px;
  background-color: #f5f5f5;
  border-radius: 15px;
  display: flex;
  align-items: center;
  margin-right: 10px;
  margin-bottom: 10px;
}

.custom-tag-input input {
  width: 60px;
  border: none;
  outline: none;
  background-color: transparent;
  font-size: 12px;
  color: #333;
}

.switch-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.switch-item .item-label {
  margin-bottom: 0;
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  padding: 10px 16px;
  background-color: #fff;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

.save-btn {
  flex: 1;
  height: 44px;
  background-color: #0474FC;
  color: #fff;
  font-size: 16px;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
