<template>
  <div class="bind-bank-card">
    <!-- 头部 -->
    <div class="header">
      <div class="back-icon" @click="goBack">
        <img src="@/assets/icons/back-icon.svg" alt="返回">
      </div>
      <div class="title">银行卡信息</div>
    </div>

    <!-- 银行卡表单 -->
    <div class="bank-form">
      <!-- 持卡人姓名 -->
      <div class="form-item">
        <div class="form-label">持卡人姓名</div>
        <div class="form-input">
          <input type="text" v-model="bankInfo.name" placeholder="请输入持卡人姓名">
        </div>
      </div>

      <!-- 所属银行 -->
      <div class="form-item">
        <div class="form-label">所属银行</div>
        <div class="form-input">
          <input type="text" v-model="bankInfo.bank_name" placeholder="请输入银行名称">
        </div>
      </div>

      <!-- 银行卡号 -->
      <div class="form-item">
        <div class="form-label">银行卡号</div>
        <div class="form-input">
          <input type="text" v-model="bankInfo.bank_card" placeholder="请输入银行卡号" @input="formatBankCardInput">
        </div>
      </div>

      <!-- 开户网点 -->
      <div class="form-item">
        <div class="form-label">开户网点</div>
        <div class="form-input">
          <input type="text" v-model="bankInfo.bank_address" placeholder="请输入开户网点">
        </div>
      </div>
    </div>

    <!-- 确认按钮 -->
    <div class="submit-btn" @click="submitBankInfo">
      确认
    </div>
  </div>
</template>

<script>
export default {
  name: "BindBankCard",
  data() {
    return {
      loading: true,
      bankInfo: {
        name: "",
        bank_name: "",
        bank_card: "",
        bank_address: ""
      }
    };
  },
  created() {
    this.getBankInfo();
  },
  methods: {
    goBack() {
      this.$router.go(-1);
    },
    // 获取银行卡信息
    async getBankInfo() {
      try {
        const response = await this.$http.get('/member/getBank', {
          headers: {
            'authorization': localStorage.getItem('user_token') || ''
          }
        });

        if (response.data.code === 200) {
          // 如果有银行卡信息，填充到表单
          const bankData = response.data.data;
          if (bankData && bankData.info) {
            this.bankInfo = bankData.info;
          }
        } else {
          this.$toast(response.data.msg || '获取银行卡信息失败');
        }
      } catch (error) {
        console.error('获取银行卡信息失败', error);
        this.$toast('获取银行卡信息失败，请稍后再试');
      } finally {
        this.loading = false;
      }
    },
    // 提交银行卡信息
    async submitBankInfo() {
      // 表单验证
      if (!this.bankInfo.name) {
        return this.$toast('请输入持卡人姓名');
      }
      if (!this.bankInfo.bank_name) {
        return this.$toast('请输入银行名称');
      }
      if (!this.bankInfo.bank_card) {
        return this.$toast('请输入银行卡号');
      }
      if (!this.bankInfo.bank_address) {
        return this.$toast('请输入开户网点');
      }

      // 去除银行卡号中的空格
      const cleanBankCard = this.bankInfo.bank_card.replace(/\s/g, '');

      try {
        const bankData = {
          name: this.bankInfo.name,
          bank_name: this.bankInfo.bank_name,
          bank_card: cleanBankCard,
          bank_address: this.bankInfo.bank_address
        };

        const response = await this.$http.post('/member/doBank', bankData, {
          headers: {
            'authorization': localStorage.getItem('user_token') || ''
          }
        });

        if (response.data.code === 200) {
          this.$toast('银行卡信息保存成功');
          setTimeout(() => {
            this.goBack();
          }, 1500);
        } else {
          this.$toast(response.data.msg || '保存银行卡信息失败');
        }
      } catch (error) {
        console.error('保存银行卡信息失败', error);
        this.$toast('保存银行卡信息失败，请稍后再试');
      }
    },
    // 格式化银行卡输入，每4位添加空格
    formatBankCardInput() {
      this.bankInfo.bank_card = this.bankInfo.bank_card
        .replace(/\s/g, '')
        .replace(/(\d{4})(?=\d)/g, '$1 ');
    }
  }
};
</script>

<style scoped>
.bind-bank-card {
  min-height: 100vh;
  background-color: #f6f6f6;
}

.header {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  background-color: #fff;
  position: relative;
}

.back-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon img {
  width: 20px;
  height: 20px;
}

.title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

.bank-form {
  margin-top: 10px;
  padding: 0 16px;
  background-color: #fff;
}

.form-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #f5f5f5;
}

.form-item:last-child {
  border-bottom: none;
}

.form-label {
  width: 30%;
  font-size: 16px;
  color: #333;
}

.form-input {
  width: 70%;
  text-align: right;
}

.form-input input {
  width: 100%;
  border: none;
  outline: none;
  text-align: right;
  font-size: 16px;
  color: #333;
  background-color: transparent;
}

.form-input input::placeholder {
  color: #999;
  font-size: 14px;
}

.submit-btn {
  margin: 30px 16px;
  height: 44px;
  line-height: 44px;
  text-align: center;
  background-color: #0474FC;
  color: #fff;
  font-size: 16px;
  border-radius: 22px;
}
</style>
