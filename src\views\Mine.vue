<template>
  <div class="mine">
    <!-- 背景渐变 -->
    <div class="bg-gradient"></div>

    <!-- 头部区域 -->
    <div class="header-section">
      <!-- 页面标题 -->
      <div class="page-title">我的</div>

      <!-- 用户信息 -->
      <div class="user-profile">
        <!-- 头像 -->
        <div class="avatar-container">
          <img :src="userInfo.avatar" @error="handleImageError($event, 'avatar')" alt="用户头像" class="avatar">
        </div>

        <div class="user-info">
          <div class="username">{{ userInfo.kyc_name || userInfo.mobile }}</div>
          <div class="user-level" @click="goToMemberLevel">
            <img :src="getLevelIcon(userInfo.level)" @error="handleImageError($event, 'level-icon')" alt="会员等级">
          </div>
        </div>
      </div>
    </div>

    <!-- 账户信息卡片 -->
    <div class="account-card">
      <div class="card-header">
        <div class="account-title">我的账户</div>
      </div>

      <!-- 余额信息 -->
      <div class="balance-section">
        <div class="main-balance-row">
          <div class="main-balance-item">
            <div class="balance-label">账户余额</div>
            <div class="balance-amount main">{{ userInfo.balance }}</div>
          </div>

          <!-- 操作按钮 -->
          <div class="action-buttons">
            <button class="action-btn primary" @click="goToRecharge">充值</button>
            <button class="action-btn secondary" @click="goToWithdraw">提现</button>
          </div>
        </div>

        <div class="balance-row">
          <div class="balance-item">
            <div class="balance-label">团队总收益</div>
            <div class="balance-amount">{{ userInfo.integral }}</div>
          </div>
          <div class="balance-item">
            <div class="balance-label">总收益</div>
            <div class="balance-amount">{{ userInfo.total_income }}</div>
          </div>
        </div>

        <div class="balance-row">
          <div class="balance-item">
            <div class="balance-label">可提现余额</div>
            <div class="balance-amount">{{ userInfo.withdraw_can }}</div>
          </div>
          <div class="balance-item">
            <div class="balance-label">待收入余额</div>
            <div class="balance-amount">{{ userInfo.total_amount }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 快捷操作 -->
    <div class="quick-actions">
      <div class="action-item" @click="goToRecharge">
        <div class="action-icon recharge">
          <img src="@/assets/images/mine/icons/recharge-icon.svg" @error="handleImageError($event, 'icon')" alt="充值" />
        </div>
        <div class="action-text">充值</div>
      </div>

      <div class="action-item" @click="goToWithdraw">
        <div class="action-icon withdraw">
          <img src="@/assets/images/mine/icons/withdraw-icon.svg" @error="handleImageError($event, 'icon')" alt="提现" />
        </div>
        <div class="action-text">提现</div>
      </div>

      <div class="action-item" @click="goToTransfer">
        <div class="action-icon transfer">
          <img src="@/assets/images/mine/icons/transfer-icon.svg" @error="handleImageError($event, 'icon')" alt="转账" />
        </div>
        <div class="action-text">转账</div>
      </div>

      <div class="action-item" @click="goToTransactionRecord">
        <div class="action-icon transaction">
          <img src="@/assets/images/mine/icons/transaction-icon.svg" @error="handleImageError($event, 'icon')"
            alt="交易明细" />
        </div>
        <div class="action-text">资金明细</div>
      </div>
    </div> <!-- 常用功能 -->
    <div class="common-functions">
      <div class="section-title">常用功能</div>
      <div class="function-grid">
        <div class="function-item" @click="goToCloudPay">
          <div class="function-text">云支付</div>
          <div class="function-icon">
            <img src="@/assets/images/mine/icons/zf.svg" @error="handleImageError($event, 'icon')" alt="云支付" />
          </div>
        </div>

        <div class="function-item" @click="goToVerification">
          <div class="function-text">实名认证</div>
          <div class="function-icon">
            <img src="@/assets/images/mine/icons/sm.svg" @error="handleImageError($event, 'icon')" alt="实名认证" />
          </div>
        </div>

        <div class="function-item" @click="goToBankCard">
          <div class="function-text">银行卡绑定</div>
          <div class="function-icon">
            <img src="@/assets/images/mine/icons/bank.svg" @error="handleImageError($event, 'icon')" alt="银行卡绑定" />
          </div>
        </div>

        <div class="function-item" @click="goToGuide">
          <div class="function-text">新手攻略</div>
          <div class="function-icon">
            <img src="@/assets/images/mine/icons/gl.svg" @error="handleImageError($event, 'icon')" alt="新手攻略" />
          </div>
        </div>
      </div>
    </div>

    <!-- 更多功能 -->
    <div class="more-functions">
      <div class="section-title">更多功能</div>
      <div class="function-list">

        <div class="function-row" @click="goToMemberLevel">
          <div class="function-icon">
            <img src="@/assets/images/mine/icons/level.svg" @error="handleImageError($event, 'icon')" alt="我的收益" />
          </div>
          <span class="function-name">团队长等级</span>
          <svg width="5" height="10" viewBox="0 0 5 10" fill="none" class="arrow">
            <path d="M1 1L4 5L1 9" stroke="#999" stroke-width="1.5" />
          </svg>
        </div>

        <div class="function-row" @click="goToTeam">
          <div class="function-icon">
            <img src="@/assets/images/mine/icons/team.svg" @error="handleImageError($event, 'icon')" alt="我的团队" />
          </div>
          <span class="function-name">我的团队</span>
          <svg width="5" height="10" viewBox="0 0 5 10" fill="none" class="arrow">
            <path d="M1 1L4 5L1 9" stroke="#999" stroke-width="1.5" />
          </svg>
        </div>

        <div class="function-row" @click="goToIncome">
          <div class="function-icon">
            <img src="@/assets/images/mine/icons/shouyi.svg" @error="handleImageError($event, 'icon')" alt="我的收益" />
          </div>
          <span class="function-name">我的收益</span>
          <svg width="5" height="10" viewBox="0 0 5 10" fill="none" class="arrow">
            <path d="M1 1L4 5L1 9" stroke="#999" stroke-width="1.5" />
          </svg>
        </div>

        <div class="function-row" @click="goToIntegralDetail">
          <div class="function-icon">
            <img src="@/assets/images/mine/icons/jifen.svg" @error="handleImageError($event, 'icon')" alt="积分明细" />
          </div>
          <span class="function-name">积分明细</span>
          <svg width="5" height="10" viewBox="0 0 5 10" fill="none" class="arrow">
            <path d="M1 1L4 5L1 9" stroke="#999" stroke-width="1.5" />
          </svg>
        </div>

        <div class="function-row" @click="goToProjectOrder">
          <div class="function-icon">
            <img src="@/assets/images/mine/icons/dingdan.svg" @error="handleImageError($event, 'icon')" alt="理财订单" />
          </div>
          <span class="function-name">认购订单</span>
          <svg width="5" height="10" viewBox="0 0 5 10" fill="none" class="arrow">
            <path d="M1 1L4 5L1 9" stroke="#999" stroke-width="1.5" />
          </svg>
        </div>

        <div class="function-row" @click="goToMallOrder">
          <div class="function-icon">
            <img src="@/assets/images/mine/icons/shangchen.svg" @error="handleImageError($event, 'icon')" alt="商城订单" />
          </div>
          <span class="function-name">商城订单</span>
          <svg width="5" height="10" viewBox="0 0 5 10" fill="none" class="arrow">
            <path d="M1 1L4 5L1 9" stroke="#999" stroke-width="1.5" />
          </svg>
        </div>

        <div class="function-row" @click="goToAddress">
          <div class="function-icon">
            <img src="@/assets/images/mine/icons/dizhi.svg" @error="handleImageError($event, 'icon')" alt="收货地址" />
          </div>
          <span class="function-name">收货地址</span>
          <svg width="5" height="10" viewBox="0 0 5 10" fill="none" class="arrow">
            <path d="M1 1L4 5L1 9" stroke="#999" stroke-width="1.5" />
          </svg>
        </div>

        <div class="function-row" @click="goToChangePassword">
          <div class="function-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <rect x="3" y="11" width="18" height="10" rx="2" ry="2" fill="#4CAF50" />
              <circle cx="12" cy="7" r="4" stroke="#4CAF50" stroke-width="2" fill="none" />
              <circle cx="9" cy="16" r="1" fill="white" />
              <circle cx="12" cy="16" r="1" fill="white" />
              <circle cx="15" cy="16" r="1" fill="white" />
            </svg>
          </div>
          <span class="function-name">登录密码</span>
          <svg width="5" height="10" viewBox="0 0 5 10" fill="none" class="arrow">
            <path d="M1 1L4 5L1 9" stroke="#999" stroke-width="1.5" />
          </svg>
        </div>

        <div class="function-row" @click="goToFundPassword">
          <div class="function-icon">
            <img src="@/assets/images/mine/icons/jiaoyimima.svg" @error="handleImageError($event, 'icon')" alt="资金密码" />
          </div>
          <span class="function-name">资金密码</span>
          <svg width="5" height="10" viewBox="0 0 5 10" fill="none" class="arrow">
            <path d="M1 1L4 5L1 9" stroke="#999" stroke-width="1.5" />
          </svg>
        </div>

        <div class="function-row" @click="goToCustomerService">
          <div class="function-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <circle cx="12" cy="12" r="11" fill="#2C98FF" />
              <path
                d="M7 9.5C7 8.12 8.12 7 9.5 7h1.5v4H9.5C8.12 11 7 9.88 7 8.5V9.5zM17 9.5C17 8.12 15.88 7 14.5 7H13v4h1.5c1.38 0 2.5-1.12 2.5-2.5V9.5z"
                fill="white" />
              <path d="M8 12c0 4 4 4 4 4s4 0 4-4" stroke="white" stroke-width="2" stroke-linecap="round" />
            </svg>
          </div>
          <span class="function-name">客服</span>
          <svg width="5" height="10" viewBox="0 0 5 10" fill="none" class="arrow">
            <path d="M1 1L4 5L1 9" stroke="#999" stroke-width="1.5" />
          </svg>
        </div>

        <div class="function-row" @click="goToSettings">
          <div class="function-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <circle cx="12" cy="12" r="11" fill="#5A6C8F" />
              <circle cx="12" cy="12" r="3" fill="white" />
              <path
                d="M12 4V6M12 18v2M20 12h-2M6 12H4M18.36 5.64l-1.41 1.41M7.05 16.95l-1.41 1.41M18.36 18.36l-1.41-1.41M7.05 7.05L5.64 5.64"
                stroke="white" stroke-width="1.5" stroke-linecap="round" />
            </svg>
          </div>
          <span class="function-name">设置</span>
          <svg width="5" height="10" viewBox="0 0 5 10" fill="none" class="arrow">
            <path d="M1 1L4 5L1 9" stroke="#999" stroke-width="1.5" />
          </svg>
        </div>

      </div>
    </div>

    <tab-bar :active="4"></tab-bar>
  </div>
</template>

<script>
import TabBar from '@/components/TabBar.vue'
import ImageErrorHandler from '@/utils/ImageErrorHandler'

export default {
  name: 'Mine',
  components: {
    TabBar
  },
  // 路由导航守卫 - 进入页面前检查登录状态
  beforeRouteEnter(to, from, next) {
    const userToken = localStorage.getItem('user_token')
    if (!userToken) {
      // 没有token，跳转到登录页
      next('/login')
    } else {
      next()
    }
  },
  data() {
    return {
      userInfo: {
        mobile: '',
        level: 0,
        level_name: '普通会员',
        team_level: '无',
        avatar: '',
        nickname: '',
        balance: '0.00',
        integral: 0,
        withdraw_can: '0.00',
        total_amount: '0.00',
        total_income: '0.00',
        sms: false
      }
    }
  },
  created() {
    this.checkLoginAndLoadUserInfo()
  }, methods: {
    // 获取会员等级图标
    getLevelIcon(level) {
      try {
        // 我们知道目录中有SVG格式的等级图标
        return require(`@/assets/images/mine/icons/level${level}.svg`)
      } catch (error) {
        // 如果找不到特定等级的图标，返回默认图标
        console.warn(`未找到等级${level}的图标，使用默认图标替代`)
        return require('@/assets/images/mine/icons/level.svg')
      }
    },

    // 处理图片加载错误
    handleImageError(event, type) {
      ImageErrorHandler.handleImageError(event, type)
    },

    // 检查登录状态并加载用户信息
    checkLoginAndLoadUserInfo() {
      const userToken = localStorage.getItem('user_token')

      if (!userToken) {
        // 没有token，直接跳转到登录页
        this.redirectToLogin()
        return
      }

      // 有token，尝试加载用户信息
      this.loadUserInfo()
    },

    // 跳转到登录页面
    redirectToLogin() {
      this.$toast('请先登录')
      // 确保toast显示后再跳转
      setTimeout(() => {
        this.$router.replace('/login')
      }, 300)
    },

    // 加载用户信息
    loadUserInfo() {
      // 从localStorage获取token
      const userToken = localStorage.getItem('user_token')

      if (!userToken) {
        this.redirectToLogin()
        return
      }

      // 调用API接口获取用户信息
      this.$http.get('/member/getIndex', {
        headers: {
          'authorization': userToken
        }
      })
        .then(response => {
          if (response.data.code === 200) {
            this.userInfo = response.data.data.info
          } else {
            // API返回错误，可能是token过期
            if (response.data.code === 401 || response.data.code === 403) {
              // token过期或无效
              localStorage.removeItem('user_token')
              localStorage.removeItem('user_info')
              this.redirectToLogin()
            } else {
              this.$toast(response.data.msg || '获取用户信息失败')
            }
          }
        })
        .catch(error => {
          console.error('获取用户信息失败:', error)

          // 检查是否是认证错误
          if (error.response && (error.response.status === 401 || error.response.status === 403)) {
            // 认证失败，清除token并跳转登录
            localStorage.removeItem('user_token')
            localStorage.removeItem('user_info')
            this.redirectToLogin()
          } else {
            this.$toast('网络异常，请稍后重试')
          }
        })
    },

    // 快捷操作
    goToRecharge() {
      this.$router.push('/recharge')
    },
    goToWithdraw() {
      this.$router.push('/withdraw')
    },
    goToTransfer() {
      this.$router.push('/transfer')
    },
    goToTransactionRecord() {
      this.$router.push('/transaction-record')
    },

    // 常用功能
    goToCloudPay() {
      // 修改为跳转到充值页面
      this.$router.push('/recharge')
    },
    goToVerification() {
      this.$router.push('/verification')
    },

    // 新手指南 - 获取动态URL并在新窗口打开
    goToGuide() {
      const userToken = localStorage.getItem('user_token')

      if (!userToken) {
        this.redirectToLogin()
        return
      }

      // 调用API获取新手指南URL
      this.$http.get('/member/getPage', {
        params: {
          name: 'help1'
        },
        headers: {
          'authorization': userToken
        }
      })
        .then(response => {
          if (response.data.code === 200) {
            const guideUrl = response.data.data

            if (guideUrl && typeof guideUrl === 'string') {
              this.$router.push({
                path: '/guide',
                query: {
                  url: guideUrl
                }
              })
            } else {
              this.$toast('获取指南内容失败')
            }
          } else {
            this.$toast(response.data.msg || '获取新手指南失败')
          }
        })
        .catch(error => {
          console.error('获取新手指南失败:', error)
          this.$toast('网络异常，请稍后重试')
        })
    },
    goToBankCard() {
      this.$router.push('/bank-card/bind')
    },

    // 更多功能
    goToIncome() {
      this.$router.push('/income')
    },
    goToTeam() {
      this.$router.push('/team')
    },
    goToIntegralDetail() {
      this.$router.push('/integral-detail')
    },
    goToOrder() {
      this.$router.push('/order')
    },
    goToProjectOrder() {
      this.$router.push('/project-order')
    },
    goToMallOrder() {
      this.$router.push('/order')
    },
    goToAddress() {
      this.$router.push('/address')
    },
    goToChangePassword() {
      this.$router.push('/change-password')
    },
    goToFundPassword() {
      this.$router.push('/change-password-finance')
    },
    goToSettings() {
      this.$router.push('/settings')
    },
    goToMemberLevel() {
      this.$router.push('/member-level')
    },
    goToSignInCalendar() {
      this.$router.push('/sign-in-calendar')
    },
    goToCustomerService() {
      this.$router.push('/customer-service')
    }
  }
}
</script>

<style scoped>
.mine {
  min-height: 100vh;
  background: #F2F3F4;
  position: relative;
  padding-bottom: 90px;
  margin: 0;
  padding-top: 0;
}

/* 背景渐变 */
.bg-gradient {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 200px;
  background: linear-gradient(103.91deg, #EACFD8 4.98%, #50B9F9 27.83%, #0474FC 41.44%, #BAB8E0 80.93%, #95BAEB 98.32%);
  z-index: 0;
  margin: 0;
}

/* 头部区域 */
.header-section {
  position: relative;
  z-index: 10;
  padding: 0 16px;
  padding-top: 8px;
}

/* 页面标题 */
.page-title {
  position: relative;
  z-index: 10;
  text-align: center;
  font-family: 'PingFang HK', sans-serif;
  font-weight: 400;
  font-size: 16px;
  color: #000;
  margin-bottom: 15px;
}

/* 用户信息 */
.user-profile {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.avatar-container {
  flex-shrink: 0;
  margin-right: 15px;
}

.avatar {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  border: 2px solid #fff;
  object-fit: cover;
}

.user-info {
  position: relative;
  z-index: 10;
  text-align: left;
}

.username {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 16px;
  color: #333;
  margin-bottom: 4px;
}

.user-level {
  display: flex;
  align-items: center;
  padding: 4px 10px;
  border-radius: 15px;
  cursor: pointer;
}

.user-level img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  margin-right: 4px;
}

/* 账户信息卡片 */
.account-card {
  position: relative;
  z-index: 10;
  margin: 10px 16px 20px 16px;
  background: #fff;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.card-header {
  background: #fff;
  padding: 12px 15px;
  position: relative;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.account-title {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 600;
  font-size: 14px;
  color: #333;
}

/* 余额信息 */
.balance-section {
  padding: 15px;
}

.main-balance-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.main-balance-item {
  flex: 1;
}

.balance-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.balance-row:last-child {
  margin-bottom: 0;
}

.balance-item {
  flex: 1;
}

.balance-label {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

.balance-amount {
  font-family: 'DIN', sans-serif;
  font-weight: 700;
  font-size: 14px;
  color: #333;
}

.balance-amount.main {
  font-size: 22px;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  flex-direction: row;
  gap: 8px;
  flex-shrink: 0;
}

.action-btn {
  width: 50px;
  height: 28px;
  border-radius: 14px;
  border: none;
  font-family: 'Inter', sans-serif;
  font-weight: 500;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: #0474FC;
  color: #fff;
}

.action-btn.secondary {
  background: transparent;
  color: #0474FC;
  border: 1px solid #0474FC;
}

.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 快捷操作 */
.quick-actions {
  margin: 0 16px 10px 16px;
  background: #fff;
  border-radius: 10px;
  padding: 14px 25px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-item:hover {
  transform: translateY(-2px);
}

.action-icon {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 6px;
}

.action-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.action-text {
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  font-size: 12px;
  color: #333;
}

/* 常用功能 */
.common-functions {
  margin: 0 16px 10px 16px;
  background: #fff;
  border-radius: 12px;
  padding: 16px 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.section-title {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 600;
  font-size: 16px;
  color: #333;
  margin-bottom: 16px;
}

.function-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, auto);
  gap: 8px;
}

.function-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 8px 12px;
  border-radius: 8px;
  background: #E8EFFB;
  min-height: 48px;
}

.function-item:hover {
  transform: translateY(-1px);
  background: rgba(255, 97, 139, 0.08);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.function-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0;
  border-radius: 6px;
  background: #fff;
  flex-shrink: 0;
}

.function-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.function-text {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 13px;
  color: #333;
  text-align: left;
  flex: 1;
}

/* 更多功能 */
.more-functions {
  margin: 0 16px 20px 16px;
  background: #fff;
  border-radius: 10px;
  padding: 14px;
}

.function-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.function-row {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.function-row:hover {
  background: rgba(255, 97, 139, 0.05);
}

.function-row .function-icon {
  width: 24px;
  height: 24px;
  margin-right: 32px;
  margin-bottom: 0;
}

.function-name {
  flex: 1;
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #333;
}

.arrow {
  margin-left: auto;
}
</style>
