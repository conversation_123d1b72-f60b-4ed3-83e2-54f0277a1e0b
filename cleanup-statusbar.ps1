# 清理StatusBar组件导入和注册的PowerShell脚本

$vueFiles = Get-ChildItem "D:\work\work\shanzhili\h5code\src\views\*.vue"

foreach ($file in $vueFiles) {
    Write-Host "Processing: $($file.Name)"
    
    $content = Get-Content $file.FullName -Raw -Encoding UTF8
    
    # 删除StatusBar导入行
    $content = $content -replace "import StatusBar from '@/components/StatusBar\.vue'\r?\n", ""
    
    # 删除组件注册中的StatusBar（处理不同的格式）
    $content = $content -replace ",\s*StatusBar\s*,?\s*", ""
    $content = $content -replace "StatusBar\s*,\s*", ""
    $content = $content -replace "components:\s*\{\s*StatusBar\s*\}", "components: {}"
    
    # 清理空的组件注册
    $content = $content -replace "components:\s*\{\s*\}", ""
    
    Set-Content $file.FullName -Value $content -Encoding UTF8
}

Write-Host "StatusBar cleanup completed!"
