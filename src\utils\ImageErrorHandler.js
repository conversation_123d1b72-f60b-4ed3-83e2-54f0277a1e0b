/**
 * 图片错误处理工具
 * 当图片加载失败时，提供默认图片替代
 */
const ImageErrorHandler = {
    /**
     * 处理图片加载错误
     * @param {Event} event - 触发错误的事件对象
     * @param {string} type - 图片类型，用于选择合适的默认图片
     */
    handleImageError(event, type) {
        const defaultImages = {
            // 用户头像
            avatar: require('@/assets/user-avatar.png'),
            // 各类横幅图片
            banner: require('@/assets/images/default-banner.png'),
            // 产品/项目图片
            product: require('@/assets/images/default-product.png'),
            // 电子合同
            contract: require('@/assets/images/default-contract.png'),
            // 图标
            icon: require('@/assets/icons/default-icon.svg'),
            // 会员等级图标
            'level-icon': require('@/assets/images/team/level-icon.svg'),            // 团队图标
            'team-icon': require('@/assets/images/team/team-icon.svg'),
            // 收益图标
            'income-icon': require('@/assets/images/income/income-icon.svg'),
            // 装饰性图片元素
            decorative: require('@/assets/images/mine/decorative-elements.png'),
            // 通用默认图片
            default: require('@/assets/images/default-image.png')
        }

        // 设置默认图片
        event.target.src = defaultImages[type] || defaultImages.default
    }
}

export default ImageErrorHandler
