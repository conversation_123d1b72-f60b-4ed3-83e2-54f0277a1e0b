<template>
  <div class="transfer-detail">
    <!-- 头部 -->
    <div class="header">
      <div class="back-icon" @click="goBack">
        <img src="@/assets/icons/back-icon.svg" alt="返回">
      </div>
      <div class="title">转账详细</div>
    </div>

    <!-- 状态筛选 -->
    <div class="status-filter">
      <div class="status-item" v-for="(item, index) in statusOptions" :key="index"
        :class="{ active: selectedStatus === item.value }" @click="selectStatus(item.value)">
        {{ item.text }}
      </div>
    </div>

    <!-- 详细列表 -->
    <div class="detail-list">
      <div class="detail-item" v-for="(item, index) in records" :key="index">
        <div class="detail-header">
          <div class="detail-title">转账</div>
          <div class="detail-status" :class="getStatusClass(item.status)">{{ getStatusText(item.status) }}</div>
        </div>
        <div class="detail-content">
          <div class="detail-amount">-{{ item.amount }}元</div>
          <div class="detail-time">{{ item.create_time }}</div>
        </div>
        <div class="detail-info">
          <div class="detail-receiver">
            <span class="label">收款人：</span>
            <span class="value">{{ item.kyc_name }}</span>
          </div>
          <div class="detail-mobile">
            <span class="label">手机号：</span>
            <span class="value">{{ item.mobile }}</span>
          </div>
        </div>
        <div class="detail-footer">
          <div class="detail-order">订单号：{{ item.order_id }}</div>
        </div>
      </div>

      <!-- 加载更多 -->
      <div class="loading-more" v-if="loading">
        <van-loading size="24px" color="#0474FC ">加载中..</van-loading>
      </div>

      <!-- 空状态 -->
      <div class="empty-state" v-if="records.length === 0 && !loading">
        <van-empty description="暂无转账记录" />
      </div>

      <!-- 没有更多数据 -->
      <div class="no-more" v-if="!hasMore && records.length > 0">
        没有更多数据了
      </div>
    </div>
  </div>
</template>

<script>

export default {
  name: 'TransferDetail',
  data() {
    return {
      page: 1,
      loading: false,
      hasMore: true,
      records: [],
      observerTarget: null,
      selectedStatus: 0,
      statusOptions: [
        { text: '全部', value: 0 },
        { text: '未处理', value: 1 },
        { text: '成功', value: 2 },
        { text: '失败', value: 3 }
      ]
    }
  },
  created() {
    this.fetchTransferLog()
  },
  mounted() {
    // 创建一个IntersectionObserver 实例，用于检测列表底部并触发加载更多
    this.observerTarget = document.createElement('div')
    document.querySelector('.detail-list').appendChild(this.observerTarget)

    const observer = new IntersectionObserver(entries => {
      // 当目标元素进入视口时，自动加载更多数据
      if (entries[0].isIntersecting && this.hasMore && !this.loading) {
        this.loadMore()
      }
    }, { threshold: 0.1 })

    observer.observe(this.observerTarget)

    // 记录观察器以便在组件销毁时断开连接
    this.observer = observer
  },
  beforeDestroy() {
    // 清理 IntersectionObserver
    if (this.observer && this.observerTarget) {
      this.observer.unobserve(this.observerTarget)
      this.observer.disconnect()
    }
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    getStatusText(status) {
      // 映射状态码：1=未处理，2=成功，3=失败
      const statusMap = {
        1: '未处理',
        2: '成功',
        3: '失败'
      }
      return statusMap[status] || '未知'
    },
    getStatusClass(status) {
      // 映射状态码对应的CSS类 1=processing, 2=success, 3=failed
      const classMap = {
        1: 'processing',
        2: 'success',
        3: 'failed'
      }
      return classMap[status] || ''
    },
    async fetchTransferLog() {
      try {
        this.loading = true
        const response = await this.$http.get(`/finance/getTransferLog?page=${this.page}&status=${this.selectedStatus}`, {
          headers: {
            'authorization': localStorage.getItem('user_token') || ''
          }
        })
        if (response.data.code === 200) {
          const data = response.data.data

          // 添加新数据到列表
          if (data && data.list && Array.isArray(data.list)) {
            this.records = [...this.records, ...data.list]

            // 判断是否还有更多数据
            if (data.list.length < 10 || !data.list.length) {
              this.hasMore = false
            }
          } else {
            console.error('API返回数据格式不正确', data)
            this.hasMore = false
          }
        } else {
          this.$toast(response.data.msg || '获取数据失败')
        }
      } catch (error) {
        console.error('获取转账记录失败:', error)
        this.$toast('获取数据失败，请稍后再试')
      } finally {
        this.loading = false
      }
    },
    loadMore() {
      if (!this.loading && this.hasMore) {
        this.page += 1
        this.fetchTransferLog()
      }
    },

    selectStatus(status) {
      if (this.selectedStatus === status) return

      // 重置分页和数据
      this.selectedStatus = status
      this.page = 1
      this.records = []
      this.hasMore = true

      // 重新加载数据
      this.fetchTransferLog()
    }
  }
}
</script>

<style scoped>
.transfer-detail {
  min-height: 100vh;
  background-color: #f6f6f6;
}

.header {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  background-color: #fff;
  position: relative;
}

.back-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon img {
  width: 20px;
  height: 20px;
}

.title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

.status-filter {
  display: flex;
  background-color: #fff;
  padding: 10px 16px;
  margin-bottom: 10px;
  overflow-x: auto;
}

.status-item {
  min-width: 70px;
  height: 30px;
  background-color: #f5f5f5;
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #666;
  margin-right: 10px;
  padding: 0 15px;
}

.status-item.active {
  background-color: #0474FC;
  color: #fff;
}

.detail-list {
  padding: 0 16px;
  padding-bottom: 50px;
}

.detail-item {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 10px;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.detail-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.detail-status {
  font-size: 14px;
}

.detail-status.success {
  color: #52C41A;
}

.detail-status.processing {
  color: #FF9500;
}

.detail-status.failed {
  color: #F5222D;
}

.detail-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.detail-amount {
  font-size: 18px;
  font-weight: 500;
  color: #0474FC;
}

.detail-time {
  font-size: 12px;
  color: #999;
}

.detail-info {
  margin-bottom: 10px;
}

.detail-receiver,
.detail-mobile {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.detail-receiver:last-child,
.detail-mobile:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 14px;
  color: #666;
  width: 60px;
  flex-shrink: 0;
}

.value {
  font-size: 14px;
  color: #333;
}

.detail-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.detail-order {
  font-size: 12px;
  color: #999;
}

.empty-state {
  padding: 50px 0;
}

.loading-more {
  text-align: center;
  padding: 15px 0;
}

.no-more {
  text-align: center;
  color: #999;
  font-size: 12px;
  padding: 15px 0;
}
</style>
