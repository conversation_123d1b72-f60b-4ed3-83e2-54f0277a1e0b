# 充值页面测试指南

## 功能验证清单

### 1. 页面初始化功能测试
- [ ] 打开充值页面，检查是否自动调用 `/finance/getDeposit` 接口
- [ ] 验证账户余额是否从API更新 
- [ ] 检查金额选择列表是否使用API返回的 `amount_list`
- [ ] 确认充值说明是否显示API返回的 `des` 内容

### 2. 定时更新功能测试  
- [ ] 页面停留1分钟，观察是否自动调用更新接口
- [ ] 检查余额等信息是否实时更新

### 3. 支付渠道选择测试
- [ ] 输入充值金额，点击"下一步"
- [ ] 验证是否调用 `/finance/getDepositPayment?amount=xxx` 接口
- [ ] 检查支付渠道列表是否正确显示

### 4. 支付流程分支测试

#### 4.1 Type=1 支付渠道（内部页面）
- [ ] 选择 type=1 的支付渠道，点击"确认支付"
- [ ] 验证是否跳转到 `/recharge-submit` 页面
- [ ] 检查页面是否正确显示充值金额、渠道ID等信息
- [ ] 在提交页面点击"确认支付"，验证是否调用 `/finance/getPayment?id=xxx&amount=xxx&type=1`
- [ ] 测试支付结果弹窗的显示和交互

#### 4.2 其他Type支付渠道（外部链接）
- [ ] 选择 type≠1 的支付渠道，点击"确认支付"
- [ ] 验证是否调用 `/finance/getPayment?id=xxx&amount=xxx&type=xxx` 接口
- [ ] 测试以下场景：
  - **成功场景 (code: 200)**：检查是否打开新窗口到支付链接
  - **失败场景 (其他code)**：检查是否显示API返回的错误msg

### 5. 错误处理测试
- [ ] 网络错误：断网状态下测试各个接口调用
- [ ] 认证错误：删除token后测试是否正确跳转登录页
- [ ] 参数错误：测试无效金额、空金额等边界情况
- [ ] 服务器错误：模拟500错误响应

### 6. 用户交互测试
- [ ] 支付确认对话框：测试确认和取消操作
- [ ] 支付窗口监听：测试支付窗口关闭后的处理逻辑
- [ ] 页面生命周期：测试页面离开时定时器的清理

## API接口格式

### 1. 获取充值信息 - GET `/finance/getDeposit`
```json
// 请求头
{
  "authorization": "user_token"
}

// 响应格式
{
  "code": 200,
  "msg": "success", 
  "data": {
    "balance": "11754.99",
    "kyc": true,
    "amount_list": [500, 1000, 2000, 3000, 5000, 10000, 20000, 50000],
    "des": [
      "充值说明",
      "1.单笔充值金额不得低于500元",
      "2.充值时间：周一至周日/24小时",
      "3.使用网银或手机银行付款金额必须与提交的充值金额一致",
      "（如：充值界面输入500元，则付款金额也必须是500元）",
      "4.如有其他疑问，请咨询在线客服。"
    ]
  }
}
```

### 2. 获取支付渠道 - GET `/finance/getDepositPayment?amount=500`
```json
// 请求头
{
  "authorization": "user_token"
}

// 响应格式
{
  "code": 200,
  "msg": "success",
  "data": {
    "payment": [
      {
        "id": "1",
        "title": "微信支付",
        "type": 1,
        "icon": "wechat_icon_url"
      },
      {
        "id": "2", 
        "title": "支付宝",
        "type": 2,
        "icon": "alipay_icon_url"
      },
      {
        "id": "3",
        "title": "银联支付", 
        "type": 3,
        "icon": "unionpay_icon_url"
      }
    ]
  }
}
```

### 3. 支付处理 - GET `/finance/getPayment?id=1&amount=500&type=1`
```json
// 请求头
{
  "authorization": "user_token"
}

// 响应格式 (type=1 内部处理)
{
  "code": 200,
  "msg": "success",
  "data": "order_id_or_success_message"
}

// 响应格式 (其他type 外部链接)
{
  "code": 200,
  "msg": "success", 
  "data": "https://payment.example.com/pay?order=xxx"
}

// 错误响应
{
  "code": 400,
  "msg": "充值失败：余额不足或其他错误信息",
  "data": null
}
```

## 完整测试流程

### 场景1：Type=1支付渠道测试
1. 打开充值页面 `/recharge`
2. 选择金额（如500元）
3. 点击"下一步"
4. 在弹窗中选择type=1的支付渠道
5. 点击"确认支付"
6. 确认跳转到 `/recharge-submit` 页面
7. 在提交页面点击"确认支付"
8. 验证API调用和结果处理

### 场景2：外部支付链接测试  
1. 打开充值页面 `/recharge`
2. 选择金额（如1000元）
3. 点击"下一步"
4. 在弹窗中选择type≠1的支付渠道
5. 点击"确认支付"
6. 确认打开新窗口到支付链接
7. 验证支付窗口关闭后的处理

### 场景3：错误处理测试
1. 测试网络异常情况
2. 测试登录状态失效
3. 测试API返回错误
4. 测试无效参数输入

## 注意事项

1. **测试环境**：确保后端API接口已部署且可访问
2. **Token管理**：测试前确保已登录并获取有效token
3. **浏览器设置**：确保浏览器允许弹出窗口（测试支付链接功能）
4. **控制台日志**：观察浏览器控制台的网络请求和错误日志
5. **移动端测试**：在不同设备尺寸下测试响应式布局
  }
}
```

### 2. 获取支付渠道 - GET `/finance/getDepositPayment?amount=500`
```json
// 请求头  
{
  "authorization": "user_token"
}

// 响应格式
{
  "code": 200,
  "msg": "success",
  "data": {
    "payment": [
      {
        "id": 265,
        "type": 2, 
        "title": "新支付宝快捷专线五2（单笔2000-50000）"
      }
    ]
  }
}
```

### 3. 确认支付 - GET `/finance/getPayment?id=252&amount=500`
```json
// 请求头
{
  "authorization": "user_token"
}

// 成功响应
{
  "code": 200,
  "msg": "success", 
  "data": "https://www.figma.com/design/"
}

// 失败响应
{
  "code": 400,
  "msg": "商户已被禁用",
  "data": "https://www.figma.com/design/"
}
```

## 测试注意事项

1. **登录状态**：确保localStorage中有有效的user_token
2. **网络环境**：确保能正常访问 http://api.43bt.com
3. **实名认证**：支付功能需要kyc状态为true
4. **浏览器支持**：确认浏览器支持window.open打开新窗口

## 问题排查

### 常见问题
- **API调用失败**：检查token是否有效，网络是否畅通
- **页面数据不更新**：检查API返回格式是否正确
- **支付窗口未打开**：检查浏览器是否阻止弹窗
- **定时器不工作**：检查控制台是否有错误信息

### 调试技巧
1. 打开浏览器开发者工具，查看Network面板的API请求
2. 检查Console面板的错误信息
3. 验证localStorage中的user_token是否存在
