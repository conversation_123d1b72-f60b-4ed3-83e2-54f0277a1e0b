<template>
  <div class="home">
    <!-- 顶部背景图 -->
    <div class="top-bg">
      <img src="@/assets/images/home/<USER>" alt="首页背景" />
    </div>

    <!-- 大图banner区域 -->
    <div class="main-banner">
      <div class="banner-container" v-if="myswiper && myswiper.length > 0">
        <div class="banner-swipe">
          <div class="banner-slides"
            :style="{ transform: `translateX(${-currentSlide * 100}%)`, width: `${myswiper.length * 100}%` }">
            <div class="banner-slide" v-for="(image, index) in myswiper" :key="index" @click="handleBannerClick(image)">
              <img :src="image.pic" :alt="image.title || '轮播图'" class="banner-img" @load="onImageLoad"
                @error="onImageError" />
            </div>
          </div>
          <div class="banner-indicators" v-if="myswiper.length > 1">
            <span class="indicator" v-for="(item, index) in myswiper" :key="index"
              :class="{ active: index === currentSlide }" @click="goToSlide(index)"></span>
          </div>
        </div>
      </div>
    </div>

    <!-- 功能快捷入口 -->
    <div class="quick-actions">
      <div class="action-item" @click="goToPage('/sign-in-calendar')">
        <div class="action-icon">
          <div class="icon-bg">
            <img src="@/assets/images/home/<USER>" alt="每日签到" width="30" height="30" />
          </div>
        </div>
        <div class="action-text">每日签到</div>
      </div>

      <div class="action-item" @click="goToPage('/sign-in')">
        <div class="action-icon">
          <div class="icon-bg">
            <img src="@/assets/images/home/<USER>" alt="转盘抽奖" width="30" height="30" />
          </div>
        </div>
        <div class="action-text">转盘抽奖</div>
      </div>

      <div class="action-item" @click="goToPage('/express')">
        <div class="action-icon">
          <div class="icon-bg">
            <img src="@/assets/images/home/<USER>" alt="快递查询" width="30" height="30" />
          </div>
        </div>
        <div class="action-text">快递查询</div>
      </div>

      <div class="action-item" @click="goToPage('/invite')">
        <div class="action-icon">
          <div class="icon-bg">
            <img src="@/assets/images/home/<USER>" alt="邀请好友" width="30" height="30" />
          </div>
        </div>
        <div class="action-text">邀请好友</div>
      </div>
    </div>

    <!-- 立即加入按钮 -->
    <div class="join-section">
      <button class="join-btn" @click="goToPage('/power')">
        立即加入
      </button>
    </div>

    <!-- 专属服务标题 -->
    <div class="section-title">
      <span>专属服务</span>
    </div>

    <!-- 快捷服务卡片 -->
    <div class="service-cards">
      <div class="service-card" @click="goToPage('/team')">
        <div class="card-content">
          <h5 class="card-title">我的团队</h5>
          <p class="card-subtitle">立即参与</p>
        </div>
      </div>

      <div class="service-card" @click="goToPage('/customer-service')">
        <div class="card-content">
          <h5 class="card-title">在线客服</h5>
          <p class="card-subtitle">立即沟通</p>
        </div>
      </div>
    </div>

    <!-- 团队展示标题 -->
    <div class="section-title">
      <span>团队展示</span>
    </div>

    <!-- 慈善项目卡片（横向滑动） -->
    <div class="charity-section">
      <div class="charity-scroll-container" @touchstart="onCharityTouchStart" @touchmove="onCharityTouchMove"
        @touchend="onCharityTouchEnd">
        <div class="charity-cards-wrapper" :style="{ transform: `translateX(${charityScrollOffset}px)` }">
          <div class="charity-card" :class="index === 0 ? 'main-card' : 'side-card'" v-for="(item, index) in hot"
            :key="index" @click="handleActivityClick(item)" v-if="hot && hot.length > 0">
            <div class="charity-image">
              <img :src="item.pic" :alt="item.title" />
            </div>
            <div class="charity-content">
              <h4 class="charity-title">{{ item.title }}</h4>
              <p class="charity-amount" v-if="index === 0 && item.description">{{ item.description }}</p>
            </div>
            <button class="charity-btn" v-if="index === 0" @click.stop="handleActivityClick(item)">
              立即参与
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 新闻资讯标题 -->
    <div class="section-title">
      <span>新闻资讯</span>
      <span class="more-link" @click="goToPage('/news')">查看更多 ></span>
    </div>

    <!-- 新闻资讯卡片区域 -->
    <div class="news-section">
      <div class="news-item" v-for="(item, index) in randomNews" :key="index" @click="handleNewsClick(item)"
        v-if="randomNews && randomNews.length > 0">
        <div class="news-content">
          <h3 class="news-title">{{ item.title }}</h3>
          <span class="news-date">{{ formatDate(item.created_at) }}</span>
        </div>
        <div class="news-image">
          <img :src="item.pic" :alt="item.title" />
        </div>
      </div>
    </div>

    <!-- 浮动头像 -->
    <div class="floating-avatar" :style="floatingStyle" @touchstart="onTouchStart" @touchmove="onTouchMove"
      @touchend="onTouchEnd" @click="goToPage('/customer-service')">
      <img src="@/assets/images/home/<USER>" alt="用户头像" />
    </div>

    <!-- 通知弹窗 -->
    <div class="notice-modal" v-if="showNoticeModal && notice && notice.title" @click="closeNoticeModal">
      <div class="modal-overlay"></div>
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <div class="notice-icon">
            <img src="@/assets/images/home/<USER>" alt="通知图标" width="36" height="36" />
          </div>
          <div class="close-btn" @click="closeNoticeModal">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M18 6L6 18M6 6L18 18" stroke="#9CA3AF" stroke-width="1.5" stroke-linecap="round"
                stroke-linejoin="round" />
            </svg>
          </div>
        </div>
        <div class="modal-title">
          {{ notice.title }}
        </div>
        <div class="modal-body">
          <div class="notice-content" v-html="notice.content"></div>
        </div>
        <div class="modal-footer">
          <button class="confirm-btn" @click="closeNoticeModal">我知道了</button>
        </div>
      </div>
    </div>

    <!-- 底部导航 -->
    <tab-bar :active="0"></tab-bar>
  </div>
</template>

<script>
import TabBar from '@/components/TabBar.vue'

export default {
  name: 'Home',
  components: {
    TabBar
  },
  data() {
    return {
      // API数据
      myswiper: [], // 轮播图数据
      newsList: [], // 新闻列表
      randomNews: [], // 随机选择的新闻
      hot: [], // 慈善项目/活动数据
      notice: {}, // 通知数据
      lobbyInfo: null, // 首页数据

      // 弹窗状态
      showNoticeModal: false,

      // 轮播图相关
      currentSlide: 0,
      slideTimer: null,

      // 浮动头像位置
      floatingPosition: {
        x: window.innerWidth - 70,
        y: window.innerHeight / 2 - 50
      },
      isDragging: false,
      startPosition: { x: 0, y: 0 },
      initialPosition: { x: 0, y: 0 },

      // 慈善项目滑动相关
      charityScrollOffset: 0,
      charityStartX: 0,
      charityCurrentX: 0,
      charityIsDragging: false
    }
  },
  created() {
    // 获取首页数据
    this.getLobbyInfo()
  },
  mounted() {
    this.initFloatingPosition()
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    this.stopSlideShow()
    window.removeEventListener('resize', this.handleResize)
  },
  computed: {
    floatingStyle() {
      return {
        left: this.floatingPosition.x + 'px',
        top: this.floatingPosition.y + 'px'
      }
    }
  },
  methods: {
    goToPage(path) {
      this.$router.push(path)
    },

    // 获取首页数据
    getLobbyInfo() {
      // 检查是否有token
      const userToken = localStorage.getItem('user_token')

      if (!userToken) {
        console.log('无token，跳转登录页')
        this.$router.replace('/login')
        return
      }

      // 发送API请求
      this.$http.get('/lobby/getInfo', {
        headers: {
          'authorization': userToken
        }
      })
        .then(response => {
          console.log('首页数据获取成功:', response.data)
          if (response.data.code === 200) {
            this.lobbyInfo = response.data.data

            // 处理轮播图数据
            if (response.data.data.swiper) {
              this.myswiper = response.data.data.swiper
              console.log('轮播图数据:', this.myswiper)
              // 确保轮播图数据加载后启动自动轮播
              this.$nextTick(() => {
                this.startSlideShow()
              })
            }

            // 处理新闻数据
            if (response.data.data.news) {
              this.newsList = response.data.data.news
              this.randomNews = this.getRandomNews(this.newsList, 2)
              console.log('新闻数据:', this.newsList)
            }

            // 处理活动/慈善项目数据
            if (response.data.data.hot) {
              this.hot = response.data.data.hot
              console.log('活动数据:', this.hot)
            }

            // 处理通知数据
            if (response.data.data.notice) {
              this.notice = response.data.data.notice
              // 如果有通知内容，显示弹窗
              if (this.notice.title && this.notice.content) {
                this.showNoticeModal = true
              }
            }
          } else {
            console.error('API返回错误:', response.data.message)
            if (response.data.code === 401 || response.data.code === 403) {
              localStorage.removeItem('user_token')
              localStorage.removeItem('user_info')
              this.$router.replace('/login')
            }
          }
        })
        .catch(error => {
          console.error('获取首页数据错误:', error)
          if (error.response && (error.response.status === 401 || error.response.status === 403)) {
            localStorage.removeItem('user_token')
            localStorage.removeItem('user_info')
            this.$router.replace('/login')
          }
        })
    },

    // 从数组中随机选择指定数量的元素
    getRandomNews(array, count) {
      if (!array || array.length === 0) return []
      if (array.length <= count) return [...array]

      const copyArray = [...array]
      const result = []

      for (let i = 0; i < count; i++) {
        const randomIndex = Math.floor(Math.random() * copyArray.length)
        result.push(copyArray[randomIndex])
        copyArray.splice(randomIndex, 1)
      }

      return result
    },

    // 处理轮播图点击
    handleBannerClick(image) {
      if (image.url && image.url.trim() !== '') {
        if (image.url.startsWith('http://') || image.url.startsWith('https://')) {
          window.open(image.url, '_blank')
        } else {
          this.$router.push(image.url)
        }
      }
    },

    // 处理新闻点击
    handleNewsClick(news) {
      if (news.url && news.url.trim() !== '') {
        if (news.url.startsWith('http://') || news.url.startsWith('https://')) {
          window.open(news.url, '_blank')
        } else {
          this.$router.push(news.url)
        }
      }
    },

    // 处理活动点击
    handleActivityClick(activity) {
      this.$router.push('/power')
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    },

    // 轮播图控制方法
    startSlideShow() {
      if (this.myswiper && this.myswiper.length > 1) {
        this.slideTimer = setInterval(() => {
          this.nextSlide()
        }, 3000)
      }
    },

    stopSlideShow() {
      if (this.slideTimer) {
        clearInterval(this.slideTimer)
        this.slideTimer = null
      }
    },

    nextSlide() {
      if (this.myswiper && this.myswiper.length > 1) {
        this.currentSlide = (this.currentSlide + 1) % this.myswiper.length
      }
    },

    goToSlide(index) {
      this.currentSlide = index
      this.stopSlideShow()
      this.startSlideShow()
    },

    // 图片加载处理
    onImageLoad(event) {
      console.log('轮播图加载成功:', event.target.src)
    },

    onImageError(event) {
      console.error('轮播图加载失败:', event.target.src)
      // 可以设置默认图片
      event.target.src = '@/assets/images/home/<USER>'
    },

    // 初始化浮动头像位置
    initFloatingPosition() {
      const screenWidth = window.innerWidth
      const screenHeight = window.innerHeight

      this.floatingPosition = {
        x: screenWidth - 70,
        y: Math.max(100, screenHeight / 2 - 30)
      }
    },

    // 处理窗口大小变化
    handleResize() {
      const screenWidth = window.innerWidth
      const screenHeight = window.innerHeight

      this.floatingPosition.x = Math.min(this.floatingPosition.x, screenWidth - 70)
      this.floatingPosition.y = Math.min(this.floatingPosition.y, screenHeight - 70)
    },

    // 触摸开始
    onTouchStart(e) {
      e.preventDefault()
      this.isDragging = true
      const touch = e.touches[0]
      this.startPosition = {
        x: touch.clientX,
        y: touch.clientY
      }
      this.initialPosition = {
        x: this.floatingPosition.x,
        y: this.floatingPosition.y
      }
    },

    // 触摸移动
    onTouchMove(e) {
      e.preventDefault()
      if (!this.isDragging) return

      const touch = e.touches[0]
      const deltaX = touch.clientX - this.startPosition.x
      const deltaY = touch.clientY - this.startPosition.y

      const newX = this.initialPosition.x + deltaX
      const newY = this.initialPosition.y + deltaY

      const maxX = window.innerWidth - 60
      const maxY = window.innerHeight - 60

      this.floatingPosition.x = Math.max(0, Math.min(newX, maxX))
      this.floatingPosition.y = Math.max(0, Math.min(newY, maxY))
    },

    // 触摸结束
    onTouchEnd(e) {
      e.preventDefault()
      this.isDragging = false

      const touch = e.changedTouches[0]
      const deltaX = Math.abs(touch.clientX - this.startPosition.x)
      const deltaY = Math.abs(touch.clientY - this.startPosition.y)

      // 如果移动距离小于10px，认为是点击事件
      if (deltaX < 10 && deltaY < 10) {
        setTimeout(() => {
          this.goToPage('/customer-service')
        }, 100)
      }
    },

    // 慈善项目滑动触摸开始
    onCharityTouchStart(e) {
      this.charityIsDragging = true
      this.charityStartX = e.touches[0].clientX
      this.charityCurrentX = this.charityScrollOffset
    },

    // 慈善项目滑动触摸移动
    onCharityTouchMove(e) {
      if (!this.charityIsDragging) return
      e.preventDefault()

      const currentX = e.touches[0].clientX
      const deltaX = currentX - this.charityStartX
      const newOffset = this.charityCurrentX + deltaX

      // 动态计算滑动范围
      const containerElement = document.querySelector('.charity-scroll-container')
      const wrapperElement = document.querySelector('.charity-cards-wrapper')

      if (!containerElement || !wrapperElement) return

      const containerWidth = containerElement.offsetWidth
      const wrapperWidth = wrapperElement.scrollWidth

      // 计算最小偏移量：确保最后的内容能完全显示
      const minOffset = containerWidth >= wrapperWidth ? 0 : -(wrapperWidth - containerWidth)

      // 限制滑动范围
      const maxOffset = 0

      this.charityScrollOffset = Math.max(minOffset, Math.min(maxOffset, newOffset))
    },

    // 慈善项目滑动触摸结束
    onCharityTouchEnd(e) {
      this.charityIsDragging = false

      // 使用DOM元素动态计算，支持任意数量的卡片
      const containerElement = document.querySelector('.charity-scroll-container')
      const wrapperElement = document.querySelector('.charity-cards-wrapper')

      if (!containerElement || !wrapperElement) return

      const containerWidth = containerElement.offsetWidth
      const wrapperWidth = wrapperElement.scrollWidth

      // 如果内容宽度小于等于容器宽度，不需要滑动
      if (wrapperWidth <= containerWidth) {
        this.charityScrollOffset = 0
        return
      }

      const minOffset = -(wrapperWidth - containerWidth)
      const maxOffset = 0

      // 简单的边界检查和吸附
      if (this.charityScrollOffset > maxOffset) {
        this.charityScrollOffset = maxOffset
      } else if (this.charityScrollOffset < minOffset) {
        this.charityScrollOffset = minOffset
      }

      // 可选：添加卡片级别的吸附效果
      const cardElements = document.querySelectorAll('.charity-card')
      if (cardElements.length > 0) {
        const cardWidth = cardElements[0].offsetWidth
        const gap = 12
        const snapDistance = cardWidth + gap

        // 计算最接近的吸附点
        const snapIndex = Math.round(Math.abs(this.charityScrollOffset) / snapDistance)
        let targetOffset = -snapIndex * snapDistance

        // 确保在边界内
        targetOffset = Math.max(minOffset, Math.min(maxOffset, targetOffset))

        this.charityScrollOffset = targetOffset
      }
    },

    // 关闭通知弹窗
    closeNoticeModal() {
      this.showNoticeModal = false
    }
  }
}
</script>

<style scoped>
.home {
  min-height: 100vh;
  background: #F7F7F7;
  position: relative;
  padding: 0 0 80px 0;
}

/* 顶部背景图 */
.top-bg {
  display: none;
}

.top-bg img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 主要banner区域 */
.main-banner {
  position: relative;
  z-index: 1;
  width: 100%;
  height: 200px;
  margin-top: 0;
  margin-bottom: -40px;
  overflow: hidden;
  padding: 0;
}

.banner-container {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  background: #f8f8f8;
  border-radius: 0;
  margin: 0;
  box-shadow: none;
}

.banner-swipe {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  border-radius: 0;
}

.banner-slides {
  display: flex;
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease;
  flex-wrap: nowrap;
}

.banner-slide {
  width: 100%;
  height: 100%;
  cursor: pointer;
  flex-shrink: 0;
  overflow: hidden;
  position: relative;
  border-radius: 0;
}

.banner-img {
  object-fit: cover;
  object-position: center;
  display: block;
  user-select: none;
  -webkit-user-drag: none;
}

.banner-indicators {
  position: absolute;
  bottom: 16px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8px;
  z-index: 2;
}

.indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: background 0.3s ease;
}

.indicator.active {
  background: #EA555F;
}

.banner-image {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.banner-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 快捷功能区域 */
.quick-actions {
  display: flex;
  justify-content: space-around;
  align-items: center;
  background: #FFFFFF;
  border-radius: 12px;
  margin: -40px 16px 20px;
  padding: 16px 8px;
  position: relative;
  z-index: 2;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  flex: 1;
}

.action-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}

.icon-bg {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}



.action-text {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 12px;
  color: #333333;
  text-align: center;
}

/* 立即加入按钮 */
.join-section {
  text-align: center;
  margin: 0 16px 24px;
  position: relative;
  z-index: 2;
}

.join-btn {
  background: #3575FF;
  border: none;
  border-radius: 25px;
  padding: 12px 40px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 600;
  font-size: 16px;
  color: #FFFFFF;
  cursor: pointer;
  box-shadow: 0 4px 20px rgba(52, 154, 240, 0.3);
  width: 100%;
  max-width: 280px;
}

/* 区块标题 */
.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0 16px 16px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 600;
  font-size: 18px;
  color: #333333;
}

.more-link {
  font-size: 14px;
  color: #999999;
  font-weight: 400;
  cursor: pointer;
}

/* 快捷服务卡片 */
.service-cards {
  display: flex;
  gap: 8px;
  margin: 0 16px 24px;
  position: relative;
  z-index: 2;
}

.service-card {
  background: #FFFFFF;
  border-radius: 8px;
  padding: 16px 12px;
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.service-card:active {
  transform: translateY(1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-content {
  text-align: left;
  flex: 1;
}

.card-title {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 600;
  font-size: 14px;
  color: #333333;
  margin-bottom: 3px;
  line-height: 1.3;
}

.card-subtitle {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 11px;
  color: #999999;
  line-height: 1.2;
}

/* 慈善项目区域 */
.charity-section {
  margin: 0 0 24px;
  position: relative;
  z-index: 2;
}

.charity-scroll-container {
  overflow: hidden;
  padding-left: 16px;
  padding-right: 0;
  position: relative;
}

.charity-cards-wrapper {
  display: flex;
  gap: 12px;
  transition: transform 0.3s ease;
  padding-right: 16px;
  width: max-content;
}

.charity-card {
  background: #1F0E0E;
  border-radius: 12px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  min-height: 140px;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  flex-shrink: 0;
}

.main-card {
  width: 70vw;
  min-width: 260px;
}

.side-card {
  width: 70vw;
  min-width: 120px;
}

.charity-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.charity-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.charity-content {
  position: relative;
  z-index: 2;
  margin-bottom: 12px;
}

.charity-title {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 600;
  font-size: 14px;
  color: #FFFFFF;
  margin-bottom: 6px;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

.charity-amount {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 12px;
  color: #E0E0E0;
}

.charity-btn {
  background: linear-gradient(135deg, #51CF66 0%, #40C057 100%);
  border: none;
  border-radius: 20px;
  padding: 8px 16px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 12px;
  color: #FFFFFF;
  cursor: pointer;
  position: relative;
  z-index: 2;
  align-self: flex-start;
}

/* 新闻资讯区域 */
.news-section {
  margin: 0 16px 24px;
  position: relative;
  z-index: 2;
}

.news-item {
  background: #FFFFFF;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.news-content {
  flex: 1;
  margin-right: 12px;
}

.news-title {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 1.4;
  color: #333333;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.news-date {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 12px;
  color: #999999;
}

.news-image {
  width: 80px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.news-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 浮动头像 */
.floating-avatar {
  position: fixed;
  width: 54px;
  height: 54px;
  border-radius: 27px;
  background: linear-gradient(135deg, #4DABF7 0%, #339AF0 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  cursor: pointer;
  box-shadow: 0 4px 16px rgba(77, 171, 247, 0.4);
  overflow: hidden;
}

.floating-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 27px;
}

/* 响应式轮播图 */
@media (max-width: 480px) {
  .main-banner {
    height: 180px;
  }
}

@media (min-width: 481px) and (max-width: 768px) {
  .main-banner {
    height: 200px;
  }
}

@media (min-width: 769px) {
  .main-banner {
    height: 220px;
  }
}

/* 通知弹窗样式 */
.notice-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
}

.modal-content {
  position: relative;
  background: #ffffff;
  border-radius: 16px;
  width: 100%;
  max-width: 300px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  background: linear-gradient(135deg, #0474FC 0%, #ffffff 48%);
}

.modal-header {
  position: relative;
  padding: 24px 20px 0;
  display: flex;
  justify-content: center;
}

.notice-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: inset 0px 0px 12px 0px rgba(255, 255, 255, 1);
}

.close-btn {
  position: absolute;
  top: 16px;
  right: 20px;
  width: 24px;
  height: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-title {
  font-family: 'OPPO Sans 4.0', 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 20px;
  line-height: 1.4;
  color: #333333;
  text-align: center;
  padding: 16px 20px 0;
  margin-bottom: 16px;
}

.modal-body {
  padding: 0 24px;
  max-height: 300px;
  overflow-y: auto;
}

.notice-content {
  font-family: 'Inter', 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.625;
  color: #666666;
  text-align: left;
  word-wrap: break-word;
  white-space: pre-wrap;
}

.modal-footer {
  padding: 24px 20px;
}

.confirm-btn {
  width: 100%;
  height: 44px;
  background: #0474FC;
  border: none;
  border-radius: 8px;
  font-family: 'OPPO Sans 4.0', 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 16px;
  color: #ffffff;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.confirm-btn:hover {
  background: #0365d9;
}

.confirm-btn:active {
  background: #0256c7;
  transform: translateY(1px);
}
</style>
