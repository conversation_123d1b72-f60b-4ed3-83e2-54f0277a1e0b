<template>
  <div class="power">
    <!-- 顶部导航 -->
    <div class="header">
      <div class="back-icon" @click="goBack">
        <img src="@/assets/icons/back-arrow-icon.png" alt="返回" />
      </div>
      <div class="header-title">项 目</div>
    </div> <!-- 顶部背景图片区域 - 轮播图 -->
    <div class="banner-section">
      <div class="banner-bg"></div> <!-- 轮播内容 -->
      <div class="banner-carousel">
        <!-- 轮播容器 -->
        <div class="carousel-container" :style="{ transform: `translateX(-${currentSlide * 100}%)` }">
          <!-- API数据轮播图 -->
          <template v-if="bannerList && bannerList.length > 0">
            <div class="carousel-item" v-for="(banner, index) in bannerList" :key="'banner-' + index">
              <!-- 带链接的图片 --> <a v-if="banner.url && banner.url.trim()" :href="banner.url" class="banner-link">
                <img :src="banner.pic" alt="项目banner" class="banner-image" @error="handleBannerImageError($event)" />
              </a>
              <!-- 不带链接的图片 -->
              <img v-else :src="banner.pic" alt="项目banner" class="banner-image"
                @error="handleBannerImageError($event)" />
            </div>
          </template>

          <!-- 默认展示的banner，当API数据为空时显示 -->
          <div class="carousel-item" v-if="!bannerList || bannerList.length === 0">
            <img src="@/assets/images/power/power-banner-image1.png" alt="善之力项目背景图" class="banner-image" />
          </div>
        </div>

        <!-- 指示器 - 只在有多张轮播图时显示 -->
        <div class="carousel-indicators" v-if="bannerList && bannerList.length > 1">
          <div v-for="(_, index) in bannerList" :key="index" class="indicator-dot"
            :class="{ active: currentSlide === index }" @click="goToSlide(index)"></div>
        </div>

        <!-- 轮播图加载状态提示 - 如果需要可显示 -->
        <div v-if="false && !bannerList || bannerList.length === 0" class="banner-loading">
          加载中...
        </div>
      </div>
    </div> <!-- 项目标题 -->
    <div class="section-title">{{ pageTitle || '善之力项目' }}</div>

    <!-- 标签切换区域（可滚动固定） -->
    <div class="sticky-container" :class="{ 'is-sticky': isSticky }">
      <div class="tab-container">
        <div class="tab-buttons" :class="{ loading: loadingTypes }">
          <template v-if="projectTypes.length > 0">
            <div v-for="(type, index) in projectTypes" :key="type.id" class="tab-button"
              :class="{ active: activeTypeId === type.id }" @click="switchProjectType(type.id)">
              {{ type.name }}
            </div>
          </template>
          <template v-else>
            <div class="tab-button" :class="{ active: activeTab === 0 }" @click="switchTab(0)">
              经济项目
            </div>
            <div class="tab-button" :class="{ active: activeTab === 1 }" @click="switchTab(1)">
              周期项目
            </div>
          </template>
        </div>
      </div>
    </div>

    <!-- 占位元素，防止内容跳动 -->
    <div class="sticky-placeholder" v-if="isSticky" :style="{ height: stickyHeight + 'px' }"></div>

    <!-- 项目列表 -->
    <div class="project-list">
      <div v-for="project in projects" :key="project.id" class="project-card" @click="goToDetail(project)">
        <div class="project-content">
          <div class="project-image">
            <img :src="project.pic || require('@/assets/images/power/power-project-image1.png')" class="project-icon"
              @error="handleImageError($event)" alt="项目图标" />
          </div>

          <div class="project-info">
            <div class="project-title">{{ project.name || '国寿福泽E生终身寿险（互联网专属）' }}</div>

            <div class="project-details">
              <div class="detail-item">
                <div class="detail-label">项目收益</div>
                <div class="detail-value">{{ project.rate || '0.46%' }}</div>
              </div>
              <div class="detail-item">
                <div class="detail-label">项目周期</div>
                <div class="detail-value">{{ project.cycle || '3天' }}</div>
              </div>
              <div class="detail-item">
                <div class="detail-label">起购金额</div>
                <div class="detail-value">{{ formatAmount(project.min_amount) || '5000元' }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 进度条区域 - 从图片下方开始 -->
        <div class="progress-section">
          <div class="progress-container">
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: (project.percent || 85) + '%' }"></div>
            </div>
            <div class="progress-text">{{ project.percent || 85 }}%</div>
          </div>

          <div class="action-button" @click.stop="goToDetail(project)">
            认购详情
          </div>
        </div>

        <div class="purchase-limit" v-if="project.buy_number > 0">{{ project.buy_number }}次限购</div>
      </div>

      <!-- 没有数据时显示示例卡片 -->
      <div class="project-card" v-if="projects.length === 0 && !loading">
        <div class="project-content">
          <div class="project-image">
            <img src="@/assets/images/power/power-project-image2.png" class="project-icon" alt="项目图标" />
          </div>

          <div class="project-info">
            <div class="project-title">国寿福泽E生终身寿险（互联网专属）</div>

            <div class="project-details">
              <div class="detail-item">
                <div class="detail-label">项目收益</div>
                <div class="detail-value">0.46%</div>
              </div>
              <div class="detail-item">
                <div class="detail-label">项目周期</div>
                <div class="detail-value">3天</div>
              </div>
              <div class="detail-item">
                <div class="detail-label">起购金额</div>
                <div class="detail-value">5000元</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 进度条区域 - 从图片下方开始 -->
        <div class="progress-section">
          <div class="progress-container">
            <div class="progress-bar">
              <div class="progress-fill" style="width: 85%"></div>
            </div>
            <div class="progress-text">85%</div>
          </div>

          <div class="action-button">
            认购详情
          </div>
        </div>

        <div class="purchase-limit">1次限购</div>
      </div>

      <!-- 加载状态 -->
      <div class="loading-status" v-if="loading">
        <div class="loading-spinner"></div>
        <div class="loading-text">加载中..</div>
      </div>

      <!-- 没有更多数据 -->
      <div class="no-more" v-if="noMore && !loading && projects.length > 0">
        - 没有更多数据了 -
      </div>
    </div>

    <!-- 底部导航栏 -->
    <TabBar :active="2" />
  </div>
</template>

<script>
import TabBar from '@/components/TabBar.vue'
import ImageErrorHandler from '@/utils/ImageErrorHandler'

export default {
  name: 'Power',
  components: {
    TabBar
  }, mixins: [ImageErrorHandler], data() {
    return {
      activeTab: 0, // 0: 经济项目, 1: 周期项目（兼容旧版本）
      page: 1,
      loading: false,
      noMore: false,
      projects: [],
      projectTypes: [], // 项目类型列表
      activeTypeId: 0, // 当前选中的项目类型ID
      pageTitle: '', // 页面标题
      loadingTypes: false, // 加载项目类型的状态
      savedTypeId: null, // 从本地存储恢复的上次选择的类型ID
      typeSwitchTime: 0, // 记录类型切换的时间戳，用于限制频繁切换

      // 轮播图相关
      bannerList: [], // 轮播图列表
      currentSlide: 0, // 当前轮播图索引
      autoplayTimer: null, // 轮播自动播放定时器

      // 吸顶效果相关
      isSticky: false, // 是否处于吸顶状态
      stickyHeight: 0, // 吸顶元素高度
      stickyTriggerPosition: 0, // 触发吸顶的位置
      scrollTimeout: null // 滚动节流定时器
    }
  }, created() {
    // 初始化时获取项目类型和标题（包括轮播图数据）
    // 注意：此处是唯一应调用getProjectTypes的地方，避免重复请求API
    this.getProjectTypes();

    // 尝试从本地存储恢复上次选择的类型
    const savedTypeId = localStorage.getItem('power_active_type_id');
    if (savedTypeId) {
      // 仅记录值，实际切换会在获取到类型数据后进行
      this.savedTypeId = parseInt(savedTypeId);
    }
  },
  mounted() {
    // 设置轮播图自动播放（等待DOM更新后）
    this.$nextTick(() => {
      // 延迟一点时间确保DOM完全更新
      setTimeout(() => {
        this.startAutoplay();
      }, 200);
    });

    // 初始化吸顶元素
    this.$nextTick(() => {
      this.initStickyElements();
    });

    // 设置滚动监听
    this.setupScrollListener();

    // 设置窗口大小调整监听
    window.addEventListener('resize', this.handleResize);

    // 添加触摸事件监听，用于移动端操作轮播图
    this.$nextTick(() => {
      const carousel = document.querySelector('.banner-carousel');
      if (carousel) {
        this.setupTouchEvents(carousel);
      }
    });
  },

  beforeDestroy() {
    // 清理轮播自动播放
    this.stopAutoplay();

    // 清理滚动监听
    this.cleanupScrollListener();

    // 清理窗口大小调整监听
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },

    // 图片加载失败处理
    handleImageError(event) {
      ImageErrorHandler.handleImageError(event, 'icon');
    },

    // 格式化金额显示
    formatAmount(amount) {
      if (!amount && amount !== 0) return '0';
      return parseInt(amount).toLocaleString();
    },    // 获取项目类型和标题
    getProjectTypes() {
      this.loadingTypes = true;

      // 获取token
      const userToken = localStorage.getItem('user_token');

      if (!userToken) {
        this.$toast('请先登录');
        this.$nextTick(() => {
          this.$router.replace('/login');
        });
        this.loadingTypes = false;
        return;
      }      // 发起API请求获取项目类型和标题
      this.$http.get('/product/getInfo', {
        headers: { 'authorization': userToken }
      })
        .then(response => {
          if (response.data.code === 200) {
            const responseData = response.data.data || {};

            // 设置项目类型列表
            this.projectTypes = responseData.list || [];

            // 设置页面标题
            this.pageTitle = responseData.title || '善之力项目';            // 设置轮播图数据
            if (responseData.notice) {
              try {
                if (Array.isArray(responseData.notice)) {
                  // 确保数据有效
                  const validBanners = responseData.notice.filter(item => item && item.pic);
                  this.bannerList = validBanners;
                } else if (responseData.notice.pic) {
                  // 如果notice是单个对象，转换为数组
                  this.bannerList = [responseData.notice];
                } else {
                  throw new Error('无效的notice数据结构');
                }

                // 如果有轮播图数据，在DOM更新后重新启动轮播
                if (this.bannerList.length > 0) {
                  this.currentSlide = 0; // 重置为第一张                  // 使用nextTick确保DOM更新后再操作
                  this.$nextTick(() => {
                    this.restartAutoplay();
                  });
                }
              } catch (e) {
                // 出错时重置为空数组
                this.bannerList = [];
              }
            } else {
              this.bannerList = []; // 确保清空
            }
            // 如果有项目类型
            if (this.projectTypes.length > 0) {
              let typeIdToSelect;

              // 首先尝试使用之前保存的类型ID
              if (this.savedTypeId) {
                // 检查保存的类型ID是否仍然存在于当前的类型列表中
                const typeExists = this.projectTypes.some(type => type.id === this.savedTypeId);
                if (typeExists) {
                  typeIdToSelect = this.savedTypeId;
                }
              }

              // 如果没有可用的保存ID，使用第一个类型
              if (!typeIdToSelect) {
                typeIdToSelect = this.projectTypes[0].id;
              }

              // 记录当前选择的类型到本地存储
              localStorage.setItem('power_active_type_id', typeIdToSelect);
              this.switchProjectType(typeIdToSelect);
            } else {
              // 没有获取到项目类型，使用默认的标签
              this.switchTab(0);
            }
          } else {
            this.$toast(response.data.msg || '获取项目类型失败');
            this.switchTab(0); // 失败时使用默认标签
          }
        }).catch(error => {
          this.$toast('网络异常，请稍后重试');
          this.switchTab(0); // 异常时使用默认标签
        })
        .finally(() => {
          this.loadingTypes = false;
        });
    },    // 切换项目类型（新API方式）
    switchProjectType(typeId) {
      // 如果点击的是当前激活的标签，不做任何操作
      if (this.activeTypeId === typeId) return;

      // 记录类型切换开始时间
      const switchStartTime = Date.now();

      // 更新激活的类型ID
      this.activeTypeId = typeId;

      // 记录选择的类型ID到本地存储
      localStorage.setItem('power_active_type_id', typeId);      // 重置项目列表相关状态
      this.projects = [];
      this.page = 1;
      this.noMore = false;

      // 加载对应类型的项目数据
      this.loadProjectsByType();
    },

    // 获取当前激活类型的名称
    getActiveTypeName() {
      if (!this.projectTypes || this.projectTypes.length === 0) return '';
      const activeType = this.projectTypes.find(type => type.id === this.activeTypeId);
      return activeType ? activeType.name : '';
    },
    // 根据类型ID加载项目数据
    loadProjectsByType(isLoadMore = false) {
      // 防止重复加载
      if (this.loading) return;

      // 设置加载状态
      this.loading = true;

      // 记录开始加载时间
      const loadStartTime = Date.now();

      // 获取token
      const userToken = localStorage.getItem('user_token');

      // 验证用户登录状态
      if (!userToken) {
        this.$toast('请先登录');
        this.$nextTick(() => {
          this.$router.replace('/login');
        });
        this.loading = false;
        return;
      }

      // 获取当前项目类型名称，用于提示
      const currentTypeName = this.getActiveTypeName() || '项目';

      // 发起API请求获取项目列表
      this.$http.get(`/product/getList?page=${this.page}&id=${this.activeTypeId}`, {
        headers: { 'authorization': userToken }
      })
        .then(response => {
          if (response.data.code === 200) {
            const newProjects = response.data.data.list || [];

            // 处理加载结果           
            if (isLoadMore) {
              // 追加新加载的项目
              this.projects = [...this.projects, ...newProjects];
            } else {
              // 替换现有项目列表
              this.projects = newProjects;
            }            // 更新是否还有更多数据的状态
            this.noMore = newProjects.length === 0;
          } else {
            // API返回错误
            this.$toast(response.data.msg || `获取${currentTypeName}列表失败`);
          }
        })
        .catch(error => {
          // 网络异常处理
          this.$toast(`网络异常，获取${currentTypeName}失败，请稍后重试`);

          // 如果是首次加载且出错，可以尝试显示本地缓存数据
          if (!isLoadMore && this.projects.length === 0) {
            const cachedProjects = localStorage.getItem(`power_projects_${this.activeTypeId}`);
            if (cachedProjects) {
              try {
                this.projects = JSON.parse(cachedProjects);
                this.$toast('已显示缓存数据');
              } catch (e) {
                // 解析失败时不执行任何操作
              }
            }
          }
        })
        .finally(() => {
          // 确保最小加载时间，避免闪烁
          const loadTime = Date.now() - loadStartTime;
          const minLoadTime = 500; // 最小加载时间（毫秒）

          if (loadTime < minLoadTime) {
            setTimeout(() => {
              this.loading = false;
            }, minLoadTime - loadTime);
          } else {
            this.loading = false;
          }

          // 缓存首页数据
          if (!isLoadMore && this.projects.length > 0) {
            try {
              localStorage.setItem(`power_projects_${this.activeTypeId}`, JSON.stringify(this.projects));
            } catch (e) {
              // 缓存失败时不执行任何操作
            }
          }
        });
    },

    // 切换标签（旧方式，用于兼容）
    switchTab(tab) {
      if (this.activeTab === tab) return;

      this.activeTab = tab;
      this.projects = [];
      this.page = 1;
      this.noMore = false;
      this.loadProjects();
    },

    // 加载项目数据（旧方式，用于兼容）
    loadProjects(isLoadMore = false) {
      if (this.loading) return;

      this.loading = true;

      // 获取token
      const userToken = localStorage.getItem('user_token');

      if (!userToken) {
        this.$toast('请先登录');
        // 使用 $nextTick 确保 toast 显示后再跳转
        this.$nextTick(() => {
          this.$router.replace('/login');
        })
        this.loading = false;
        return;
      }

      // API请求获取项目列表
      this.$http.get(`/product/getList?page=${this.page}&type=${this.activeTab}`, {
        headers: {
          'authorization': userToken
        }
      })
        .then(response => {
          if (response.data.code === 200) {
            const newProjects = response.data.data.list || [];

            // 处理项目列表数据
            if (isLoadMore) {
              this.projects = [...this.projects, ...newProjects];
            } else {
              this.projects = newProjects;
            }

            // 判断是否还有更多数据
            this.noMore = newProjects.length === 0;
          } else {
            this.$toast(response.data.msg || '获取项目列表失败');
          }

          this.loading = false;
        })
        .catch(error => {
          console.error('获取项目列表失败:', error);
          this.$toast('网络异常，请稍后重试');
          this.loading = false;
        });
    },    // 轮播图相关方法     
    startAutoplay() {
      // 检查bannerList数据

      // 如果只有一张或没有轮播图，不需要自动播放
      if (!this.bannerList || this.bannerList.length <= 1) {
        return;
      }

      // 清除可能已存在的定时器
      if (this.autoplayTimer) {
        clearInterval(this.autoplayTimer);
      }      // 设置4秒自动切换（稍微延长时间，使体验更好）
      this.autoplayTimer = setInterval(() => {
        this.nextSlide();
      }, 4000);

      // 确保第一张图片正确显示      
      this.$nextTick(() => {
        this.currentSlide = 0;
      });
    },

    stopAutoplay() {
      if (this.autoplayTimer) {
        clearInterval(this.autoplayTimer);
        this.autoplayTimer = null;
      }
    },

    restartAutoplay() {
      this.stopAutoplay();
      this.startAutoplay();
    },

    nextSlide() {
      if (!this.bannerList || this.bannerList.length <= 1) return;
      this.currentSlide = (this.currentSlide + 1) % this.bannerList.length;
    },

    prevSlide() {
      if (!this.bannerList || this.bannerList.length <= 1) return;
      this.currentSlide = (this.currentSlide - 1 + this.bannerList.length) % this.bannerList.length;
    },

    goToSlide(index) {
      this.currentSlide = index;
      // 点击切换后重新开始自动播放
      this.restartAutoplay();
    },    // 初始化轮播图
    initCarousel() {
      // 确保已有轮播数据
      if (!this.bannerList || this.bannerList.length === 0) {
        return;
      }


      // 重置当前轮播位置
      this.currentSlide = 0;

      // 确保DOM已更新
      this.$nextTick(() => {        // 检查轮播图容器和轮播项
        const carousel = document.querySelector('.banner-carousel');
        if (!carousel) {
          return;
        }

        const items = carousel.querySelectorAll('.carousel-item');


        // 如果DOM中有轮播项，则启动自动播放
        if (items.length > 1) {
          this.restartAutoplay();
        }
      });
    },

    // 吸顶效果相关方法
    initStickyElements() {
      const stickyContainer = document.querySelector('.sticky-container');
      if (stickyContainer) {
        // 获取头部高度
        const header = document.querySelector('.header');
        const headerHeight = header ? header.offsetHeight : 0;

        // 计算触发吸顶的位置（添加缓冲，使切换更平滑）
        this.stickyTriggerPosition = stickyContainer.offsetTop - headerHeight - 5;

        // 保存吸顶容器的高度，用于设置占位元素高度
        this.stickyHeight = stickyContainer.offsetHeight + 2;
      }
    },

    setupScrollListener() {
      window.addEventListener('scroll', this.handleScroll, { passive: true });
    },

    cleanupScrollListener() {
      window.removeEventListener('scroll', this.handleScroll);
      if (this.scrollTimeout) {
        clearTimeout(this.scrollTimeout);
      }
    },

    handleResize() {
      // 窗口大小变化时重新计算吸顶元素
      this.$nextTick(() => {
        this.initStickyElements();
      });
    },

    handleScroll() {
      if (this.scrollTimeout) clearTimeout(this.scrollTimeout);

      this.scrollTimeout = setTimeout(() => {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop;

        // 更新吸顶状态
        this.isSticky = scrollTop > this.stickyTriggerPosition;

        // 如果接近底部，加载更多
        const scrollHeight = document.documentElement.scrollHeight || document.body.scrollHeight;
        const clientHeight = document.documentElement.clientHeight || window.innerHeight;

        if (scrollTop + clientHeight >= scrollHeight - 50) {
          if (!this.loading && !this.noMore && this.projects.length > 0) {
            this.page++;
            this.loadProjectsByType(true);
          }
        }
      }, 50);
    },
    // 跳转到项目详情
    goToDetail(project) {
      this.$router.push({
        path: '/project-detail',
        query: { id: project.id }
      });
    },    // 改进的轮播图错误处理方法       
    handleBannerImageError(event) {
      // 替换为默认图片
      try {
        event.target.src = require('@/assets/images/power/power-banner-image1.png');
      } catch (e) {
        // 最后的备用方案 - 使用纯色占位
        event.target.style.backgroundColor = '#f0f0f0';
        event.target.style.minHeight = '150px';
      }

      // 添加加载失败标记，方便样式区分
      event.target.classList.add('load-fail');

      // 尝试重新启动轮播
      this.$nextTick(() => {
        // 防止所有图片都加载失败导致轮播失效
        if (this.bannerList && this.bannerList.length > 0) {
          this.restartAutoplay();
        }
      });
    },

    // 设置轮播图触摸事件
    setupTouchEvents(element) {
      let startX = 0;
      let startY = 0;
      let distX = 0;
      let distY = 0;
      let startTime = 0;
      const minSwipeDistance = 50;
      const maxSwipeTime = 300;

      // 记录触摸开始位置
      element.addEventListener('touchstart', (e) => {
        startX = e.touches[0].clientX;
        startY = e.touches[0].clientY;
        startTime = Date.now();
      }, { passive: true });

      // 处理轻扫操作
      element.addEventListener('touchend', (e) => {
        const endX = e.changedTouches[0].clientX;
        const endY = e.changedTouches[0].clientY;
        distX = endX - startX;
        distY = endY - startY;
        const elapsedTime = Date.now() - startTime;

        // 确保是水平滑动（避免上下滚动时也触发轮播切换）
        if (Math.abs(distX) > Math.abs(distY) &&
          Math.abs(distX) > minSwipeDistance &&
          elapsedTime < maxSwipeTime) {

          // 向左滑动 -> 下一张
          if (distX < 0) {
            this.nextSlide();
          }
          // 向右滑动 -> 上一张
          else {
            this.prevSlide();
          }

          // 重启自动播放
          this.restartAutoplay();
        }
      }, { passive: true });
    },
  }, mounted() {
    // 设置轮播图自动播放（等待DOM更新后）
    this.$nextTick(() => {
      // 延迟一点时间确保DOM完全更新
      setTimeout(() => {

        // 检查轮播图数据
        if (this.bannerList && this.bannerList.length > 0) {

          this.currentSlide = 0; // 确保从第一张开始
          this.startAutoplay();
        } else {

          // 不再重复调用getProjectTypes，因为已经在created钩子中调用过
        }
      }, 300);
    });

    // 初始化吸顶元素
    this.$nextTick(() => {
      // 等DOM更新后再获取元素高度      
      setTimeout(() => {
        const stickyContainer = document.querySelector('.sticky-container');
        if (stickyContainer) {
          this.stickyHeight = stickyContainer.offsetHeight;
        }
      }, 300);
    });

    // 添加滚动监听
    window.addEventListener('scroll', this.handleScroll);    // 检查banner-carousel元素是否存在及其高度
    this.$nextTick(() => {
      const carousel = document.querySelector('.banner-carousel');
      if (carousel && carousel.offsetHeight === 0) {
        carousel.style.height = '160px';
      }
    });
  }, beforeDestroy() {
    // 清理轮播自动播放
    if (this.autoplayTimer) {
      clearInterval(this.autoplayTimer);
      this.autoplayTimer = null;
    }

    // 移除滚动监听
    window.removeEventListener('scroll', this.handleScroll);
  }
}
</script>

<style scoped>
/* 全局样式 */
.power {
  position: relative;
  min-height: 100vh;
  background-color: #F6F6F6;
  padding-bottom: 80px;
  box-sizing: border-box;
}

/* 顶部标题栏 */
.header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 44px;
  background-color: #fff;
  z-index: 10;
  padding: 0 16px;
}

.back-icon {
  position: absolute;
  left: 16px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.back-icon img {
  width: 10px;
  height: 18px;
}

.header-title {
  font-size: 16px;
  font-weight: 600;
  font-family: 'PingFang SC', sans-serif;
  color: #000;
}

/* Banner区域 */
.banner-section {
  position: relative;
  height: 206px;
  overflow: hidden;
}

.banner-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.banner-title {
  position: absolute;
  top: 220px;
  left: 19px;
  font-size: 16px;
  font-weight: 400;
  color: #000;
  font-family: 'PingFang SC', sans-serif;
}

/* 项目标题 */
.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #000;
  margin: 30px 0 15px 20px;
  font-family: 'PingFang SC', sans-serif;
  position: relative;
}

/* 标签容器 */
.tab-container {
  position: relative;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  padding: 0 14px;
  margin-bottom: 15px;
}

/* 隐藏滚动条但保留功能 */
.tab-container::-webkit-scrollbar {
  display: none;
}

/* 标签切换按钮 */
.tab-buttons {
  display: flex;
  align-items: center;
  padding-bottom: 5px;
  min-width: fit-content;
}

.tab-button {
  min-width: 92px;
  text-align: center;
  padding: 8px 18px;
  font-size: 14px;
  color: #0474FC;
  background-color: #E8EFFB;
  border-radius: 24px;
  margin-right: 13px;
  transition: all 0.3s ease;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  display: inline-block;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  user-select: none;
  white-space: nowrap;
  position: relative;
}

.tab-button.active {
  background: linear-gradient(90deg, #0094FF 0%, #0474FC 100%);
  color: #fff;
  box-shadow: 0 2px 6px rgba(4, 116, 252, 0.25);
  transform: translateY(-1px);
}

.tab-button:active {
  opacity: 0.9;
  transform: translateY(0px);
}

/* 轮播图样式 */
.banner-carousel {
  position: relative;
  width: 100%;
  height: 100%;
  /* 明确指定高度确保内容可见 */
  overflow: hidden;
  /* 移除圆角，使轮播图边缘为直角 */
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.carousel-container {
  display: flex;
  height: 100%;
  width: 100%;
  transition: transform 0.5s ease;
  /* 简化过渡效果 */
  will-change: transform;
  /* 性能优化 */
  position: relative;
  /* 确保定位正确 */
}

.carousel-item {
  width: 100%;
  flex-shrink: 0;
  height: 100%;
  position: relative;
  /* 确保内容正确定位 */
  overflow: hidden;
  /* 防止内容溢出 */
}

.banner-link {
  display: block;
  width: 100%;
  height: 100%;
}

.banner-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  /* 防止图片底部出现空隙 */
  margin: 0;
  /* 确保没有外边距 */
}

/* 图片加载失败时的样式 */
.banner-image.load-fail {
  min-height: 150px;
  background-color: #f0f0f0;
  position: relative;
}

/* 图片加载失败时的占位指示 */
.banner-image.load-fail::after {
  content: "图片加载失败";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #999;
  font-size: 14px;
}

.carousel-indicators {
  position: absolute;
  bottom: 12px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  gap: 10px;
  z-index: 5;
}

.indicator-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  transform: scale(1);
}

.indicator-dot.active {
  width: 10px;
  height: 10px;
  background: #fff;
  transform: scale(1.1);
}

/* 项目列表 */
.project-list {
  padding: 0 14px;
}

.project-card {
  background: linear-gradient(180deg, #DAEDFE 0%, #FFFFFF 30.8%);
  border-radius: 8px;
  padding: 15px 15px 25px 15px;
  margin-bottom: 15px;
  position: relative;
  box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.05);
}

.project-content {
  display: flex;
  margin-bottom: 15px;
}

.project-image {
  width: 83px;
  height: 83px;
  border-radius: 4px;
  overflow: hidden;
  margin-right: 15px;
  background-color: #D9D9D9;
  flex-shrink: 0;
}

.project-icon {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.project-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.project-title {
  font-size: 14px;
  font-weight: 600;
  color: #000;
  font-family: 'PingFang SC', sans-serif;
  line-height: 19.6px;
  margin-bottom: 12px;
}

.project-details {
  display: flex;
  justify-content: space-between;
}

.detail-item {
  flex: 1;
  text-align: center;
}

.detail-label {
  font-size: 12px;
  color: #666;
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  margin-bottom: 4px;
  line-height: 14px;
}

.detail-value {
  font-size: 16px;
  font-weight: 700;
  color: #333;
  font-family: 'DIN', sans-serif;
  line-height: 19.5px;
}

/* 进度条区域 - 从图片下方开始 */
.progress-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 15px;
  margin-bottom: 20px;
  position: relative;
}

.purchase-limit {
  position: absolute;
  bottom: 5px;
  left: 15px;
  font-size: 12px;
  color: #666;
  background: linear-gradient(90deg, #FDF5BE 100%, rgba(253, 236, 190, 0) 0%);
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: 400;
  font-family: 'Inter', sans-serif;
  line-height: 16px;
  z-index: 1;
}

.progress-container {
  display: flex;
  align-items: center;
  width: calc(100% - 87px);
  /* 为右侧按钮留出空间 */
  margin-right: 15px;
}

.progress-bar {
  width: 200px;
  /* 固定宽度，从图片左边缘开始 */
  height: 8px;
  background-color: #F2F2F2;
  border-radius: 4px;
  overflow: hidden;
  margin-right: 8px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #0094FF 0%, #0474FC 100%);
  border-radius: 4px;
}

.progress-text {
  font-size: 14px;
  color: #FF618B;
  font-weight: 500;
  font-family: 'PingFang SC', sans-serif;
  line-height: 20px;
  min-width: 35px;
}

.action-button {
  background-color: #0474FC;
  color: #fff;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 14px;
  font-weight: 700;
  font-family: 'Inter', sans-serif;
  border: none;
  cursor: pointer;
  white-space: nowrap;
  justify-content: center;
  line-height: 17px;
  position: absolute;
  right: 0;
}

/* 加载状态 */
.loading-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #0474FC;
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 14px;
  color: #999;
  font-family: 'PingFang SC', sans-serif;
}

.no-more {
  text-align: center;
  padding: 15px 0;
  color: #999;
  font-size: 14px;
  font-family: 'PingFang SC', sans-serif;
}

/* 吸顶效果样式 */
.sticky-container {
  background: #fff;
  z-index: 10;
  transition: box-shadow 0.3s ease;
  padding: 10px 0;
}

.sticky-container.is-sticky {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.sticky-placeholder {
  width: 100%;
}

/* 加载项目类型时的过渡动画 */
.tab-buttons {
  transition: opacity 0.3s ease;
}

.tab-buttons.loading {
  opacity: 0.6;
}

/* 轮播图加载状态 */
.banner-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
}
</style>
