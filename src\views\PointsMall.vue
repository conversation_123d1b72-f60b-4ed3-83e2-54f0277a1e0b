<template>
  <div class="points-mall">
    <!-- 顶部背景图 -->
    <div class="top-bg">
      <img src="@/assets/images/points-mall/top-bg.png" alt="顶部背景" />
    </div>

    <!-- 包含头部导航栏和搜索框的吸顶容器 -->
    <div class="sticky-container" :class="{ 'is-sticky': isSticky }">
      <!-- 头部导航栏 -->
      <div class="header">
        <div class="back-icon" @click="goBack">
          <img src="@/assets/images/points-mall/back-icon.svg" alt="返回" />
        </div>
        <div class="title">积分商城</div>
      </div>

      <!-- 搜索框 -->
      <div class="search-box">
        <div class="search-input">
          <input type="text" class="search-input-field" v-model="searchKeyword" placeholder="输入商品名称"
            @keyup.enter="searchProducts(searchKeyword)" />
          <img class="search-icon-small" src="@/assets/images/points-mall/search-icon.svg" alt="搜索"
            @click="searchProducts(searchKeyword)" />
        </div>
        <div class="search-clear" v-if="searchKeyword" @click.stop="clearSearch">
          <span>&times;</span>
        </div>
      </div>
    </div>

    <!-- 占位元素，用于防止内容跳动 -->
    <div class="sticky-placeholder" v-if="isSticky" :style="{ height: stickyHeight + 'px' }"></div>

    <!-- 顶部轮播广告 -->
    <div class="banner">
      <div class="banner-content">
        <van-swipe class="swiper-container" :autoplay="3000" indicator-color="#0474FC">
          <van-swipe-item v-for="(item, index) in swiperList" :key="index">
            <img class="banner-bg" :src="item.pic" :alt="item.name || '轮播图'" @error="handleImageError" />
          </van-swipe-item>
        </van-swipe>
      </div>
    </div>
    <!-- 积分卡片 -->
    <div class="points-card">
      <div class="gift-icon">
        <div class="gift-icon-bg"></div>
        <img src="@/assets/images/points-mall/gift-box-icon.png" alt="礼盒" />
      </div>
      <div class="points-info">
        <div class="points-row">
          <div class="points-label">我的积分</div>
          <div class="points-value">{{ userPoints.toFixed(2) }}</div>
        </div>
      </div>
    </div>

    <!-- 功能快捷入口 -->
    <div class="quick-links">
      <div class="quick-link" @click="goToExpress">
        <div class="link-icon">
          <img src="@/assets/images/points-mall/express-icon.png" alt="快递查询" />
        </div>
        <div class="link-text">快递查询</div>
      </div>

      <div class="quick-link" @click="goToMallDetail">
        <div class="link-icon">
          <img src="@/assets/images/points-mall/order-icon.png" alt="我的订单" />
        </div>
        <div class="link-text">我的订单</div>
      </div>

      <div class="quick-link" @click="goToIntegralDetail">
        <div class="link-icon">
          <img src="@/assets/images/points-mall/point-detail-icon.png" alt="积分详情" />
        </div>
        <div class="link-text">积分详情</div>
      </div>
    </div>

    <!-- 积分筛选标签标题 -->
    <div class="filter-title">
      <span class="normal-text">积分</span><span class="highlight-text">兑好礼</span>
    </div>

    <!-- 积分筛选标签 -->
    <div class="point-filter">
      <div v-for="(tag, index) in filterTags" :key="index" class="filter-tag"
        :class="{ active: currentFilter === tag.value }" @click="filterByPrice(tag.value)">
        {{ tag.label }}
      </div>
    </div>

    <!-- 商品列表区域 -->
    <div class="product-list">
      <div class="product-item" v-for="(product, index) in displayProducts" :key="product.id"
        @click="viewProductDetail(product)">
        <div class="product-image">
          <img :src="product.image" alt="商品图片" @error="handleImageError" />
          <div class="product-badge" v-if="product.isNew">
            <span>新品</span>
          </div>
          <div class="product-badge hot" v-if="product.isHot">
            <span>热门</span>
          </div>
        </div>
        <div class="product-info">
          <div class="product-name">{{ product.name }}</div>
          <div class="product-sold">
            <span>已售</span>
            <span class="sold-count">{{ product.soldCount || '300+' }}</span>
          </div>
          <div class="product-price">{{ product.price }}积分</div>
          <div class="cart-icon" @click.stop="exchangeProduct(product)">
            <img src="@/assets/images/points-mall/cart-icon.svg" alt="加入购物车" />
            <span class="cart-add-animation" v-if="product.showAddAnimation">+1</span>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div class="loading-container" v-if="loading">
        <van-loading color="#0474FC" />
        <span class="loading-text">加载中...</span>
      </div>

      <!-- 全部加载完毕 -->
      <div class="finished-text" v-if="finished && products.length > 0">
        - 没有更多商品了 -
      </div>

      <!-- 空状态 -->
      <div class="empty-state" v-if="!loading && products.length === 0">
        {{ searchKeyword ? `未找到"${searchKeyword}"相关商品` : '暂无商品' }}
      </div>
    </div>

    <!-- 底部装饰线 -->
    <div class="bottom-line">
      <div class="bottom-line-text">积分兑好礼</div>
    </div>
  </div>
</template>

<script>
import { Toast, Loading, Dialog, Swipe, SwipeItem } from 'vant'
import axios from 'axios'

export default {
  name: 'PointsMall',
  components: {
    VanLoading: Loading,
    VanSwipe: Swipe,
    VanSwipeItem: SwipeItem
  },
  data() {
    return {
      products: [], // 全部商品列表
      displayProducts: [], // 当前显示的经过筛选的商品列表
      loading: false, // 是否正在加载更多
      finished: false, // 是否已加载全部数据
      currentPage: 1, // 当前页码
      userPoints: 0, // 用户积分
      swiperList: [], // 顶部轮播图数据
      searchKeyword: '', // 搜索关键词
      currentFilter: 'all', // 当前选中的筛选标签
      cartItems: [], // 购物车商品
      cartCount: 0, // 购物车商品数量
      filterTags: [
        { label: '全部', value: 'all' },
        { label: '1千以下', value: 'below1k' },
        { label: '1千-5千', value: '1k-5k' },
        { label: '5千-2万', value: '5k-20k' },
        { label: '2万以上', value: 'above20k' }
      ],
      searchActive: false, // 搜索框是否激活
      isSticky: false, // 是否处于吸顶状态
      stickyHeight: 60, // 吸顶元素的高度
      stickyTriggerPosition: 0, // 触发吸顶的滚动位置
      scrollTimeout: null, // 滚动节流的timeout
    }
  }, created() {
    // 加载第一页数据
    this.loadProductData();

    // 从本地存储恢复购物车数据
    try {
      const savedCart = localStorage.getItem('points_mall_cart');
      if (savedCart) {
        this.cartItems = JSON.parse(savedCart);
        this.cartCount = this.cartItems.reduce((sum, item) => sum + item.quantity, 0);
      }
    } catch (e) {
      console.error('恢复购物车数据失败', e);
    }
  },
  mounted() {
    // 确保页面完全渲染后再初始化吸顶元素
    this.$nextTick(() => {
      // 初始化吸顶元素
      this.initStickyElements();

      // 设置滚动和调整大小的监听器
      this.setupScrollListener();
      this.setupResizeListener();

      // 应用一次当前筛选条件
      this.applyCurrentFilter();

      // 强制触发一次滚动处理，确保初始状态正确
      this.handleScroll();

      // 如果有图片需要加载，可能会影响布局，所以在图片加载完成后再次初始化吸顶元素
      window.addEventListener('load', () => {
        this.initStickyElements();
        this.handleScroll();
      });
    });
  },
  beforeDestroy() {
    // 清理事件监听器
    this.cleanupScrollListener();
    this.cleanupResizeListener();
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    goToIntegralDetail() {
      this.$router.push('/integral-detail')
    },
    goToMallDetail() {
      this.$router.push('/order')
    },
    goToExpress() {
      this.$router.push('/express')
    },

    // 查看商品详情
    viewProductDetail(product) {
      this.$router.push({
        path: '/product-detail',
        query: {
          id: product.id
        }
      })
    },


    // 处理图片加载错误
    handleImageError(e) {
      // 设置默认图片
      e.target.src = require('@/assets/images/points-mall/product-image.png');
    },

    // 兑换商品
    exchangeProduct(product) {
      // 检查积分是否足够
      if (this.userPoints < product.price) {
        Toast('积分不足，无法兑换');
        return;
      }

      // 显示确认对话框
      this.$dialog.confirm({
        title: '确认兑换',
        message: `确定使用${product.price}积分兑换"${product.name}"吗？`,
      }).then(() => {
        // 用户点击确认，调用兑换API
        this.doExchange(product);
      }).catch(() => {
        // 用户取消操作，不做处理
      });
    },

    // 执行兑换操作
    doExchange(product) {
      Toast.loading({
        message: '兑换中...',
        forbidClick: true,
        duration: 0
      });

      const userToken = localStorage.getItem('user_token');

      // 调用真实的兑换API
      this.$http.post('/shop/doBuy', {
        id: product.id,
        number: 1
      }, {
        headers: {
          'authorization': userToken
        }
      })
        .then(response => {
          Toast.clear();
          if (response.data.code === 200) {
            Toast.success('兑换成功');
            // 更新用户积分
            this.userPoints -= product.price;
            // 刷新商品数据以获取最新的用户积分
            this.refreshData();
          } else {
            Toast.fail(response.data.msg || '兑换失败');
          }
        })
        .catch(error => {
          Toast.clear();
          if (error.response && error.response.status === 401) {
            Toast.fail('登录已过期，请重新登录');
            localStorage.removeItem('user_token');
            this.$router.replace('/login');
          } else {
            Toast.fail('网络异常，请稍后重试');
          }
          console.error('兑换商品失败:', error);
        });
    },

    // 加载商品数据
    loadProductData(searchKeyword = '') {
      if (this.finished || this.loading) return;

      this.loading = true;

      // 从localStorage获取token
      const userToken = localStorage.getItem('user_token');

      if (!userToken) {
        Toast('请先登录');
        this.$nextTick(() => { this.$router.replace('/login') });
        this.loading = false;
        return;
      }

      // 构建API请求URL，按照要求格式: /shop/getsearch?page=1&name=大米
      let apiUrl = searchKeyword
        ? `/shop/getsearch?page=${this.currentPage}&name=${encodeURIComponent(searchKeyword)}`
        : `/shop/getlist?page=${this.currentPage}`;

      console.log('请求API:', apiUrl); // 开发调试用，可在上线前移除

      // 发起API请求获取商品列表
      this.$http.get(apiUrl, {
        headers: {
          'authorization': userToken
        }
      })
        .then(response => {
          if (response.data.code === 200) {
            const data = response.data.data;

            // 清除搜索加载提示
            Toast.clear();

            // 设置用户积分
            if (data.integral !== undefined) {
              this.userPoints = data.integral;
            }

            // 处理轮播图数据
            if (data.swiper && Array.isArray(data.swiper) && data.swiper.length > 0) {
              this.swiperList = data.swiper;
            } else if (this.currentPage === 1 && !searchKeyword) {
              // 如果没有轮播图数据，设置默认轮播图
              this.swiperList = [
                { pic: require('@/assets/images/points-mall/banner.png'), name: '默认轮播图' }
              ];
            }

            // 处理商品列表数据
            if (data.list && data.list.length > 0) {
              // 将图片URL转换为正确格式的商品数据
              const newProducts = data.list.map(item => {
                // 使用随机数据丰富产品演示
                const isHot = Math.random() > 0.7;
                const isNew = !isHot && Math.random() > 0.7;
                const randomSoldCount = Math.floor(Math.random() * 1000) + 100;

                return {
                  id: item.id || Date.now() + Math.random(), // 确保有id
                  name: item.name || '苏泊尔不锈钢电热水壶',
                  price: item.price || Math.floor(Math.random() * 10000) + 66, // 随机价格增加变化
                  image: item.pic || require(`@/assets/images/points-mall/product-${Math.floor(Math.random() * 4) + 1}.png`), // 随机使用示例图片
                  isHot,
                  isNew,
                  soldCount: item.soldCount || `${randomSoldCount}+`,
                  description: item.description || '这是一款高品质的商品，物美价优，欢迎选购！'
                };
              });

              // 将新数据添加到现有数据末尾
              this.products = [...this.products, ...newProducts];
              // 应用当前筛选条件
              this.applyCurrentFilter();
              // 下滑加载时页码加1
              this.currentPage++;
            } else {
              // 如果当前页没有数据，则标记为加载完成
              this.finished = true;

              // 如果是搜索模式且没有数据，显示更友好的提示
              if (this.searchKeyword && this.products.length === 0) {
                Toast(`未找到"${this.searchKeyword}"相关商品`);
              }
            }
          } else {
            Toast(response.data.msg || '获取商品列表失败');
            this.finished = true;
          }

          this.loading = false;
        })
        .catch(error => {
          console.error('获取商品列表失败:', error);
          // 清除加载中提示
          Toast.clear();
          // 显示错误提示
          Toast('网络异常，请稍后重试');
          this.loading = false;
          this.finished = true;

          // 如果是搜索请求出错，显示相应提示
          if (this.searchKeyword && this.products.length === 0) {
            this.$nextTick(() => {
              Toast('搜索失败，请重试');
            });
          }
        });
    },
    // 刷新数据（重置分页并重新加载）
    refreshData() {
      this.products = [];
      this.displayProducts = [];
      this.currentPage = 1;
      this.finished = false;

      // 在刷新数据时显示加载提示
      Toast.loading({
        message: '刷新中...',
        forbidClick: true,
        duration: 0
      });

      // 如果有搜索关键词，则带上搜索关键词刷新
      this.loadProductData(this.searchKeyword);
    },



    // 搜索商品 - 使用指定API格式请求
    searchProducts(keyword) {
      if (!keyword || keyword.trim() === '') {
        // 如果清空关键词，恢复全部商品
        this.clearSearch();
        return;
      }

      Toast.loading({
        message: '搜索中...',
        forbidClick: true,
        duration: 0 // 持续显示，直到请求完成
      });

      // 重置状态
      this.products = [];
      this.displayProducts = [];
      this.currentPage = 1;
      this.finished = false;

      // 调用API进行搜索，使用 getsearch API
      this.loadProductData(keyword.trim());
    },

    // 清空搜索
    clearSearch() {
      if (this.searchKeyword) {
        this.searchKeyword = '';
        // 重置状态
        this.products = [];
        this.displayProducts = [];
        this.currentPage = 1;
        this.finished = false;

        // 重新加载所有商品
        this.loadProductData();

        Toast('已清空搜索结果');
      }
    },

    // 根据价格筛选商品
    filterByPrice(filterValue) {
      this.currentFilter = filterValue;
      this.applyCurrentFilter();
    },

    // 应用当前筛选条件
    applyCurrentFilter() {
      if (!this.products.length) {
        this.displayProducts = [];
        return;
      }

      if (this.currentFilter === 'all') {
        this.displayProducts = [...this.products];
        return;
      }

      // 根据价格区间筛选
      let filteredProducts = [];
      switch (this.currentFilter) {
        case 'below1k':
          filteredProducts = this.products.filter(p => p.price < 1000);
          break;
        case '1k-5k':
          filteredProducts = this.products.filter(p => p.price >= 1000 && p.price < 5000);
          break;
        case '5k-20k':
          filteredProducts = this.products.filter(p => p.price >= 5000 && p.price < 20000);
          break;
        case 'above20k':
          filteredProducts = this.products.filter(p => p.price >= 20000);
          break;
        default:
          filteredProducts = [...this.products];
      }

      this.displayProducts = filteredProducts;

      // 如果没有筛选结果，显示提示
      if (filteredProducts.length === 0 && this.products.length > 0) {
        Toast(`没有${this.getFilterLabel(this.currentFilter)}的商品`);
      }
    },

    // 获取筛选标签的文本
    getFilterLabel(filterValue) {
      const tag = this.filterTags.find(t => t.value === filterValue);
      return tag ? tag.label : '全部';
    },

    // 节流函数，限制函数的执行频率
    throttle(fn, delay) {
      let lastCall = 0;
      return function (...args) {
        const now = new Date().getTime();
        if (now - lastCall < delay) {
          return;
        }
        lastCall = now;
        return fn.apply(this, args);
      };
    },

    // 初始化吸顶元素
    initStickyElements() {
      this.$nextTick(() => {
        const stickyContainer = document.querySelector('.sticky-container');
        if (stickyContainer) {
          // 获取顶部背景图高度
          const topBg = document.querySelector('.top-bg');
          const topBgHeight = topBg ? topBg.offsetHeight : 0;

          // 获取容器的原始位置（相对于文档顶部）
          const containerRect = stickyContainer.getBoundingClientRect();
          const containerTop = containerRect.top + window.pageYOffset;

          // 计算触发吸顶的位置，使用顶部背景图高度作为触发点
          this.stickyTriggerPosition = Math.max(5, topBgHeight - 10); // 顶部背景图消失前开始吸顶

          // 保存吸顶容器的高度，用于设置占位元素高度
          this.stickyHeight = stickyContainer.offsetHeight;

          // 如果当前滚动位置很小，强制重置吸顶状态
          if (window.pageYOffset <= 10) {
            this.isSticky = false;
          }
        }
      });
    },

    // 设置滚动监听
    setupScrollListener() {
      window.addEventListener('scroll', this.handleScroll, { passive: true });
    },

    // 清理滚动监听
    cleanupScrollListener() {
      window.removeEventListener('scroll', this.handleScroll);
      if (this.scrollTimeout) {
        clearTimeout(this.scrollTimeout);
      }
    },

    // 设置窗口大小调整的监听
    setupResizeListener() {
      window.addEventListener('resize', this.handleResize, { passive: true });
    },

    // 清理窗口大小调整的监听
    cleanupResizeListener() {
      window.removeEventListener('resize', this.handleResize);
    },

    // 处理窗口大小变化
    handleResize() {
      // 重新计算吸顶元素的高度和位置
      this.initStickyElements();
    },

    // 处理滚动事件
    handleScroll() {
      if (this.scrollTimeout) clearTimeout(this.scrollTimeout);

      this.scrollTimeout = setTimeout(() => {
        const scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
        const scrollHeight = document.documentElement.scrollHeight || document.body.scrollHeight;
        const clientHeight = document.documentElement.clientHeight || window.innerHeight;

        // 处理吸顶效果
        // 确保在滚动到顶部或接近顶部时重置吸顶状态
        if (scrollTop <= 10) {
          // 当滚动位置非常接近顶部时，强制关闭吸顶
          this.isSticky = false;
        } else {
          // 根据设定的触发位置判断是否应该吸顶
          this.isSticky = scrollTop > this.stickyTriggerPosition;
        }        // 当滚动到距离底部100px时加载更多
        if (scrollHeight - scrollTop - clientHeight < 100 && !this.loading && !this.finished) {
          console.log('触发下滑加载, page=', this.currentPage); // 开发调试用，可在上线前移除

          // 加载更多时保留搜索关键词，实现搜索结果的分页加载
          // 页码已在loadProductData方法中增加，无需在此处手动加1
          this.loadProductData(this.searchKeyword);
        }
      }, 100); // 减小节流时间，使吸顶效果更流畅
    }
  }
}
</script>

<style scoped>
.points-mall {
  min-height: 100vh;
  background: #F2F3F4;
  display: flex;
  flex-direction: column;
  padding-bottom: 30px;
  position: relative;
  /* 为绝对定位的子元素提供参考 */
  /* 避免底部内容被遮挡 */
}

/* 顶部背景图 */
.top-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 230px;
  /* 调整高度使其覆盖头部和搜索框区域 */
  z-index: 0;
  overflow: hidden;
}

.top-bg img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 头部 */
.header {
  height: 56px;
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 0;
  z-index: 1;
  width: 100%;
}

.back-icon {
  position: absolute;
  left: 16px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background: rgba(255, 255, 255, 0.3);
  /* 半透明白色背景 */
  border-radius: 50%;
  padding: 5px;
}

/* 在吸顶状态下调整返回按钮样式 */
.is-sticky .back-icon {
  background: rgba(255, 255, 255, 0.2);
}

.title {
  font-family: 'PingFang SC', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #FFF;
  /* 更改为白色以适应顶部背景图 */
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  /* 添加文字阴影提高可读性 */
}

/* 顶部轮播广告 */
.banner {
  margin: 0 16px 16px;
  border-radius: 10px;
  overflow: hidden;
  position: relative;
  height: 140px;
  /* 调整高度以适应新banner图片 */
}

.banner-content {
  width: 100%;
  height: 100%;
  position: relative;
  /* 移除渐变背景，使用轮播图数据作为完整背景 */
  padding: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.swiper-container {
  width: 100%;
  height: 100%;
  border-radius: 10px;
  overflow: hidden;
}

.van-swipe-item {
  width: 100%;
  height: 100%;
}

.banner-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 1;
  /* 确保背景图片在正确的层级 */
}

.banner-label {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.banner-label img {
  width: 42px;
  height: 18px;
  margin-right: 8px;
}

.banner-text {
  font-size: 12px;
  color: #FF5C89;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
  /* 添加文字阴影提高可读性 */
}

.banner-title {
  position: relative;
  z-index: 2;
  font-size: 24px;
  font-weight: 600;
  /* 加粗 */
  color: #333;
  margin-top: 8px;
  text-shadow: 0 1px 3px rgba(255, 255, 255, 0.9);
  /* 添加文字阴影提高可读性 */
}

.mascot-icon {
  position: absolute;
  bottom: 0;
  right: 10px;
  height: 140px;
  /* 调整高度与banner一致 */
  z-index: 2;
  /* 确保在背景图之上 */
}

/* 积分卡片 */
.points-card {
  margin: 0 16px 16px;
  background: linear-gradient(180deg, #DAEDFE 0%, #FFFFFF 30.77%);
  border-radius: 10px;
  padding: 16px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.gift-icon {
  width: 44px;
  height: 44px;
  position: relative;
  margin-right: 16px;
}

.gift-icon-bg {
  width: 44px;
  height: 44px;
  background: rgba(4, 116, 252, 0.1);
  border-radius: 22px;
}

.gift-icon img {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 24px;
  height: 24px;
}

.points-info {
  flex: 1;
}

.points-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.points-label {
  font-size: 14px;
  color: #666;
}

.points-value {
  font-family: 'DIN Black', 'Arial', sans-serif;
  font-weight: 900;
  font-size: 20px;
  line-height: 100%;
  letter-spacing: 0px;
  color: #333333;
  text-align: right;
}

/* 功能快捷入口 */
.quick-links {
  margin: 0 16px 16px;
  background: #FFFFFF;
  border-radius: 10px;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.quick-link {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 33%;
}

.link-icon {
  width: 36px;
  height: 36px;
  background: rgba(4, 116, 252, 0.1);
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}

.link-icon img {
  width: 100%;
  height: 100%;
}

.link-text {
  font-size: 12px;
  color: #333;
}

/* 积分展示容器 */
.points-container {
  z-index: 2;
  position: relative;
  margin-top: 32px;
}

.points-content {
  margin-bottom: 24px;
}

.points-label {
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  margin-bottom: 4px;
}



/* 按钮区域 */
.action-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.love-btn-container {
  background: linear-gradient(180deg, #FF7D87 0%, #FF5C89 100%);
  border-radius: 10px 10px 0 0;
  padding: 8px 0;
  width: 118px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.love-btn {
  background: none;
  border: none;
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #FFFFFF;
  cursor: pointer;
  padding: 0;
}

.mall-link {
  display: flex;
  align-items: center;
  gap: 4px;
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  cursor: pointer;
  text-align: center;
}

/* 商品网格 */
.product-grid {
  background: #FFEEF7;
  border-radius: 10px;
  margin-top: 20px;
  padding: 16px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  flex: 1;
}

.product-card {
  background: #FFFFFF;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  cursor: pointer;
}

.product-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

.product-card:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.product-image {
  width: 100%;
  height: 102px;
  position: relative;
  overflow: hidden;
  border-radius: 4px;
  margin: 12px;
  width: calc(100% - 24px);
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-badge {
  position: absolute;
  top: 8px;
  left: 8px;
  width: 20px;
  height: 20px;
}

.product-badge img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.product-info {
  padding: 0 12px 12px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.product-name {
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.4;
  min-height: 2.8em;
}

.product-points {
  font-family: 'Arial', sans-serif;
  font-size: 20px;
  font-weight: 700;
  color: #333333;
  line-height: 1.15;
  display: flex;
  align-items: center;
  gap: 4px;
}

.heart-icon {
  flex-shrink: 0;
}

.exchange-btn {
  width: 100%;
  background: linear-gradient(180deg, #FF7D87 0%, #FF5C89 100%);
  color: #FFFFFF;
  border: none;
  padding: 8px 12px;
  border-radius: 22px;
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
  font-weight: 400;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 4px;
  height: 30px;
}

.exchange-btn:hover {
  background: linear-gradient(180deg, #FF5C89 0%, #FF1493 100%);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(255, 20, 147, 0.3);
}

.exchange-btn.disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.loading-container {
  grid-column: 1 / -1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
}

.loading-text {
  color: #999;
  font-size: 14px;
  margin-top: 10px;
}

.finished-text {
  grid-column: 1 / -1;
  text-align: center;
  color: #999;
  font-size: 14px;
  padding: 15px 0;
}

.empty-state {
  grid-column: 1 / -1;
  text-align: center;
  padding: 40px 0;
  color: #999;
  font-size: 16px;
}

/* 筛选标题样式 */
.filter-title {
  padding: 0 16px;
  margin-top: 16px;
  margin-bottom: 12px;
  text-align: left;
}

.filter-title .highlight-text {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 600;
  font-size: 16px;
  line-height: 100%;
  letter-spacing: 0px;
  vertical-align: middle;
  color: #333;
}

.filter-title .normal-text {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 600;
  font-size: 16px;
  line-height: 100%;
  letter-spacing: 0px;
  vertical-align: middle;
  color: #0474FC;
  /* 突出显示的颜色 */
}

/* 吸顶相关样式 */
.sticky-container {
  position: relative;
  z-index: 10;
  transition: all 0.3s ease;
  padding: 0 0 8px 0;
  will-change: position, transform;
  /* 提高性能 */
  display: flex;
  flex-direction: column;
}

.sticky-container .search-box {
  padding: 0 16px 8px;
}

.sticky-container.is-sticky {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: #F2F3F4;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 100;
  padding: 0;
  transform: translateZ(0);
  /* 启用GPU加速 */
}

.sticky-container.is-sticky .header {
  background: #0474FC;
  height: 50px;
}

.sticky-container.is-sticky .search-box {
  padding: 0 16px 8px;
}

.sticky-placeholder {
  width: 100%;
  transition: height 0.3s ease;
}

/* 搜索框 */
.search-box {
  height: 40px;
  background: #FFFFFF;
  border-radius: 20px;
  padding: 0 16px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  cursor: pointer;
  margin-top: 5px;
}

.search-input {
  flex: 1;
  height: 100%;
  display: flex;
  align-items: center;
  position: relative;
}

.search-input-field {
  flex: 1;
  height: 100%;
  border: none;
  outline: none;
  background: transparent;
  font-size: 14px;
  color: #333;
  width: 100%;
  padding-right: 30px;
}

.search-input-field::placeholder {
  color: #999999;
  font-size: 14px;
}

.search-icon-small {
  width: 16px;
  height: 16px;
  position: absolute;
  right: 0;
  cursor: pointer;
}

.search-clear {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #eee;
  border-radius: 50%;
  cursor: pointer;
}

.search-clear span {
  font-size: 18px;
  line-height: 18px;
  color: #999;
}

/* 筛选标签 */
.point-filter {
  display: flex;
  overflow-x: auto;
  padding: 0 16px;
  margin: 0 0 12px 0;
  /* 减少上边距，因为现在有标题 */
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  /* Firefox */
}

.point-filter::-webkit-scrollbar {
  display: none;
  /* Chrome, Safari, Edge */
}

.filter-tag {
  padding: 6px 16px;
  background: #FFFFFF;
  border-radius: 16px;
  font-size: 13px;
  color: #666;
  margin-right: 10px;
  white-space: nowrap;
  flex-shrink: 0;
  transition: all 0.3s ease;
  cursor: pointer;
}

.filter-tag.active {
  background: #0474FC;
  color: #FFFFFF;
  font-weight: 500;
}

/* 商品列表 */
.product-list {
  padding: 12px 16px;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 12px;
}

.product-item {
  background: #FFFFFF;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease;
  cursor: pointer;
  position: relative;
}

.product-item:active {
  transform: scale(0.98);
}

.product-image {
  width: 100%;
  height: 140px;
  position: relative;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-badge {
  position: absolute;
  top: 8px;
  left: 8px;
  background: #FF5C89;
  color: white;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 10px;
  z-index: 2;
}

.product-badge.hot {
  background: #FF3B30;
}

.product-info {
  padding: 10px;
}

.product-name {
  font-size: 14px;
  font-weight: 400;
  color: #333;
  margin-bottom: 5px;
  line-height: 1.3;
  height: 36px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-sold {
  font-size: 12px;
  color: #999;
  margin-bottom: 5px;
}

.sold-count {
  color: #FF5C89;
}

.product-price {
  font-size: 16px;
  font-weight: 500;
  color: #FF5C89;
}

.cart-icon {
  position: absolute;
  right: 10px;
  bottom: 10px;
  width: 26px;
  height: 26px;
  background: #0474FC;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 5px rgba(4, 116, 252, 0.3);
  cursor: pointer;
}

.cart-icon img {
  width: 14px;
  height: 14px;
}

.cart-add-animation {
  position: absolute;
  color: #FF5C89;
  font-weight: bold;
  font-size: 14px;
  animation: cartAdd 0.5s ease-out;
  top: -20px;
  right: 5px;
}

@keyframes cartAdd {
  0% {
    opacity: 1;
    transform: translateY(0);
  }

  100% {
    opacity: 0;
    transform: translateY(-20px);
  }
}

/* 底部装饰线 */
.bottom-line {
  margin-top: 20px;
  padding: 0 16px;
  position: relative;
  text-align: center;
  height: 20px;
}

.bottom-line::before,
.bottom-line::after {
  content: '';
  position: absolute;
  top: 50%;
  height: 1px;
  width: calc(50% - 80px);
  background: #DDDDDD;
}

.bottom-line::before {
  left: 16px;
}

.bottom-line::after {
  right: 16px;
}

.bottom-line-text {
  display: inline-block;
  position: relative;
  background: #F2F3F4;
  padding: 0 16px;
  font-size: 14px;
  color: #999;
}

/* 搜索图标样式 */
.search-icon {
  position: absolute;
  right: 16px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background: rgba(255, 255, 255, 0.3);
  /* 半透明白色背景 */
  border-radius: 50%;
  padding: 5px;
}
</style>
