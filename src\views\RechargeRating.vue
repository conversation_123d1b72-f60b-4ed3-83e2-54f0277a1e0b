<template>
  <div class="recharge-rating">
    <!-- 头部 -->
    <div class="header">
      <div class="back-icon" @click="goBack">
        <img src="@/assets/icons/back-icon.svg" alt="返回">
      </div>
      <div class="title">充值等级</div>
    </div>

    <!-- 等级说明 -->
    <div class="rating-intro">
      <div class="intro-title">充值等级说明</div>
      <div class="intro-content">
        充值等级是根据您累计充值金额所判定的用户等级，等级越高，享受的权益越丰富。
      </div>
    </div>

    <!-- 当前等级 -->
    <div class="current-rating">
      <div class="rating-title">当前等级</div>
      <div class="rating-level">
        <div class="level-icon">
          <img :src="currentLevel.icon" alt="等级图标">
        </div>
        <div class="level-info">
          <div class="level-name">{{ currentLevel.name }}</div>
          <div class="level-desc">{{ currentLevel.desc }}</div>
        </div>
      </div>
    </div>

    <!-- 升级进度 -->
    <div class="rating-progress">
      <div class="progress-title">
        <span>升级进度</span>
        <span class="progress-value">{{ progress.current }}/{{ progress.target }}</span>
      </div>
      <div class="progress-bar">
        <div class="progress-inner" :style="{ width: progressPercent + '%' }"></div>
      </div>
      <div class="progress-tips">
        再充值{{ progress.target - progress.current }}元即可升级为{{ nextLevel.name }}
      </div>
    </div>

    <!-- 等级权益 -->
    <div class="rating-benefits">
      <div class="benefits-title">等级权益</div>
      <div class="benefits-list">
        <div class="benefit-item" v-for="(item, index) in benefits" :key="index">
          <div class="benefit-icon">
            <img :src="item.icon" alt="权益图标">
          </div>
          <div class="benefit-info">
            <div class="benefit-name">{{ item.name }}</div>
            <div class="benefit-desc">{{ item.desc }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 等级规则 -->
    <div class="rating-rules">
      <div class="rules-title">等级规则</div>
      <div class="rules-list">
        <div class="rule-item" v-for="(item, index) in levels" :key="index">
          <div class="rule-level">
            <img :src="item.icon" alt="等级图标">
            <span>{{ item.name }}</span>
          </div>
          <div class="rule-condition">
            累计充值 {{ item.requirement }} 元
          </div>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="bottom-btn" @click="goToRecharge">
      立即充值
    </div>
  </div>
</template>

<script>

export default {
  name: 'RechargeRating',
  data() {
    return {
      levels: [
        {
          id: 1,
          name: '青铜会员',
          icon: require('@/assets/images/avatar.jpg'),
          requirement: '1,000',
          desc: '初级会员权益'
        },
        {
          id: 2,
          name: '白银会员',
          icon: require('@/assets/images/avatar.jpg'),
          requirement: '5,000',
          desc: '中级会员权益'
        },
        {
          id: 3,
          name: '黄金会员',
          icon: require('@/assets/images/avatar.jpg'),
          requirement: '10,000',
          desc: '高级会员权益'
        },
        {
          id: 4,
          name: '钻石会员',
          icon: require('@/assets/images/avatar.jpg'),
          requirement: '50,000',
          desc: '顶级会员权益'
        }
      ],
      currentLevelId: 2, // 当前等级ID
      progress: {
        current: 6800,
        target: 10000
      },
      benefits: [
        {
          name: '专属客服',
          desc: '享有一对一专属客服服务',
          icon: require('@/assets/images/avatar.jpg')
        },
        {
          name: '优先购买',
          desc: '新品上架享有优先购买权',
          icon: require('@/assets/images/avatar.jpg')
        },
        {
          name: '生日特权',
          desc: '生日当月获得专属福利',
          icon: require('@/assets/images/avatar.jpg')
        },
        {
          name: '积分加成',
          desc: '购买获得1.5倍积分',
          icon: require('@/assets/images/avatar.jpg')
        }
      ]
    }
  },
  computed: {
    currentLevel() {
      return this.levels.find(item => item.id === this.currentLevelId) || this.levels[0]
    },
    nextLevel() {
      const nextLevelId = this.currentLevelId + 1
      return this.levels.find(item => item.id === nextLevelId) || this.currentLevel
    },
    progressPercent() {
      return (this.progress.current / this.progress.target) * 100
    }
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    goToRecharge() {
      this.$router.push('/recharge')
    }
  }
}
</script>

<style scoped>
.recharge-rating {
  min-height: 100vh;
  background-color: #f6f6f6;
  padding-bottom: 80px;
}

.header {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  background-color: #fff;
  position: relative;
}

.back-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon img {
  width: 20px;
  height: 20px;
}

.title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

.rating-intro {
  background-color: #fff;
  padding: 15px;
  margin-bottom: 10px;
}

.intro-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 10px;
}

.intro-content {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.current-rating {
  background-color: #fff;
  padding: 15px;
  margin-bottom: 10px;
}

.rating-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 15px;
}

.rating-level {
  display: flex;
  align-items: center;
}

.level-icon {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  overflow: hidden;
  margin-right: 15px;
}

.level-icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.level-info {
  flex: 1;
}

.level-name {
  font-size: 18px;
  font-weight: 500;
  color: #333;
  margin-bottom: 5px;
}

.level-desc {
  font-size: 14px;
  color: #999;
}

.rating-progress {
  background-color: #fff;
  padding: 15px;
  margin-bottom: 10px;
}

.progress-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.progress-title span {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.progress-value {
  font-size: 14px;
  color: #FF618B;
}

.progress-bar {
  height: 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 10px;
}

.progress-inner {
  height: 100%;
  background-color: #FF618B;
  border-radius: 4px;
}

.progress-tips {
  font-size: 12px;
  color: #999;
}

.rating-benefits {
  background-color: #fff;
  padding: 15px;
  margin-bottom: 10px;
}

.benefits-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 15px;
}

.benefits-list {
  display: flex;
  flex-wrap: wrap;
}

.benefit-item {
  width: 50%;
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.benefit-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  overflow: hidden;
  margin-right: 10px;
}

.benefit-icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.benefit-info {
  flex: 1;
}

.benefit-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 5px;
}

.benefit-desc {
  font-size: 12px;
  color: #999;
}

.rating-rules {
  background-color: #fff;
  padding: 15px;
}

.rules-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 15px;
}

.rule-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f5f5f5;
}

.rule-item:last-child {
  border-bottom: none;
}

.rule-level {
  display: flex;
  align-items: center;
}

.rule-level img {
  width: 30px;
  height: 30px;
  border-radius: 15px;
  margin-right: 10px;
}

.rule-level span {
  font-size: 14px;
  color: #333;
}

.rule-condition {
  font-size: 14px;
  color: #666;
}

.bottom-btn {
  position: fixed;
  bottom: 20px;
  left: 16px;
  right: 16px;
  height: 44px;
  background-color: #FF618B;
  color: #fff;
  font-size: 16px;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
