<template>
  <div class="live">
    <div class="header">
      <span class="title">直播间</span>
      <van-icon name="replay" color="#333" size="18" @click="refreshLive" />
    </div>

    <!-- 加载中状态 -->
    <div v-if="loading" class="loading-container">
      <van-loading type="spinner" color="#FF618B" />
      <p>正在获取直播链接，即将跳转...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <van-icon name="warning-o" color="#ff4444" size="48" />
      <p class="error-message">{{ errorMessage }}</p>
      <van-button type="primary" size="small" @click="loadLiveUrl">重新获取</van-button>
    </div>

    <tab-bar :active="3"></tab-bar>
  </div>
</template>

<script>
import TabBar from '@/components/TabBar.vue'

export default {
  name: 'Live',
  components: {
    TabBar
  },  data() {
    return {
      loading: true,
      error: false,
      errorMessage: '',
      userToken: localStorage.getItem('user_token') || ''
    }
  },
  mounted() {
    this.loadLiveUrl()
  },
  methods: {
    async loadLiveUrl() {
      try {
        this.loading = true
        this.error = false
        this.errorMessage = ''

        // 检查用户token
        if (!this.userToken) {
          this.handleError('用户未登录，请先登录')
          return
        }

        // 发送API请求
        const response = await this.$http.get('/lobby/thirdpartyuserlogin', {
          headers: {
            'authorization': this.userToken
          }
        })        // 检查响应状态
        if (response.data.code === 200 && response.data.data && response.data.data.url) {
          // 直接重定向到获取的URL
          window.location.href = response.data.data.url
        } else {
          this.handleError(response.data.msg || '获取直播链接失败')
        }
      } catch (error) {
        console.error('直播链接请求失败:', error)
        
        // 处理不同类型的错误
        if (error.response) {
          const status = error.response.status
          if (status === 401 || status === 403) {
            this.handleError('登录已过期，请重新登录')
            // 清除过期token并跳转到登录页
            localStorage.removeItem('user_token')
            this.$router.push('/login')
            return
          } else if (status === 404) {
            this.handleError('直播服务暂时不可用')
          } else {
            this.handleError(`请求失败 (${status})`)
          }
        } else if (error.request) {
          this.handleError('网络连接失败，请检查网络')
        } else {
          this.handleError('加载直播间失败')
        }
      }
    },

    handleError(message) {
      this.loading = false
      this.error = true
      this.errorMessage = message
      this.$toast.fail(message)
    },    refreshLive() {
      this.loadLiveUrl()
    }
  }
}
</script>


<style scoped>
.live {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 50px;
  position: relative;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  background-color: #fff;
  position: relative;
  z-index: 10;
}

.header .title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 140px);
  padding: 40px 20px;
}

.loading-container p {
  margin-top: 15px;
  color: #666;
  font-size: 14px;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 140px);
  padding: 40px 20px;
  text-align: center;
}

.error-message {
  margin: 15px 0 20px 0;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header {
    padding: 12px 15px;
  }
  
  .header .title {
    font-size: 16px;
  }
    .loading-container,
  .error-container {
    min-height: calc(100vh - 120px);
    padding: 30px 15px;
  }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .live {
    background-color: #1a1a1a;
  }
  
  .header {
    background-color: #2a2a2a;
  }
  
  .header .title {
    color: #fff;
  }
  
  .loading-container p,
  .error-message {
    color: #ccc;
  }
}
</style>
