<template>
  <div class="sign-in">
    <!-- 返回按钮 -->
    <div class="header">
      <div class="back-btn" @click="goBack">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <path d="M15 18L9 12L15 6" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
        </svg>
      </div>
      <div class="page-title">幸运大抽奖</div>
    </div> <!-- 大标题区域 -->
    <div class="title-section">
      <img src="@/assets/images/signin/title-section.png" alt="幸运大转盘" />
    </div>

    <!-- 转盘区域 -->
    <div class="wheel-container">
      <div class="wheel-wrapper">
        <!-- 转盘背景装饰 -->
        <div class="wheel-decoration"></div> <!-- Canvas转盘 -->
        <canvas ref="wheelCanvas" class="wheel-canvas" :class="{ spinning: isSpinning }"
          :style="{ transform: `rotate(${wheelRotation}deg)` }"></canvas>

        <!-- 中央按钮 -->
        <div class="center-button" @click="spinWheel" :class="{ spinning: isSpinning }">
          <div class="center-button-inner">
            <div class="center-text">开始</div>
            <div class="center-text">抽奖</div>
          </div>
        </div>
      </div>
    </div> <!-- 底部按钮区 -->
    <div class="bottom-buttons">
      <div class="btn-item" @click="showRules">
        <img src="@/assets/images/signin/guize.png" alt="活动规则" class="btn-img" />
      </div>
      <div class="btn-item" @click="showMyPrizes">
        <img src="@/assets/images/signin/record-btn.png" alt="我的奖品" class="btn-img" />
      </div>
    </div>

    <!-- 活动规则弹窗 -->
    <van-popup v-model="showRulesPopup" position="center" class="rules-popup">
      <div class="popup-content">
        <div class="popup-header">
          <h3>活动规则</h3>
          <van-icon name="cross" @click="showRulesPopup = false" />
        </div>
        <div class="popup-body">
          <div class="rule-content">
            <template v-if="Array.isArray(activityRules)">
              <p v-for="(rule, index) in activityRules" :key="index">{{ rule }}</p>
            </template>
            <template v-else>
              <div v-html="activityRules"></div>
            </template>
          </div>
        </div>
        <div class="popup-footer">
          <van-button type="primary" @click="showRulesPopup = false">我知道了</van-button>
        </div>
      </div>
    </van-popup>

    <!-- 我的奖品弹窗 -->
    <van-popup v-model="showPrizesPopup" position="center" class="prizes-popup">
      <div class="popup-content">
        <div class="popup-header">
          <h3>我的奖品</h3>
          <van-icon name="cross" @click="showPrizesPopup = false" />
        </div>
        <div class="popup-body">
          <div v-if="myPrizes.length === 0" class="empty-state">
            <p>暂无奖品</p>
          </div>
          <div v-else class="prize-list">
            <div v-for="(prize, index) in myPrizes" :key="index" class="prize-item">
              <div class="prize-info">
                <span class="prize-name">{{ prize.name }}</span>
                <span class="prize-time">{{ prize.time }}</span>
              </div>
              <div class="prize-status">{{ prize.status }}</div>
            </div>
          </div>
        </div>
        <div class="popup-footer">
          <van-button type="primary" @click="showPrizesPopup = false">确定</van-button>
        </div>
      </div>
    </van-popup>

    <!-- 中奖结果弹窗 -->
    <van-popup v-model="showResultPopup" position="center" class="result-popup">
      <div class="popup-content">
        <div class="popup-header">
          <h3>恭喜您</h3>
        </div>
        <div class="popup-body">
          <div class="result-icon">🎉</div>
          <p class="result-text">获得 {{ prizeResult }}</p>
        </div>
        <div class="popup-footer">
          <van-button type="primary" @click="showResultPopup = false">确定</van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { Icon, Button, Popup, Toast } from 'vant'

export default {
  name: 'SignIn',
  components: {
    VanIcon: Icon,
    VanButton: Button,
    VanPopup: Popup
  },
  data() {
    return {
      wheelRotation: 0,
      isSpinning: false,
      showRulesPopup: false,
      showPrizesPopup: false,
      showResultPopup: false,
      prizeResult: '',
      prizeValue: 0,
      prizeValueUnit: '积分',
      myPrizes: [],
      activityRules: '暂无活动规则', // 奖品配置 - 可以是字符串或数组
      prizes: [
        { text: '50积分', value: '50', valueUnit: '积分', weight: 10 },
        { text: '100积分', value: '100', valueUnit: '积分', weight: 5 },
        { text: '500积分', value: '500', valueUnit: '积分', weight: 1 },
        { text: '再接再厉', value: '0', valueUnit: '', weight: 20 },
        { text: '10积分', value: '10', valueUnit: '积分', weight: 15 },
        { text: '3积分', value: '3', valueUnit: '积分', weight: 20 },
        { text: '5积分', value: '5', valueUnit: '积分', weight: 18 },
        { text: '1积分', value: '1', valueUnit: '积分', weight: 25 }
      ]
    }
  }, mounted() {
    // 获取抽奖配置
    this.getLotteryConfig()

    // 加载我的奖品列表
    this.loadMyPrizes()

    // 确保DOM完全加载后再绘制转盘
    this.$nextTick(() => {
      setTimeout(() => {
        this.drawWheel()
      }, 100)
    })

    // 监听窗口尺寸变化
    window.addEventListener('resize', this.handleResize)
  },

  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    }, spinWheel() {
      if (this.isSpinning) {
        Toast('正在转盘中，请稍候...')
        return
      }
      // 获取用户令牌
      const userToken = localStorage.getItem('user_token')
      // 不检查登录状态，允许未登录用户参与抽奖

      this.isSpinning = true
      Toast('转盘启动中...')

      // 调用抽奖结果API
      this.submitLotteryResult()
    },
    // 提交抽奖结果API
    submitLotteryResult() {
      const userToken = localStorage.getItem('user_token')

      // 准备请求头，如果有令牌则添加到请求中
      const headers = userToken ? { 'authorization': userToken } : {}

      this.$http.post('/lottery/getResult', {}, {
        headers: headers
      })
        .then(response => {
          if (response.data.code === 200) {
            const data = response.data.data
            // API返回的数据结构示例:
            // { index: 2, id: 8, name: "八等奖", value: "18积分" }
            const prizeInfo = data || {}

            // 从API获取中奖结果
            const selectedPrize = {
              text: `${prizeInfo.name || ''}${prizeInfo.value || ''}` || '未知奖品',
              angle: 0, // 将根据奖品在prizes数组中的位置计算
              id: prizeInfo.id,
              prizeName: prizeInfo.name || '',
              prizeValue: prizeInfo.value || '',
              index: prizeInfo.index || 0
            }// 在当前奖品列表中找到对应奖品的角度和价值信息
            const matchingPrizeIndex = this.prizes.findIndex(p => p.text === selectedPrize.text || p.id === selectedPrize.id)
            if (matchingPrizeIndex !== -1) {
              // 根据奖品在数组中的位置计算角度
              selectedPrize.angle = matchingPrizeIndex * (360 / this.prizes.length)
            } else if (prizeInfo.index !== undefined) {
              // 使用API返回的index来计算角度
              selectedPrize.angle = prizeInfo.index * (360 / this.prizes.length)
            } else {
              // 如果没找到匹配的奖品，随机选择一个角度
              selectedPrize.angle = Math.random() * 360
            }

            // 计算旋转角度 (多转几圈 + 目标角度)
            const spins = 5 + Math.random() * 3 // 5-8圈
            const targetAngle = 360 - selectedPrize.angle
            const totalRotation = this.wheelRotation + spins * 360 + targetAngle
            this.wheelRotation = totalRotation
            this.prizeResult = selectedPrize.text
            this.prizeValue = selectedPrize.prizeValue
            this.prizeValueUnit = ''

            // 转盘停止后显示结果
            setTimeout(() => {
              this.isSpinning = false
              this.showResultPopup = true

              // 刷新我的奖品列表
              this.loadMyPrizes()            // 根据奖品类型显示不同的消息
              setTimeout(() => Toast(`恭喜获得${selectedPrize.text}！`), 1000)
            }, 3000)

          } else {
            this.isSpinning = false
            Toast(response.data.msg || '抽奖失败，请稍后重试')
          }
        })
        .catch(error => {
          this.isSpinning = false
          console.error('抽奖API调用失败:', error)

          // 如果是网络错误，进行本地模拟
          if (error.code === 'NETWORK_ERROR' || !navigator.onLine) {
            Toast('网络异常，使用离线模式')
            this.simulateLocalSpin()
          } else {
            Toast('抽奖失败，请稍后重试')
          }
        })
    },

    // 本地模拟抽奖（网络异常时的降级方案）
    simulateLocalSpin() {
      // 根据权重随机选择奖品
      const totalWeight = this.prizes.reduce((sum, prize) => sum + prize.weight, 0)
      let random = Math.random() * totalWeight
      let selectedPrize = this.prizes[0]

      for (const prize of this.prizes) {
        random -= prize.weight
        if (random <= 0) {
          selectedPrize = prize
          break
        }
      }      // 计算旋转角度
      const spins = 5 + Math.random() * 3
      const targetAngle = 360 - selectedPrize.angle
      const totalRotation = this.wheelRotation + spins * 360 + targetAngle

      this.wheelRotation = totalRotation
      this.prizeResult = selectedPrize.text
      this.prizeValue = selectedPrize.value || 0
      this.prizeValueUnit = selectedPrize.valueUnit || '积分'

      setTimeout(() => {
        this.isSpinning = false
        this.showResultPopup = true

        // 本地保存奖品记录
        const newPrize = {
          name: selectedPrize.text,
          time: new Date().toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
          }), status: '已到账'
        }
        this.myPrizes.unshift(newPrize)
        localStorage.setItem('myPrizes', JSON.stringify(this.myPrizes))

        setTimeout(() => Toast(`恭喜获得${selectedPrize.text}！`), 1000)
      }, 3000)
    },
    showRules() {
      this.showRulesPopup = true
    }, showMyPrizes() {
      // 先加载最新的奖品数据，然后显示弹窗
      this.loadMyPrizes().then(() => {
        this.showPrizesPopup = true
      })
    },
    // 加载我的奖品列表
    loadMyPrizes() {
      return new Promise((resolve, reject) => {
        const userToken = localStorage.getItem('user_token')

        // 准备请求头，如果有令牌则添加到请求中
        const headers = userToken ? { 'authorization': userToken } : {}

        // 无论是否登录，都尝试调用API
        // 调用获取奖品记录API
        this.$http.get('/lottery/getLog', {
          headers: headers,
          params: {
            page: 1,
            pageSize: 20
          }
        })
          .then(response => {
            if (response.data.code === 200) {
              const data = response.data.data
              // 处理API返回的奖品数据 - 支持多种数据结构
              const prizeList = data.list || data.prizes || data.data || []
              if (prizeList && Array.isArray(prizeList)) {
                this.myPrizes = prizeList.map(item => ({
                  name: `${item.name || ''}${item.value || ''}` || item.details || '未知奖品',
                  time: this.formatTime(item.created_at || item.create_time || item.time || Date.now()),
                  status: this.formatStatus(item.status),
                  id: item.id,
                  // 支持更多字段
                  amount: item.amount || item.prize_value || item.value,
                  type: item.type || item.prize_type,
                  // 原始数据用于显示
                  prizeName: item.name || '',
                  prizeValue: item.value || ''
                }))
              } else {
                this.myPrizes = []
              }

              // 更新本地存储
              localStorage.setItem('myPrizes', JSON.stringify(this.myPrizes))
              resolve()
            } else {
              // API失败时从本地存储加载
              console.warn('获取奖品记录失败:', response.data.msg)
              const savedPrizes = localStorage.getItem('myPrizes')
              if (savedPrizes) {
                this.myPrizes = JSON.parse(savedPrizes)
              }
              resolve()
            }
          })
          .catch(error => {
            console.error('获取奖品记录API调用失败:', error)
            // 网络异常时从本地存储加载
            const savedPrizes = localStorage.getItem('myPrizes')
            if (savedPrizes) {
              this.myPrizes = JSON.parse(savedPrizes)
            }
            resolve()
          })
      })
    }, showPrizeRecord() {
      if (this.myPrizes.length === 0) {
        Toast('暂无中奖记录')
      } else {
        this.showPrizesPopup = true
      }
    },    // 绘制转盘    
    drawWheel() {
      const canvas = this.$refs.wheelCanvas
      if (!canvas) {
        return
      }

      const ctx = canvas.getContext('2d')

      // 设置固定尺寸
      const size = 300
      canvas.width = size
      canvas.height = size
      canvas.style.width = size + 'px'
      canvas.style.height = size + 'px'

      const centerX = size / 2
      const centerY = size / 2
      const radius = 135

      // 清空画布
      ctx.clearRect(0, 0, size, size)

      // 绘制圆形外边框
      ctx.beginPath()
      ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI)
      ctx.strokeStyle = '#FF7062'
      ctx.lineWidth = 1
      ctx.stroke()

      // 根据Figma设计调整转盘颜色
      const defaultColors = [
        { bgColor: '#FEF7EA', textColor: '#D6572D' }, // 50积分
        { bgColor: '#FCE7B5', textColor: '#D6572D' }, // 100积分
        { bgColor: '#FEF7EA', textColor: '#D6572D' }, // 500积分
        { bgColor: '#FCE7B5', textColor: '#FF4A39' }, // 再接再厉
        { bgColor: '#FEF7EA', textColor: '#D6572D' }, // 10积分
        { bgColor: '#FCE7B5', textColor: '#D6572D' }, // 3积分
        { bgColor: '#FEF7EA', textColor: '#D6572D' }, // 5积分
        { bgColor: '#FCE7B5', textColor: '#D6572D' }  // 1积分
      ]

      // 动态生成转盘配置，基于prizes数据
      const wheelPrizes = this.prizes.map((prize, index) => {
        const colorConfig = defaultColors[index % defaultColors.length]

        return {
          text: prize.text,
          angle: prize.angle,
          bgColor: prize.bgColor || colorConfig.bgColor,
          textColor: prize.textColor || colorConfig.textColor,
          id: prize.id
        }
      })
      // 计算每个扇形的角度
      const anglePerPrize = 360 / wheelPrizes.length

      // 第一步：绘制所有扇形背景
      wheelPrizes.forEach((prize, index) => {
        const adjustedAngle = index * anglePerPrize - 90 // 从顶部开始
        const startAngle = (adjustedAngle - anglePerPrize / 2) * Math.PI / 180
        const endAngle = (adjustedAngle + anglePerPrize / 2) * Math.PI / 180

        // 绘制扇形
        ctx.beginPath()
        ctx.moveTo(centerX, centerY)
        ctx.arc(centerX, centerY, radius, startAngle, endAngle)
        ctx.closePath()
        ctx.fillStyle = prize.bgColor
        ctx.fill()

        // 绘制扇形边框
        ctx.strokeStyle = '#FF7062'
        ctx.lineWidth = 1
        ctx.stroke()
      })
      // 第二步：绘制所有文字
      wheelPrizes.forEach((prize, index) => {
        ctx.save()

        // 移动到中心并旋转
        const adjustedAngle = index * anglePerPrize - 90
        ctx.translate(centerX, centerY)
        ctx.rotate(adjustedAngle * Math.PI / 180)

        // 设置文字样式 - 根据奖品数量动态调整字体大小
        // 使用HarmonyOS Sans SC字体，和Figma设计一致
        const fontSize = Math.max(12, Math.min(14, 140 / wheelPrizes.length))
        ctx.font = `900 ${fontSize}px "HarmonyOS Sans SC", Arial, sans-serif`
        ctx.textAlign = 'center'
        ctx.textBaseline = 'middle'
        ctx.fillStyle = prize.textColor
        ctx.lineWidth = 0.5 // 细描边

        // 轻微文字阴影
        ctx.shadowColor = 'rgba(0, 0, 0, 0.2)'
        ctx.shadowBlur = 1
        ctx.shadowOffsetX = 0
        ctx.shadowOffsetY = 1

        // 文字位置（调整到扇形的外围位置，和Figma设计一致）
        const textY = -radius * 0.65

        // 准备显示文本
        const prizeText = prize.text
        const prizeValue = this.prizes.find(p => p.text === prize.text)?.value
        const prizeValueUnit = this.prizes.find(p => p.text === prize.text)?.valueUnit

        // 绘制奖品名称 - 优化文字显示逻辑
        if (prizeText.length > 4) {
          // 优化分行逻辑
          let lines = [];
          // 更严格的分行，只允许每行最多4个字符
          const charsPerLine = 4;

          // 将文字每3个字符进行分行
          for (let i = 0; i < prizeText.length; i += charsPerLine) {
            lines.push(prizeText.substring(i, i + charsPerLine));
          }

          // 计算文字位置和行间距
          const lineHeight = fontSize * 1.8; // 进一步增加行间距
          const totalHeight = lineHeight * (lines.length - 1);
          const startY = textY - totalHeight / 2;
          // 绘制每行文字
          lines.forEach((line, i) => {
            const lineY = startY + (i * lineHeight);

            // 清除之前的阴影设置
            ctx.shadowColor = 'transparent';

            // 先绘制描边，使用较宽的描边强化文字边界
            ctx.lineWidth = 2; // 描边宽度适中
            ctx.strokeStyle = prize.textColor === '#FFFFFF' ? '#000000' : '#FFFFFF';
            ctx.strokeText(line, 0, lineY);

            // 恢复阴影设置并填充文字
            ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
            ctx.fillText(line, 0, lineY);
          });
        } else {
          // 短文字绘制
          // 先绘制描边效果，提高可读性
          ctx.lineWidth = 2;
          ctx.strokeStyle = prize.textColor === '#FFFFFF' ? '#000000' : '#FFFFFF';
          ctx.strokeText(prizeText, 0, textY);

          // 然后填充文字
          ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
          ctx.fillText(prizeText, 0, textY);
        }
        // 价值信息已包含在文本中，不需要单独显示

        ctx.restore()
      })

      // 第三步：绘制装饰边框
      ctx.beginPath()
      ctx.arc(centerX, centerY, radius + 3, 0, 2 * Math.PI)
      ctx.strokeStyle = '#333333'
      ctx.lineWidth = 4
      ctx.stroke()

      // 内圈装饰
      ctx.beginPath()
      ctx.arc(centerX, centerY, 40, 0, 2 * Math.PI)
      ctx.fillStyle = '#FFD700'
      ctx.fill()
      ctx.strokeStyle = '#333333'
      ctx.lineWidth = 3
      ctx.stroke()
    },    // 获取抽奖配置
    getLotteryConfig() {
      const userToken = localStorage.getItem('user_token')

      // 准备请求头，如果有令牌则添加到请求中
      const headers = userToken ? { 'authorization': userToken } : {}

      // 调用抽奖初始化API
      this.$http.get('/lottery/getInit', {
        headers: headers
      })
        .then(response => {
          if (response.data.code === 200) {
            const data = response.data.data
            // 处理奖品配置 - 支持多种API数据结构
            const prizesList = data.prizes || data.list || data.prize_list || []
            if (prizesList && Array.isArray(prizesList)) {            // 根据API返回的奖品数据更新转盘配置              
              this.prizes = prizesList.map((prize, index) => ({
                text: prize.value || prize.amount || prize.prize_value || prize.point || prize.name || '未知奖品',
                angle: index * (360 / prizesList.length),
                weight: prize.weight || prize.probability || prize.rate || 10,
                color: prize.color || prize.bg_color || this.getDefaultColor(index),
                id: prize.id,
                // 支持更多奖品属性
                bgColor: prize.bg_color || prize.bgColor,
                textColor: prize.text_color || prize.textColor,
                // 添加价值字段支持
                value: prize.value || prize.amount || prize.prize_value || prize.point || 0,
                valueUnit: prize.value_unit || prize.unit || '积分'
              }))

              console.log('抽奖配置已更新:', this.prizes)
            } else {
              console.warn('API未返回有效的奖品配置，使用默认配置')
            }
            // 处理用户信息
            if (data.userInfo || data.user) {
              const userInfo = data.userInfo || data.user
              // 可以在这里处理其他用户信息
            }            // 处理活动规则
            if (data.rules && Array.isArray(data.rules)) {
              // 优先使用数组格式的规则
              this.activityRules = data.rules
              console.log('活动规则(数组):', data.rules)
            } else if (data.des) {
              // 如果没有数组规则，但有描述文本，使用描述文本
              this.activityRules = data.des
              console.log('活动规则(文本):', data.des)
            }

            // 重新绘制转盘
            this.$nextTick(() => {
              this.drawWheel()
            })
          } else {
            console.warn('获取抽奖配置失败:', response.data.msg)
            Toast(response.data.msg || '获取抽奖配置失败')
          }
        })
        .catch(error => {
          console.error('抽奖配置API调用失败:', error)
          if (error.response && error.response.status === 401) {
            Toast('登录已过期，请重新登录')
            localStorage.removeItem('user_token')
            this.$router.push('/login')
          } else {
            console.warn('网络异常，使用默认配置')
          }
        })
    },

    // 获取默认颜色
    getDefaultColor(index) {
      const colors = ['#FFE4B5', '#FFF8DC', '#FFC483', '#FFE4B5', '#FFF8DC', '#FFC483', '#FFF8DC', '#FFE4B5']
      return colors[index % colors.length]
    },
    // 处理窗口尺寸变化
    handleResize() {
      this.$nextTick(() => {
        this.drawWheel()
      })
    },

    // 格式化时间
    formatTime(timeValue) {
      if (!timeValue) {
        return new Date().toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        })
      }

      // 如果是时间戳
      if (typeof timeValue === 'number') {
        return new Date(timeValue).toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        })
      }

      // 如果是字符串格式的时间
      if (typeof timeValue === 'string') {
        const date = new Date(timeValue)
        if (isNaN(date.getTime())) {
          // 如果解析失败，返回原字符串
          return timeValue
        }
        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        })
      }

      return timeValue
    },

    // 格式化状态
    formatStatus(status) {
      if (typeof status === 'number') {
        switch (status) {
          case 0: return '待发放'
          case 1: return '已到账'
          case 2: return '已发放'
          case -1: return '已失效'
          default: return '未知状态'
        }
      }

      if (typeof status === 'string') {
        // 处理字符串状态
        const statusMap = {
          'pending': '待发放',
          'success': '已到账',
          'completed': '已发放',
          'delivered': '已发放',
          'expired': '已失效',
          'failed': '发放失败'
        }
        return statusMap[status.toLowerCase()] || status
      }

      return '已到账' // 默认状态
    }
  }
}
</script>

<style scoped>
.sign-in {
  min-height: 100vh;
  background: #f7f7f7;
  background-image: url('@/assets/images/signin/page-bg.png');
  background-repeat: no-repeat;
  background-position: center top;
  position: relative;
  overflow: hidden;
}

/* 头部返回按钮 */
.header {
  padding: 18px 16px;
  display: flex;
  justify-content: center;
  position: relative;
}

.back-btn {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: absolute;
  left: 16px;
}

.page-title {
  font-size: 16px;
  font-weight: 400;
  color: #333;
  line-height: 24px;
}

/* 大标题区域*/
.title-section {
  width: 276px;
  text-align: center;
  margin: 35px auto 20px;
  position: relative;
  z-index: 2;
}

.title-section img {
  width: 100%;
  max-width: 276px;
  height: auto;
}

/* 转盘容器 */
.wheel-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 10px 0 30px;
  position: relative;
  padding: 10px;
}

.wheel-wrapper {
  position: relative;
  width: 300px;
  height: 300px;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 转盘背景装饰 */
.wheel-decoration {
  position: absolute;
  width: 412px;
  height: 412px;
  background: url('@/assets/images/signin/wheel-bg.png') no-repeat center;
  background-size: contain;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
}

/* Canvas转盘 */
.wheel-canvas {
  width: 100%;
  height: 100%;
  transition: transform 3s cubic-bezier(0.23, 1, 0.32, 1);
  border-radius: 50%;
  position: relative;
  z-index: 2;
  background-color: #FEF7EA;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.wheel-canvas.spinning {
  box-shadow:
    0 12px 40px rgba(255, 112, 98, 0.4),
    inset 0 2px 4px rgba(255, 255, 255, 0.3),
    0 0 20px rgba(255, 112, 98, 0.2);
}

/* 指针样式已移除 */

/* 中央按钮 */
.center-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 70px;
  height: 70px;
  cursor: pointer;
  z-index: 6;
  transition: transform 0.3s ease;
}

.center-button:hover {
  transform: translate(-50%, -50%) scale(1.1);
}

.center-button.spinning {
  pointer-events: none;
}

.center-button-inner {
  width: 100%;
  height: 100%;
  background: #FEF7EA;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: 1px solid #FF7062;
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.2),
    inset 0 2px 4px rgba(255, 255, 255, 0.3);
}

.center-text {
  color: #D6572D;
  font-size: 15px;
  font-weight: 900;
  font-family: 'HarmonyOS Sans SC', sans-serif;
  line-height: 1.17;
}

.center-text:first-child {
  margin-bottom: 2px;
}

/* 底部按钮区 */
.bottom-buttons {
  display: flex;
  justify-content: center;
  gap: 11px;
  padding: 0 20px;
  margin-bottom: 30px;
  /* 上移20px，原来是50px */
  width: 100%;
  max-width: 375px;
  position: relative;
  z-index: 10;
  /* 确保按钮显示在顶层 */
}

.btn-item {
  cursor: pointer;
  transition: transform 0.3s ease;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 112px;
  height: 46px;
  z-index: 11;
  /* 确保每个按钮项都显示在顶层 */
}

.btn-item:hover {
  transform: scale(1.05);
}

.btn-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 移除不再使用的样式 */

/* 弹窗样式 */
.popup-content {
  background: #fff;
  border-radius: 12px;
  width: 300px;
  max-height: 400px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.popup-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.popup-body {
  padding: 20px;
  max-height: 250px;
  overflow-y: auto;
}

.popup-footer {
  padding: 0 20px 20px;
  text-align: center;
}

/* 活动规则弹窗 */
.rule-content {
  color: #666;
  line-height: 1.6;
  white-space: pre-wrap;
  max-height: 200px;
  overflow-y: auto;
  font-size: 0.48rem;
}

.rule-content p {
  margin: 10px 0;
  padding-bottom: 8px;
  border-bottom: 1px dashed #eee;
}

.rule-content p:last-child {
  border-bottom: none;
}

.rule-item {
  margin-bottom: 15px;
}

.rule-item p {
  margin: 0;
  color: #666;
  line-height: 1.5;
}

/* 我的奖品弹窗 */
.empty-state {
  text-align: center;
  color: #999;
  padding: 40px 0;
}

.prize-list {
  max-height: 200px;
  overflow-y: auto;
}

.prize-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}

.prize-item:last-child {
  border-bottom: none;
}

.prize-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.prize-name {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.prize-time {
  font-size: 12px;
  color: #999;
}

.prize-status {
  color: #52c41a;
  font-size: 14px;
}

/* 中奖结果弹窗 */
.result-popup .popup-content {
  text-align: center;
}

.result-icon {
  font-size: 48px;
  margin: 20px 0;
}

.result-text {
  font-size: 18px;
  color: #333;
  margin: 0 0 20px 0;
  font-weight: 600;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .wheel-wrapper {
    width: 280px;
    height: 280px;
  }

  .center-button {
    width: 65px;
    height: 65px;
  }

  .center-text {
    font-size: 11px;
  }

  .lucky-title {
    font-size: 50px;
  }

  .subtitle {
    font-size: 25px;
  }
}

@media (max-width: 320px) {
  .wheel-wrapper {
    width: 250px;
    height: 250px;
  }

  .center-button {
    width: 60px;
    height: 60px;
  }

  .center-text {
    font-size: 10px;
  }
}
</style>
