# 项目上下文信息

## 当前开发状态
- **活跃分支**: main
- **开发环境**: 本地开发服务器 http://localhost:8081
- **最后更新**: 2025-05-29
- **当前任务**: 收益页面功能完善

## 环境配置
```json
{
  "nodeVersion": "推荐 16+",
  "vueVersion": "2.7.16",
  "vantVersion": "2.12.54",
  "axiosVersion": "1.9.0"
}
```

## 文件结构要点
- **核心页面**: 33个Vue组件，位于 `/src/views/`
- **公共组件**: StatusBar.vue, TabBar.vue
- **工具类**: ImageErrorHandler.js
- **样式文件**: mobile.css (移动端适配)
- **路由配置**: /src/router/index.js

## API接口清单
```javascript
// 主要API端点
const API_ENDPOINTS = {
  // 用户相关
  login: '/member/login',
  register: '/member/register',
  profile: '/member/getInfo',
  
  // 收益相关
  income: '/member/getIncomeList',
  
  // 商城相关
  products: '/shop/getList',
  purchase: '/shop/doBuy',
  
  // 团队相关
  team: '/member/getTeam',
  invite: '/member/getInvite'
}
```

## 组件依赖关系
- **全局依赖**: Vant UI组件库
- **图片处理**: ImageErrorHandler工具类
- **HTTP请求**: Axios + 全局配置
- **移动端适配**: amfe-flexible + postcss-pxtorem

## 测试要点
1. **功能测试**: 每个页面的基本功能
2. **兼容性测试**: 不同移动设备和浏览器
3. **网络测试**: 弱网环境下的表现
4. **用户体验测试**: 加载状态和错误处理
