// 登录认证帮助工具类
export default {
    // 检查用户是否已登录，如果未登录则跳转到登录页
    checkLoginAndRedirect(vm) {
        const userToken = localStorage.getItem('user_token')
        if (!userToken) {
            vm.$toast('请先登录')
            // 使用 $nextTick 确保 toast 显示后再跳转
            vm.$nextTick(() => {
                vm.$router.replace('/login')
            })
            return false
        }
        return true
    },

    // 获取用户token
    getUserToken() {
        return localStorage.getItem('user_token')
    },

    // 检查是否已登录（仅返回布尔值，不执行跳转）
    isLoggedIn() {
        return !!localStorage.getItem('user_token')
    }
}
