<template>
  <div class="customer-service">
    <!-- 头部 -->
    <div class="header">
      <div class="back-icon" @click="goBack">
        <img src="@/assets/icons/back-icon.svg" alt="返回">
      </div>
      <div class="title">客服中心</div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <van-loading type="spinner" color="#0474FC">加载中...</van-loading>
    </div>

    <!-- 客服信息 -->
    <div v-else class="service-info">
      <div class="service-avatar">
        <img src="@/assets/images/avatar.jpg" alt="客服头像">
      </div>
      <div class="service-name">在线客服</div>
      <div class="service-status">
        <span class="status-dot"></span>
        <span class="status-text">在线</span>
      </div>
      <div class="service-desc">服务时间9:00-18:00</div>
      <div class="contact-btn" @click="contactService">
        <van-icon name="service-o" size="20" />
        <span>联系客服</span>
      </div>
    </div>


  </div>
</template>

<script>

export default {
  name: 'CustomerService',
  data() {
    return {
      loading: false,
      serviceUrl: '' // 客服链接地址
    }
  },
  mounted() {
    this.getServiceInfo()
  },
  methods: {    // 获取客服信息
    async getServiceInfo() {
      this.loading = true
      try {
        const response = await this.$http.get('/Lobby/getVersion')

        if (response.data.code === 200) {
          this.serviceUrl = response.data.data.service || ''
        } else {
          this.$toast(response.data.msg || '获取客服信息失败')
        }
      } catch (error) {
        console.error('获取客服信息失败:', error)
        this.$toast('网络错误，请稍后重试')
      } finally {
        this.loading = false
      }
    },

    goBack() {
      this.$router.go(-1)
    },

    contactService() {
      if (this.serviceUrl) {
        // 在新窗口打开客服链接
        window.open(this.serviceUrl, '_blank')
      } else {
        this.$toast('客服暂时不可用，请稍后重试')
      }
    }
  }
}
</script>

<style scoped>
.customer-service {
  min-height: 100vh;
  background-color: #f6f6f6;
  padding-bottom: 20px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 50px 0;
}

.header {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  background-color: #fff;
  position: relative;
}

.back-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon img {
  width: 20px;
  height: 20px;
}

.title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

.service-info {
  background-color: #fff;
  padding: 20px;
  margin: 15px;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.service-avatar {
  width: 80px;
  height: 80px;
  border-radius: 40px;
  overflow: hidden;
  margin-bottom: 10px;
}

.service-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.service-name {
  font-size: 18px;
  font-weight: 500;
  color: #333;
  margin-bottom: 5px;
}

.service-status {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.status-dot {
  width: 8px;
  height: 8px;
  background-color: #52C41A;
  border-radius: 4px;
  margin-right: 5px;
}

.status-text {
  font-size: 14px;
  color: #52C41A;
}

.service-desc {
  font-size: 14px;
  color: #999;
  margin-bottom: 15px;
}

.contact-btn {
  background-color: #0474FC;
  color: #fff;
  padding: 10px 20px;
  border-radius: 20px;
  display: flex;
  align-items: center;
}

.contact-btn span {
  margin-left: 5px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  padding: 15px;
}

.faq-section,
.contact-section {
  background-color: #fff;
  margin: 15px;
  border-radius: 10px;
}

.faq-list {
  padding: 0 15px 15px;
}

.faq-item {
  border-bottom: 1px solid #f5f5f5;
  padding: 15px 0;
}

.faq-item:last-child {
  border-bottom: none;
}

.faq-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.faq-question {
  font-size: 16px;
  color: #333;
}

.faq-answer {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  margin-top: 10px;
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 5px;
}

.contact-list {
  padding: 0 15px 15px;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f5f5f5;
}

.contact-item:last-child {
  border-bottom: none;
}

.contact-icon {
  width: 40px;
  height: 40px;
  background-color: rgba(255, 97, 139, 0.1);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.contact-info {
  flex: 1;
}

.contact-name {
  font-size: 16px;
  color: #333;
  margin-bottom: 5px;
}

.contact-value {
  font-size: 14px;
  color: #999;
}

.feedback-section {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.feedback-btn {
  background-color: #fff;
  color: #0474FC;
  padding: 10px 20px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.feedback-btn span {
  margin-left: 5px;
}

.qrcode-popup {
  width: 280px;
  padding: 20px;
  text-align: center;
}

.popup-title {
  font-size: 18px;
  font-weight: 500;
  color: #333;
  margin-bottom: 15px;
}

.qrcode-image {
  width: 200px;
  height: 200px;
  margin: 0 auto 15px;
}

.qrcode-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.popup-desc {
  font-size: 14px;
  color: #999;
  margin-bottom: 15px;
}

.popup-btn {
  background-color: #0474FC;
  color: #fff;
  padding: 10px 0;
  border-radius: 20px;
  font-size: 16px;
}
</style>
