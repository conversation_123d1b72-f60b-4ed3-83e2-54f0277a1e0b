<template>
  <div class="tab-bar">
    <div class="tab-bar-inner">
      <!-- 首页 -->
      <a href="#home" class="tab-item" @click="onTabClick(0, '/', $event)">
        <img src="@/assets/icons/home-icon.svg" alt="首页" />
      </a>

      <!-- 商城 -->
      <a href="#mall" class="tab-item" @click="onTabClick(1, '/points-mall', $event)">
        <img src="@/assets/icons/cart-icon.svg" alt="商城" />
      </a>

      <!-- 善之力 -->
      <a href="#power" class="tab-item shanzhili-tab" @click="onTabClick(2, '/power', $event)">
        <div class="shanzhili-bg"></div>
        <img src="@/assets/icons/like-icon.svg" alt="善之力" />
      </a>

      <!-- 直播间 -->
      <a href="#live" class="tab-item" @click="onTabClick(3, '/live', $event)">
        <img src="@/assets/icons/live-icon.svg" alt="直播间" />
      </a>

      <!-- 我的 -->
      <a href="#mine" class="tab-item" @click="onTabClick(4, '/mine', $event)">
        <img src="@/assets/icons/user-icon.svg" alt="我的" />
      </a>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TabBar',
  methods: {
    onTabClick(index, path, event) {
      // 阻止默认的锚点跳转行为
      event.preventDefault();

      // 直接导航到对应路由
      this.$router.push(path);
    }
  }
}
</script>

<style scoped>
.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 80px;
  z-index: 100;
  overflow: visible;
}

.tab-bar-inner {
  position: relative;
  display: flex;
  align-items: center;
  height: 100%;
  background-image: url('@/assets/images/tab-bg.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  padding-bottom: env(safe-area-inset-bottom);
  /* 如果背景图片加载失败，使用白色背景 */
  background-color: #ffffff;
  box-shadow: 0 -1px 5px rgba(0, 0, 0, 0.08);
}

.tab-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  cursor: pointer;
  position: relative;
  text-decoration: none;
  outline: none;
  -webkit-tap-highlight-color: transparent;
}

.tab-item img {
  width: 32px;
  height: 32px;
  transition: transform 0.2s ease;
}

/* 善之力特殊样式 */
.shanzhili-tab {
  position: relative;
  z-index: 10;
}

.shanzhili-bg {
  position: absolute;
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, #4A90E2 0%, #3575FF 100%);
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) translateY(-10px);
  box-shadow:
    0 8px 20px rgba(53, 117, 255, 0.3),
    0 4px 12px rgba(53, 117, 255, 0.2),
    0 0 0 3px rgba(255, 255, 255, 0.8);
  z-index: -1;
}

.shanzhili-tab img {
  position: relative;
  z-index: 1;
  width: 28px !important;
  height: 28px !important;
  filter: brightness(0) invert(1);
  transform: translateY(-10px);
}

/* hover效果 */
.tab-item:hover img {
  transform: scale(1.1);
}

.shanzhili-tab:hover .shanzhili-bg {
  transform: translate(-50%, -50%) translateY(-10px) scale(1.05);
}

.shanzhili-tab:hover img {
  transform: translateY(-10px) scale(1.1);
}
</style>