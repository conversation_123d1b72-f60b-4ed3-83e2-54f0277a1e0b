# 快速参考手册

## 常用命令
```bash
# 启动开发服务器
npm run serve

# 构建生产版本
npm run build

# 安装依赖
npm install
```

## 常用代码片段

### 1. 页面组件模板
```vue
<template>
  <div class="page-name">
    <!-- 头部 -->
    <div class="header">
      <div class="back-icon" @click="goBack">
        <img src="@/assets/icons/back-icon.svg" @error="handleImageError($event, 'icon')" alt="返回">
      </div>
      <div class="title">页面标题</div>
    </div>
    
    <!-- 内容区域 -->
    <div class="content">
      <!-- 页面内容 -->
    </div>
  </div>
</template>

<script>
import ImageErrorHandler from '@/utils/ImageErrorHandler.js'

export default {
  name: 'PageName',
  data() {
    return {
      loading: false,
      data: []
    }
  },
  created() {
    this.loadData()
  },
  methods: {
    handleImageError(event, type) {
      ImageErrorHandler.handleImageError(event, type)
    },
    
    goBack() {
      this.$router.go(-1)
    },
    
    async loadData() {
      // API请求逻辑
    }
  }
}
</script>

<style scoped>
.page-name {
  min-height: 100vh;
  background-color: #f6f6f6;
}
</style>
```

### 2. API请求模板
```javascript
async apiRequest(endpoint, data = {}) {
  const token = localStorage.getItem('user_token')
  if (!token) {
    this.$toast('请先登录')
    this.$router.push('/login')
    return
  }
  
  try {
    this.loading = true
    const response = await this.$http.post(endpoint, data, {
      headers: { 'authorization': token }
    })
    
    if (response.data.code === 200) {
      return response.data.data
    } else {
      this.$toast(response.data.msg || '操作失败')
      return null
    }
  } catch (error) {
    console.error('API请求失败:', error)
    this.$toast('网络异常，请稍后重试')
    return null
  } finally {
    this.loading = false
  }
}
```

## 调试检查清单
- [ ] 控制台无错误信息
- [ ] 网络请求正常
- [ ] 图片资源加载正常
- [ ] 移动端样式适配正确
- [ ] 用户交互反馈及时
- [ ] 加载状态显示正常
- [ ] 错误处理机制完善
