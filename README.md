# 新农村移动端应用

基于Vue 2和Vant UI组件库开发的移动端应用，完全按照Figma设计稿实现。

## 项目结构

```
├── public/                 # 静态资源
│   ├── index.html          # HTML模板
│   └── favicon.ico         # 网站图标
├── src/                    # 源代码
│   ├── assets/             # 资源文件
│   │   ├── icons/          # 图标资源
│   │   └── images/         # 图片资源
│   ├── components/         # 公共组件
│   │   ├── StatusBar.vue   # 状态栏组件
│   │   └── TabBar.vue      # 底部导航栏组件
│   ├── router/             # 路由配置
│   │   └── index.js        # 路由定义
│   ├── style/              # 样式文件
│   │   └── mobile.css      # 移动端样式
│   ├── views/              # 页面组件
│   │   ├── Home.vue        # 首页
│   │   ├── Mall.vue        # 商城页面
│   │   ├── Power.vue       # 善之力页面
│   │   ├── Live.vue        # 直播间页面
│   │   ├── Mine.vue        # 个人中心页面
│   │   ├── MemberLevel.vue # 会员等级页面
│   │   ├── PointsMall.vue  # 积分商城页面
│   │   ├── Login.vue       # 登录页面
│   │   ├── Register.vue    # 注册页面
│   │   ├── ProjectDetail.vue # 项目详情页面
│   │   ├── Team.vue        # 我的团队页面
│   │   ├── Order.vue       # 我的订单页面
│   │   ├── Settings.vue    # 设置页面
│   │   ├── CustomerService.vue # 客服中心页面
│   │   ├── Wallet.vue      # 我的钱包页面
│   │   ├── Recharge.vue    # 充值页面
│   │   ├── RechargeDetail.vue # 充值明细页面
│   │   ├── RechargeRating.vue # 充值评级页面
│   │   ├── Withdraw.vue    # 提现页面
│   │   ├── Transfer.vue    # 转账页面
│   │   ├── Invite.vue      # 邀请返利页面
│   │   ├── TransactionRecord.vue # 交易记录页面
│   │   ├── Message.vue     # 消息管理页面
│   │   ├── Profile.vue     # 基本信息页面
│   │   ├── Qualification.vue # 平台资质页面
│   │   ├── BankCard.vue    # 银行卡管理页面
│   │   ├── AddBankCard.vue # 添加银行卡页面
│   │   ├── Verification.vue # 实名认证页面
│   │   ├── Income.vue      # 我的收益页面
│   │   ├── Favorite.vue    # 我的收藏页面
│   │   ├── ChangePassword.vue # 修改密码页面
│   │   ├── Address.vue     # 地址管理页面
│   │   └── AddAddress.vue  # 新增地址页面
│   ├── App.vue             # 根组件
│   └── main.js             # 入口文件
├── package.json            # 项目依赖
└── vue.config.js           # Vue配置文件
```

## 页面说明

### 1. 首页 (Home.vue)

首页展示发现内容，包括最新活动、热门资讯和为你推荐等模块。用户可以通过首页快速了解平台的最新动态和热门内容。

### 2. 商城 (Mall.vue)

商城页面展示商品列表，包括热销商品和新品上市等分类。用户可以浏览和购买各种农产品和相关商品。

### 3. 善之力 (Power.vue)

善之力页面展示积分任务和公益项目。用户可以通过完成任务获取积分，也可以参与公益项目进行捐赠。

### 4. 直播间 (Live.vue)

直播间页面展示直播内容，包括热门直播和直播预告等。用户可以观看农产品相关的直播和参与互动。

### 5. 个人中心 (Mine.vue)

个人中心页面展示用户信息和功能入口，包括会员等级、积分商城、我的团队和我的订单等。

### 6. 会员等级 (MemberLevel.vue)

会员等级页面展示用户当前等级和所有等级的详细信息，包括升级条件和权益说明。

### 7. 积分商城 (PointsMall.vue)

积分商城页面展示可用积分兑换的商品列表，用户可以使用积分兑换各种商品。

### 8. 登录 (Login.vue)

登录页面提供手机号验证码登录功能，用户可以通过手机号和验证码进行登录。

### 9. 注册 (Register.vue)

注册页面提供新用户注册功能，用户需要填写手机号、验证码、姓名和密码等信息进行注册。

### 10. 项目详情 (ProjectDetail.vue)

项目详情页面展示公益项目的详细信息，包括项目背景、目标、进展和支持记录等。用户可以在此页面进行捐赠支持。

### 11. 我的团队 (Team.vue)

我的团队页面展示用户的团队信息，包括团队成员、团队收益等。用户可以查看团队数据和邀请新成员。

### 12. 我的订单 (Order.vue)

我的订单页面展示用户的订单列表，包括待付款、待发货、待收货和已完成等状态的订单。用户可以查看订单详情和进行相关操作。

### 13. 设置 (Settings.vue)

设置页面提供各种系统设置选项，包括个人资料、账号与安全、收货地址、消息通知、隐私设置等。用户可以在此页面管理应用的各项设置。

### 14. 客服中心 (CustomerService.vue)

客服中心页面提供在线客服、常见问题解答和联系方式等功能。用户可以通过此页面获取帮助和解决问题。

### 15. 我的钱包 (Wallet.vue)

我的钱包页面展示用户的账户余额和收益统计，提供充值、提现功能，并显示交易记录和绑定的银行卡信息。

### 16. 充值 (Recharge.vue)

充值页面提供用户充值功能，用户可以选择充值金额和支付方式进行账户充值。

### 17. 充值明细 (RechargeDetail.vue)

充值明细页面展示用户的充值记录，包括充值时间、金额、状态等信息。用户可以筛选和查看历史充值记录。

### 18. 充值评级 (RechargeRating.vue)

充值评级页面展示用户的充值等级和权益，用户可以了解不同充值等级的要求和对应的特权。

### 19. 提现 (Withdraw.vue)

提现页面提供用户提现功能，用户可以将账户余额提现到绑定的银行卡。

### 20. 转账 (Transfer.vue)

转账页面提供用户转账功能，用户可以向其他用户转账。

### 21. 邀请返利 (Invite.vue)

邀请返利页面提供用户邀请好友的功能，展示邀请码、二维码和邀请奖励规则，用户可以通过邀请好友获得返利。

### 22. 交易记录 (TransactionRecord.vue)

交易记录页面展示用户的所有交易记录，包括充值、提现、转账、消费等，用户可以筛选和查看不同类型的交易记录。

### 23. 消息管理 (Message.vue)

消息管理页面展示用户的系统消息、交易消息和活动消息等，用户可以查看和管理各类消息。

### 24. 基本信息 (Profile.vue)

基本信息页面展示和管理用户的个人资料，包括头像、昵称、性别、生日、手机号和邮箱等信息。

### 25. 平台资质 (Qualification.vue)

平台资质页面展示平台的各类资质证书和公司信息，增强用户对平台的信任。

### 26. 银行卡管理 (BankCard.vue)

银行卡管理页面展示用户绑定的银行卡列表，用户可以添加、编辑和删除银行卡。

### 27. 添加银行卡 (AddBankCard.vue)

添加银行卡页面提供用户添加新银行卡的功能，用户需要填写银行卡信息并进行验证。

### 28. 实名认证 (Verification.vue)

实名认证页面提供用户进行身份验证的功能，用户需要上传身份证照片并填写相关信息进行认证。

### 29. 我的收益 (Income.vue)

我的收益页面展示用户的收益统计和明细，包括邀请返利、消费返现等各类收益来源。

### 30. 我的收藏 (Favorite.vue)

我的收藏页面展示用户收藏的商品、文章和店铺等，用户可以管理自己的收藏内容。

### 31. 修改密码 (ChangePassword.vue)

修改密码页面提供用户修改登录密码的功能，用户需要输入当前密码和新密码进行修改。

### 32. 地址管理 (Address.vue)

地址管理页面展示用户的收货地址列表，用户可以添加、编辑和删除收货地址。

### 33. 新增地址 (AddAddress.vue)

新增地址页面提供用户添加新收货地址的功能，用户需要填写收货人、联系方式和详细地址等信息。

## 技术栈

- Vue 2：前端框架
- Vue Router：路由管理
- Vant UI：移动端UI组件库
- Axios：HTTP请求
- amfe-flexible：移动端适配

## 启动方式

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run serve

# 构建生产版本
npm run build
```

## 设计稿

本项目完全按照Figma设计稿实现，确保了UI的一致性和用户体验的流畅性。