<template>
  <div class="recharge">
    <!-- 头部 -->
    <div class="header">
      <div class="back-icon" @click="goBack">
        <img src="@/assets/icons/back-icon.svg" alt="返回">
      </div>
      <div class="title">充值</div>
      <div class="right-icon" @click="goToRechargeDetail">
        <span>明细</span>
      </div>
    </div>

    <!-- 账户余额卡片 -->
    <div class="balance-card">
      <div class="balance-card-inner">
        <div class="wallet-icon">
          <img src="@/assets/images/recharge.png" alt="钱包">
        </div>
        <div class="balance-info">
          <div class="balance-label">账户余额</div>
          <div class="balance-amount">{{ accountBalance }}</div>
        </div>
      </div>
    </div>

    <!-- 金额选择 -->
    <div class="amount-section">
      <div class="section-title">选择金额</div>
      <div class="amount-grid">
        <div class="amount-item" :class="{ active: amount === quickAmount.toString() }"
          v-for="quickAmount in quickAmounts" :key="quickAmount" @click="selectAmount(quickAmount)">
          ¥{{ quickAmount }}
          <div class="check-icon" v-if="amount === quickAmount.toString()">
            <svg width="24" height="21" viewBox="0 0 24 21" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 16.1698L22.5 2.5" stroke="#0474FC" stroke-width="1" stroke-linecap="round"
                stroke-linejoin="round" />
              <path d="M9 16.1698L1.5 9.33962" stroke="#0474FC" stroke-width="1" stroke-linecap="round"
                stroke-linejoin="round" />
            </svg>
          </div>
        </div>
      </div>
    </div>

    <!-- 充值金额输入 -->
    <div class="input-amount-section">
      <div class="section-title">充值金额</div>
      <div class="amount-input-container">
        <input class="amount-input" v-model="amount" type="number" placeholder="请输入充值金额，最少100元" @input="onAmountChange">
      </div>
    </div>

    <!-- 充值说明 -->
    <div class="recharge-notes">
      <div class="notes-header">
        <div class="notes-icon">
          <svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M11 21C16.5228 21 21 16.5228 21 11C21 5.47715 16.5228 1 11 1C5.47715 1 1 5.47715 1 11C1 16.5228 5.47715 21 11 21Z"
              stroke="#0474FC" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
            <path d="M11 15V11" stroke="#0474FC" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
            <path d="M11 7H11.01" stroke="#0474FC" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
          </svg>
        </div>
        <div class="notes-title">充值说明</div>
      </div>
      <div class="notes-content">
        <div v-for="(note, index) in rechargeNotes" :key="index">
          {{ note }}<br v-if="index < rechargeNotes.length - 1">
        </div>
        <!-- 如果API没有返回充值说明，显示默认内容 -->
        <template v-if="rechargeNotes.length === 0">
          1.单笔充值金额不得低于100元<br>
          2.充值时间：周一至周日24小时<br>
          3.使用网银或手机银行付款金额必须与提交的充值金额一致<br>
          （如：充值界面输入100元，则付款金额也必须是100元）<br>
          4.如有其他疑问，请咨询在线客服。
        </template>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="next-button" @click="confirmRecharge">
      下一步
    </div>

    <!-- 支付渠道选择弹窗 -->
    <div class="payment-channels-popup" v-if="showPaymentChannels">
      <div class="popup-overlay" @click="showPaymentChannels = false"></div>
      <div class="popup-content">
        <div class="popup-header">
          <div class="popup-title">选择支付渠道</div>
          <div class="popup-close" @click="showPaymentChannels = false">×</div>
        </div>
        <div class="popup-body">
          <!-- 加载状态 -->
          <div class="loading-state" v-if="loadingChannels">
            <van-loading type="spinner" color="#0474FC ">加载支付渠道中..</van-loading>
          </div>

          <!-- 空状态 -->
          <div class="empty-state" v-else-if="paymentChannels.length === 0">
            <div class="empty-icon">
              <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M24 44C35.0457 44 44 35.0457 44 24C44 12.9543 35.0457 4 24 4C12.9543 4 4 12.9543 4 24C4 35.0457 12.9543 44 24 44Z"
                  stroke="#D1D5DB" stroke-width="2" />
                <path d="M24 30V24" stroke="#D1D5DB" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                <path d="M24 18H24.02" stroke="#D1D5DB" stroke-width="2" stroke-linecap="round"
                  stroke-linejoin="round" />
              </svg>
            </div>
            <div class="empty-text">当前金额暂无可用支付渠道</div>
          </div>

          <!-- 支付渠道列表 -->
          <div class="channel-item" v-for="channel in paymentChannels" :key="channel.id"
            @click="selectPaymentChannel(channel)">
            <div class="channel-icon">
              <img :src="getChannelIcon(channel.type)" alt="支付渠道图标">
            </div>
            <div class="channel-info">
              <div class="channel-name">{{ channel.title }}</div>
              <div class="channel-desc">{{ getChannelDesc(channel.type) }}</div>
            </div>
            <div class="channel-select">
              <div class="select-circle" :class="{ 'selected': selectedChannel && selectedChannel.id === channel.id }">
              </div>
            </div>
          </div>
        </div>
        <div class="popup-footer" v-if="paymentChannels.length > 0">
          <div class="confirm-button" @click="proceedToPayment">确认支付</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { Loading } from 'vant'

export default {
  name: 'Recharge',
  components: {
    VanLoading: Loading
  },
  data() {
    return {
      amount: '500',
      accountBalance: '0.00',
      quickAmounts: [500, 1000, 2000, 3000, 5000, 10000, 20000, 50000], // 从API获取
      rechargeNotes: [], // 从API获取充值说明
      showPaymentChannels: false,
      selectedChannel: null,
      paymentChannels: [], // 从API获取的支付渠道列表
      loadingChannels: false, // 支付渠道加载状态
      updateTimer: null, // 定时器
      kycStatus: true, // 实名认证状态
    }
  },
  created() {
    // 页面加载时获取充值信息
    this.loadDepositInfo()
    // 设置定时器，每分钟更新一次
    this.startUpdateTimer()
  },
  beforeDestroy() {
    // 清除定时器
    if (this.updateTimer) {
      clearInterval(this.updateTimer)
    }
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    goToRechargeDetail() {
      this.$router.push('/recharge-detail')
    },

    // 获取充值基础信息
    async loadDepositInfo() {
      const userToken = localStorage.getItem('user_token')
      if (!userToken) {
        this.$toast('请先登录')
        this.$nextTick(() => { this.$router.replace('/login') })
        return
      }

      try {
        const response = await this.$http.get('/finance/getDeposit', {
          headers: {
            'authorization': userToken
          }
        })

        if (response.data.code === 200) {
          const data = response.data.data

          // 更新账户余额
          this.accountBalance = data.balance || '0.00'

          // 更新实名认证状态
          this.kycStatus = data.kyc || false

          // 更新金额选择列表
          if (data.amount_list && Array.isArray(data.amount_list)) {
            this.quickAmounts = data.amount_list
            // 设置默认金额为列表第一项
            if (this.quickAmounts.length > 0) {
              this.amount = this.quickAmounts[0].toString()
            }
          }

          // 更新充值说明
          if (data.des && Array.isArray(data.des)) {
            this.rechargeNotes = data.des
          }
        } else {
          this.$toast(response.data.msg || '获取充值信息失败')
        }
      } catch (error) {
        console.error('获取充值信息失败', error)
        this.$toast('网络异常，请稍后重试')
      }
    },

    // 启动定时器
    startUpdateTimer() {
      // 每分钟更新一次充值信息
      this.updateTimer = setInterval(() => {
        this.loadDepositInfo()
      }, 60000) // 60秒 = 1分钟
    },

    selectAmount(amount) {
      this.amount = amount.toString()
    },
    onAmountChange() {
      // 输入金额变化时重置选中的支付渠道
      this.selectedChannel = null
    },
    async confirmRecharge() {
      if (!this.amount || parseFloat(this.amount) <= 0) {
        this.$toast('请输入有效的充值金额')
        return
      }

      if (parseFloat(this.amount) < 500) {
        this.$toast('充值金额最少500元')
        return
      }

      // 显示支付渠道选择弹窗并加载支付渠道
      this.showPaymentChannels = true
      await this.loadPaymentChannels()
    },

    // 加载支付渠道
    async loadPaymentChannels() {
      const userToken = localStorage.getItem('user_token')
      if (!userToken) {
        this.$toast('请先登录')
        this.$nextTick(() => { this.$router.replace('/login') })
        return
      }

      this.loadingChannels = true

      try {
        const response = await this.$http.get(`/finance/getDepositPayment?amount=${this.amount}`, {
          headers: {
            'authorization': userToken
          }
        })

        if (response.data.code === 200) {
          this.paymentChannels = response.data.data.payment || []

          if (this.paymentChannels.length === 0) {
            this.$toast('当前金额暂无可用支付渠道')
          }
        } else {
          this.$toast(response.data.msg || '获取支付渠道失败')
          this.paymentChannels = []
        }
      } catch (error) {
        console.error('获取支付渠道失败:', error)
        this.$toast('网络异常，请稍后重试')
        this.paymentChannels = []
      } finally {
        this.loadingChannels = false
      }
    },

    // 根据支付渠道类型获取图标
    getChannelIcon(type) {
      const iconMap = {
        1: require('@/assets/images/avatar.jpg'),    // 微信支付 - 使用现有图片
        2: require('@/assets/images/avatar.jpg'),    // 支付宝 - 使用现有图片  
        3: require('@/assets/images/avatar.jpg'),    // 银联支付 - 使用现有图片
        4: require('@/assets/images/avatar.jpg')     // 网银支付 - 使用现有图片
      }

      return iconMap[type] || require('@/assets/images/avatar.jpg')
    },

    // 根据支付渠道类型获取描述
    getChannelDesc(type) {
      const descMap = {
        1: '快速到账，方便安全',
        2: '安全便捷，即时到账',
        3: '支持各大银行卡支付',
        4: '各大银行网银直接支付'
      }

      return descMap[type] || '安全便捷的支付方式'
    },

    selectPaymentChannel(channel) {
      this.selectedChannel = channel
    },

    proceedToPayment() {
      if (!this.selectedChannel) {
        this.$toast('请选择支付渠道')
        return
      }

      // 检查实名认证状态
      if (!this.kycStatus) {
        this.$toast('请先完成实名认证')
        return
      }

      // 显示确认对话框
      this.$dialog.confirm({
        title: '确认支付',
        message: `确认使用 ${this.selectedChannel.title} 充值 ¥${this.amount} 吗？`,
        confirmButtonText: '确认支付',
        cancelButtonText: '取消'
      }).then(() => {
        // 用户确认后，调用支付接口
        this.processPayment()
      }).catch(() => {
        // 用户取消，不做任何操作
      })
    },

    // 处理支付
    async processPayment() {
      const userToken = localStorage.getItem('user_token')
      if (!userToken) {
        this.$toast('请先登录')
        this.$nextTick(() => { this.$router.replace('/login') })
        return
      }

      // 检查支付渠道类型
      if (this.selectedChannel.type === 1) {
        // type=1 直接跳转到内部页面
        this.$toast.success('正在跳转到支付页面')

        // 关闭支付渠道弹窗
        this.showPaymentChannels = false

        // 跳转到充值提交页面
        this.$router.push({
          path: '/recharge-submit',
          query: {
            id: this.selectedChannel.id,
            amount: this.amount,
            channelTitle: this.selectedChannel.title
          }
        })
        return
      }

      // 其他type继续API处理流程
      const loadingToast = this.$toast.loading({
        message: '正在处理支付...',
        forbidClick: true,
        duration: 0
      })

      try {
        const response = await this.$http.get(`/finance/getPayment?id=${this.selectedChannel.id}&amount=${this.amount}&type=${this.selectedChannel.type}`, {
          headers: {
            'authorization': userToken
          }
        })

        loadingToast.clear()

        if (response.data.code === 200) {
          // 支付成功，处理返回的数据
          const paymentData = response.data.data

          if (typeof paymentData === 'string' && paymentData.startsWith('http')) {
            // 返回的是支付链接
            this.handlePaymentUrl(paymentData)
          } else if (typeof paymentData === 'object' && paymentData.url) {
            // 返回的是包含url的对象
            this.handlePaymentUrl(paymentData.url)
          } else {
            // 其他类型的响应
            this.$toast.success('支付处理成功')
            this.showPaymentChannels = false
            this.redirectToDetail()
          }
        } else {
          // API返回错误
          this.$toast.fail(response.data.msg || '支付处理失败')
        }
      } catch (error) {
        loadingToast.clear()
        console.error('支付处理失败:', error)

        if (error.response) {
          // HTTP错误响应
          const status = error.response.status
          const message = error.response.data?.msg || '支付处理失败'

          if (status === 401) {
            this.$toast.fail('登录已过期，请重新登录')
            this.$nextTick(() => { this.$router.replace('/login') })
          } else if (status >= 500) {
            this.$toast.fail('服务器异常，请稍后重试')
          } else {
            this.$toast.fail(message)
          }
        } else if (error.request) {
          // 网络错误
          this.$toast.fail('网络连接失败，请检查网络后重试')
        } else {
          // 其他错误
          this.$toast.fail('支付处理异常，请重试')
        }
      }
    },

    // 处理支付链接
    handlePaymentUrl(paymentUrl) {
      try {
        // 验证URL有效性
        new URL(paymentUrl)

        // 在新窗口打开支付链接
        const paymentWindow = window.open(paymentUrl, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes')

        if (paymentWindow) {
          // 成功打开新窗口
          this.$toast.success('已跳转到支付页面，请在新窗口完成支付')

          // 关闭支付渠道弹窗
          this.showPaymentChannels = false

          // 监听窗口关闭，延时跳转到充值明细
          const checkClosed = setInterval(() => {
            if (paymentWindow.closed) {
              clearInterval(checkClosed)
              this.$toast('支付窗口已关闭')

              // 询问用户是否查看充值记录
              this.$dialog.confirm({
                title: '支付完成',
                message: '是否查看充值记录？',
                confirmButtonText: '查看记录',
                cancelButtonText: '继续充值'
              }).then(() => {
                this.redirectToDetail()
              }).catch(() => {
                // 用户选择继续充值，刷新页面数据
                this.loadDepositInfo()
              })
            }
          }, 1000)

          // 10秒后停止检查
          setTimeout(() => {
            clearInterval(checkClosed)
          }, 10000)

        } else {
          // 新窗口被阻止
          this.$toast.fail('无法打开支付页面，请检查浏览器设置')
        }
      } catch (error) {
        console.error('支付链接无效:', error)
        this.$toast.fail('支付链接无效')
      }
    },

    // 跳转到充值明细页面
    redirectToDetail() {
      this.$router.push({
        path: '/recharge-detail',
        query: {
          amount: this.amount,
          channel: this.selectedChannel.title,
          channelId: this.selectedChannel.id,
          from: 'recharge'
        }
      })
    }
  }
}
</script>

<style scoped>
.recharge {
  min-height: 100vh;
  background-color: #F8F9FA;
  padding-bottom: 80px;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 16px;
  background-color: #fff;
  position: relative;
}

.back-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon img {
  width: 9.6px;
  height: 16.5px;
}

.title {
  font-size: 17px;
  font-weight: 400;
  color: #111827;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.right-icon {
  font-size: 14px;
  color: #666;
}

/* 账户余额卡片 */
.balance-card {
  margin: 15px;
  height: 123px;
  position: relative;
  border-radius: 10px;
}

.balance-card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 10px;
  background: linear-gradient(180deg, #0474FC 0%, #2585EB 1%, #1F68D6 43%, #1869F5 79%, #0940A0 100%);
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-left: 20px;
  overflow: hidden;
}

.wallet-icon {
  width: 70px;
  height: 70px;
  margin-right: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.wallet-icon img {
  width: 100%;
  height: auto;
  object-fit: contain;
}

.balance-info {
  color: #FFFFFF;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.balance-label {
  font-size: 14px;
  font-weight: 400;
  margin-bottom: 12px;
}

.balance-amount {
  font-size: 30px;
  font-weight: 700;
  font-family: 'DIN', Arial, sans-serif;
}

/* 金额选择部分 */
.amount-section {
  background-color: #FFFFFF;
  margin: 15px;
  padding: 15px;
  border-radius: 8px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  margin-bottom: 12px;
}

.amount-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(3, auto);
  gap: 10px;
}

.amount-item {
  height: 36px;
  background-color: #FFFFFF;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 500;
  color: #666666;
  position: relative;
  border: 1px solid #EAEAEA;
}

.amount-item.active {
  border: 2px solid #0474FC;
  background-color: rgba(25, 48, 141, 0.03);
}

.check-icon {
  position: absolute;
  right: 0;
  top: 0;
  width: 24px;
  height: 21px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 充值金额输入 */
.input-amount-section {
  background-color: #FFFFFF;
  margin: 15px;
  padding: 15px;
  border-radius: 8px;
}

.amount-input-container {
  background-color: #FFFFFF;
  border-radius: 8px;
  border: 1px solid #EAEAEA;
  height: 50px;
  display: flex;
  align-items: center;
  padding: 0 15px;
}

.amount-input {
  width: 100%;
  height: 100%;
  border: none;
  outline: none;
  font-size: 14px;
  color: #333;
}

.amount-input::placeholder {
  color: #B2B2B9;
}

/* 充值说明 */
.recharge-notes {
  background-color: #FFFFFF;
  margin: 15px;
  padding: 20px;
  border-radius: 9px;
}

.notes-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.notes-icon {
  margin-right: 10px;
}

.notes-title {
  font-size: 16px;
  color: #333333;
}

.notes-content {
  font-size: 14px;
  color: #9E9E9E;
  line-height: 1.5;
}

/* 底部按钮 */
.next-button {
  position: fixed;
  bottom: 20px;
  left: 16px;
  right: 16px;
  height: 40px;
  background: #0474FC;
  color: #fff;
  font-size: 15px;
  font-weight: 500;
  border-radius: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 支付渠道弹窗样式 */
.payment-channels-popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.popup-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.popup-content {
  position: relative;
  width: 100%;
  background-color: #FFFFFF;
  border-radius: 16px 16px 0 0;
  max-height: 70vh;
  padding: 20px;
  overflow: hidden;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.popup-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.popup-close {
  font-size: 24px;
  color: #999;
  cursor: pointer;
  padding: 5px;
}

.popup-body {
  max-height: 50vh;
  overflow-y: auto;
}

.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  flex-direction: column;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.empty-icon {
  margin-bottom: 16px;
}

.empty-text {
  font-size: 14px;
  color: #999;
}

.channel-item {
  display: flex;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
}

.channel-item:last-child {
  border-bottom: none;
}

.channel-icon {
  width: 40px;
  height: 40px;
  margin-right: 15px;
  border-radius: 8px;
  overflow: hidden;
}

.channel-icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.channel-info {
  flex: 1;
}

.channel-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.channel-desc {
  font-size: 12px;
  color: #999;
}

.channel-select {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.select-circle {
  width: 16px;
  height: 16px;
  border: 2px solid #ddd;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.select-circle.selected {
  border-color: #0474FC;
  background-color: #0474FC;
  position: relative;
}

.select-circle.selected::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 8px;
  height: 8px;
  background-color: white;
  border-radius: 50%;
}

.popup-footer {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
}

.confirm-button {
  width: 100%;
  height: 44px;
  background: linear-gradient(180deg, #FF7D87 0%, #FF5C89 100%);
  color: #fff;
  font-size: 16px;
  font-weight: 500;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
</style>
