<template>
  <div class="recharge-submit">
    <!-- 顶部导航 -->
    <div class="header">
      <div class="header-left" @click="$router.go(-1)">
        <i class="icon-arrow-left"></i>
      </div>
      <div class="header-title">充值提交</div>
      <div class="header-right"></div>
    </div>

    <!-- 充值信息卡片 -->
    <div class="recharge-info">      <div class="amount-section">
        <div class="amount-label">充值金额</div>
        <div class="amount-value">¥{{ amount }}</div>
      </div>
    </div>    <!-- 银行转账信息 -->
    <div class="bank-info" v-if="bankInfoLoaded">
      <div class="section-title">
        <i class="icon-bank"></i>
        收款信息
      </div>      <div class="bank-details">
        <div class="bank-item">
          <div class="bank-label">收款渠道</div>
          <div class="bank-value-container">
            <div class="bank-value">{{ bankInfo.title }}</div>
            <div class="item-copy-btn" @click="copyText(bankInfo.title)">复制</div>
          </div>
        </div>
        <div class="bank-item">
          <div class="bank-label">收款人</div>
          <div class="bank-value-container">
            <div class="bank-value">{{ bankInfo.pay_name }}</div>
            <div class="item-copy-btn" @click="copyText(bankInfo.pay_name)">复制</div>
          </div>
        </div>
        <div class="bank-item">
          <div class="bank-label">收款银行</div>
          <div class="bank-value-container">
            <div class="bank-value">{{ bankInfo.pay_bank }}</div>
            <div class="item-copy-btn" @click="copyText(bankInfo.pay_bank)">复制</div>
          </div>
        </div>
        <div class="bank-item">
          <div class="bank-label">银行卡号</div>
          <div class="bank-value-container">
            <div class="bank-value">{{ bankInfo.pay_card }}</div>
            <div class="item-copy-btn" @click="copyText(bankInfo.pay_card)">复制</div>
          </div>
        </div>
        <div class="bank-item" v-if="bankInfo.pay_address">
          <div class="bank-label">支行地址</div>
          <div class="bank-value-container">
            <div class="bank-value">{{ bankInfo.pay_address }}</div>
            <div class="item-copy-btn" @click="copyText(bankInfo.pay_address)">复制</div>
          </div>
        </div>
      </div>
    </div>
      <!-- 上传转账凭证 -->
    <div class="upload-section">
      <div class="section-title">
        <i class="icon-upload"></i>
        上传转账凭证
      </div>
      
      <div class="upload-area" @click="triggerFileInput">
        <input 
          type="file" 
          ref="fileInput" 
          style="display:none" 
          accept="image/*" 
          @change="handleFileChange"
        >
        <div class="upload-content" v-if="!imageUrl">
          <div class="upload-icon">
            <i class="icon-camera"></i>
          </div>
          <div class="upload-text">点击上传转账凭证</div>
          <div class="upload-hint">支持JPG、PNG格式，大小不超过5MB</div>
        </div>        <div class="preview-content" v-else>
          <img :src="imageUrl.startsWith('http') ? imageUrl : imageUrl" class="preview-image" @click="previewImage">
          <div class="change-btn" @click.stop="triggerFileInput">更换图片</div>
        </div>
      </div>
      
      <div class="upload-status" v-if="uploadStatus">{{ uploadStatus }}</div>
      
      <!-- 转账人信息 -->
      <div class="transfer-name-container">
        <div class="transfer-name-label">转账人</div>
        <input
          type="text"
          placeholder="请输入转账人姓名"
          class="transfer-name-input"
          v-model="transferName"
        />
      </div>
    </div>
    
    <!-- 支付说明 -->
    <div class="payment-notice">
      <div class="notice-title">
        <i class="icon-info"></i>
        支付说明
      </div>
      <div class="notice-content">        <p>1. 请按照收款信息上的账号进行转账</p>
        <p>2. 转账时请确保金额与充值金额一致</p>
        <p>3. 转账后请上传转账凭证</p>
        <p>4. 成功提交后客服将在1-24小时内处理</p>
        <p>5. 如有疑问请联系在线客服</p>
      </div>
    </div>

    <!-- 确认支付按钮 -->
    <div class="submit-section">
      <button 
        class="submit-btn" 
        :class="{ 'loading': submitting }"
        :disabled="submitting"
        @click="submitPayment"
      >
        <span v-if="!submitting">确认支付</span>
        <span v-else>
          <i class="loading-icon"></i>
          提交中..
        </span>
      </button>
    </div>

    <!-- 支付结果弹窗 -->
    <div class="popup-overlay" v-if="showResult" @click="closeResult">
      <div class="result-popup" @click.stop>
        <div class="result-icon" :class="resultType">
          <i :class="resultType === 'success' ? 'icon-check' : 'icon-close'"></i>
        </div>
        <div class="result-title">{{ resultTitle }}</div>
        <div class="result-message">{{ resultMessage }}</div>
        <div class="result-actions">
          <button class="result-btn primary" @click="handleResultAction">
            {{ resultType === 'success' ? '查看余额' : '重新提交' }}
          </button>
          <button class="result-btn" @click="closeResult">关闭</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RechargeSubmit',  data() {
    return {
      amount: '',
      channelId: '',
      channelTitle: '',
      orderTime: '',
      bankInfo: {
        title: '',
        pay_name: '',
        pay_bank: '',
        pay_card: '',
        pay_address: ''
      },
      imageUrl: '',
      uploadStatus: '',
      transferName: '',
      bankInfoLoaded: false,
      submitting: false,
      showResult: false,
      resultType: 'success', // success | error
      resultTitle: '',
      resultMessage: ''
    }
  },  created() {
    this.initPageData()
  },
  
  beforeDestroy() {
    // 组件销毁时，清理资源
    if (this.imageUrl && this.imageUrl.startsWith('blob:')) {
      URL.revokeObjectURL(this.imageUrl)
    }
  },
  methods: {    // 初始化页面数据
    initPageData() {
      // 获取路由参数
      this.amount = this.$route.query.amount || '0'
      this.channelId = this.$route.query.id || ''
      this.channelTitle = this.$route.query.channelTitle || ''
      
      // 设置订单时间
      this.orderTime = this.formatDateTime(new Date())
      
      // 验证必要参数
      if (!this.amount || !this.channelId) {
        this.$toast('参数错误，请重新选择充值')
        this.$router.go(-1)
        return
      }
      
      // 获取银行信息
      this.loadBankInfo()
    },
      // 加载银行信息
    async loadBankInfo() {
      const userToken = localStorage.getItem('user_token')
      if (!userToken) {
        this.$toast('请先登录')
        this.$nextTick(() => { this.$router.replace('/login') })
        return
      }
      
      try {
        // 获取充值通道信息
        const response = await this.$http.get(`/finance/getDepositPaymentInfo?amount=${this.amount}&payment_id=${this.channelId}`, {
          headers: {
            'authorization': userToken
          }
        })
        
        if (response.data.code === 200 && response.data.data && response.data.data.info) {
          this.bankInfo = response.data.data.info
          this.bankInfoLoaded = true
        } else {
          this.$toast(response.data.msg || '获取收款信息失败，请刷新页面')
        }
      } catch (error) {
        console.error('获取银行信息失败:', error)
        this.$toast('网络异常，请稍后重试')
      }
    },
    
    // 复制文本到剪贴板
    copyText(text) {
      if (!text) return
      
      const input = document.createElement('input')
      input.setAttribute('readonly', 'readonly')
      input.setAttribute('value', text)
      document.body.appendChild(input)
      input.select()
      input.setSelectionRange(0, 9999)
      
      if (document.execCommand('copy')) {
        this.$toast('复制成功')
      } else {
        this.$toast('复制失败，请手动复制')
      }
      
      document.body.removeChild(input)
    },
    
    // 格式化日期时间
    formatDateTime(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')
      
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    },    // 提交支付
    async submitPayment() {
      const userToken = localStorage.getItem('user_token')
      if (!userToken) {
        this.$toast('请先登录')
        this.$nextTick(() => { this.$router.replace('/login') })
        return
      }
      
      if (!this.imageUrl) {
        this.$toast('请上传转账凭证')
        return
      }
      
      // 验证转账人姓名
      if (!this.transferName || this.transferName.trim() === '') {
        this.$toast('请输入转账人姓名')
        return
      }
      
      this.submitting = true
      
      try {
        // 准备提交数据
        const paymentData = {
          amount: this.amount,
          payment_id: this.channelId,
          name: this.transferName,
          photo: this.imageUrl
        }
        
        // 调用支付接口 - POST方法
        const response = await this.$http.post('/finance/doDeposit', paymentData, {
          headers: {
            'authorization': userToken
          }
        })
        
        if (response.data.code === 200) {
          // 支付成功
          this.showResultPopup('success', '充值提交成功', '您的充值订单已提交成功，客服将在1-24小时内处理')
        } else {
          // 支付失败
          this.showResultPopup('error', '充值提交失败', response.data.msg || '提交失败，请重试')
        }
      } catch (error) {
        console.error('支付提交失败:', error)
        this.showResultPopup('error', '网络异常', '网络连接失败，请检查网络后重试')
      } finally {
        this.submitting = false
      }
    },
    
    // 显示结果弹窗
    showResultPopup(type, title, message) {
      this.resultType = type
      this.resultTitle = title
      this.resultMessage = message
      this.showResult = true
    },
    
    // 关闭结果弹窗
    closeResult() {
      this.showResult = false
    },
    
    // 处理结果操作
    handleResultAction() {
      if (this.resultType === 'success') {
        // 成功后跳转到钱包页面
        this.$router.push('/wallet')
      } else {
        // 失败后重新提交
        this.closeResult()
      }
    },
    
    // 触发文件选择
    triggerFileInput() {
      this.$refs.fileInput.click()
    },
      // 处理文件选择
    async handleFileChange(e) {
      const file = e.target.files[0]
      if (!file) return
      
      // 检查文件类型
      if (!['image/jpeg', 'image/png', 'image/jpg'].includes(file.type)) {
        this.$toast('只支持JPG、PNG格式的图片')
        return
      }
      
      // 检查文件大小（5MB）
      if (file.size > 5 * 1024 * 1024) {
        this.$toast('图片大小不能超过5MB')
        return
      }
      
      // 清除旧预览和上传状态
      if (this.imageUrl) {
        // 如果是本地预览URL，需要释放
        if (this.imageUrl.startsWith('blob:')) {
          URL.revokeObjectURL(this.imageUrl)
        }
        this.imageUrl = ''
      }
      
      // 重置上传状态
      this.uploadStatus = ''
      
      // 显示本地预览
      this.imageUrl = URL.createObjectURL(file)
      
      // 重置文件输入，确保下次选择相同文件时也能触发change事件
      e.target.value = ''
      
      // 上传文件
      await this.uploadFile(file)
    },    // 上传文件到服务器
    async uploadFile(file) {
      const userToken = localStorage.getItem('user_token')
      if (!userToken) {
        this.$toast('请先登录')
        this.$nextTick(() => { this.$router.replace('/login') })
        return
      }
      
      // 保存本地预览URL，用于在上传失败时恢复
      const localPreviewUrl = this.imageUrl
      
      this.uploadStatus = '正在上传...'
      
      try {
        // 创建FormData
        const formData = new FormData()
        formData.append('file', file)
        
        // 调用上传接口
        const response = await this.$http.post('/tools/uploadDeposit', formData, {
          headers: {
            'authorization': userToken,
            'Content-Type': 'multipart/form-data'
          }
        })
        
        if (response.data.code === 200) {
          // 上传成功，保存图片路径（服务器返回的路径）
          // 释放本地预览URL
          URL.revokeObjectURL(localPreviewUrl)
          
          // 保存服务器返回的图片路径
          this.imageUrl = response.data.data
          this.uploadStatus = '上传成功'
          this.$toast.success('凭证上传成功')
        } else {
          this.uploadStatus = '上传失败'
          this.$toast.fail(response.data.msg || '上传失败')
          // 清除预览
          if (localPreviewUrl && localPreviewUrl.startsWith('blob:')) {
            URL.revokeObjectURL(localPreviewUrl)
          }
          this.imageUrl = ''
        }
      } catch (error) {
        console.error('上传失败:', error)
        this.uploadStatus = '上传失败'
        this.$toast.fail('网络异常，请重试')
        // 清除预览
        if (localPreviewUrl && localPreviewUrl.startsWith('blob:')) {
          URL.revokeObjectURL(localPreviewUrl)
        }
        this.imageUrl = ''
      }
    },
    
    // 预览图片
    previewImage() {
      // 如果支持原生图片预览，可以添加在这里
      // 例如使用浏览器默认预览或第三方库
      // 这里简单实现一个全屏显示
      if (!this.imageUrl) return
      
      // 创建预览层
      const previewContainer = document.createElement('div')
      previewContainer.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.9);
        z-index: 10000;
        display: flex;
        align-items: center;
        justify-content: center;
      `
      
      // 创建图片元素
      const imgElement = document.createElement('img')
      imgElement.src = this.imageUrl
      imgElement.style.cssText = `
        max-width: 90%;
        max-height: 90%;
        object-fit: contain;
      `
      
      // 添加到容器
      previewContainer.appendChild(imgElement)
      document.body.appendChild(previewContainer)
      
      // 点击关闭
      previewContainer.addEventListener('click', () => {
        document.body.removeChild(previewContainer)
      })
    },
  }
}
</script>

<style scoped>
.recharge-submit {
  min-height: 100vh;
  background-color: #F8F9FA;
  padding-bottom: 80px;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 16px;
  background-color: #fff;
  border-bottom: 1px solid #EBEDF0;
}

.header-left {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.icon-arrow-left::before {
  content: '←';
  font-size: 18px;
  color: #333;
}

.header-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.header-right {
  width: 44px;
}

.recharge-info {
  margin: 16px;
  background-color: #fff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.amount-section {
  text-align: center;
  padding-bottom: 20px;
  border-bottom: 1px solid #EBEDF0;
  margin-bottom: 20px;
}

.amount-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.amount-value {
  font-size: 32px;
  font-weight: 600;
  color: #FF6B35;
}



/* 银行信息样式 */
.bank-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.bank-item:last-child {
  border-bottom: none;
}

.bank-label {
  font-size: 14px;
  color: #666;
  flex-shrink: 0;
  width: 80px;
}

.bank-value-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-grow: 1;
}

.bank-value {
  font-size: 15px;
  color: #333;
  font-weight: 500;
  margin-right: 10px;
  word-break: break-all;
}

.item-copy-btn {
  font-size: 12px;
  color: #FF6B35;
  background-color: #FFF2ED;
  padding: 4px 8px;
  border-radius: 12px;
  flex-shrink: 0;
  cursor: pointer;
}

.payment-notice {
  margin: 16px;
  background-color: #fff;
  border-radius: 12px;
  padding: 20px;
}

.notice-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}

.icon-info::before {
  content: 'ℹ';
  font-size: 16px;
  color: #FF6B35;
  margin-right: 8px;
}

.notice-content p {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  margin: 6px 0;
}

.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  background-color: #fff;
  border-top: 1px solid #EBEDF0;
}

.submit-btn {
  width: 100%;
  height: 48px;
  background: linear-gradient(45deg, #FF6B35, #FF8A50);
  color: #fff;
  border: none;
  border-radius: 24px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 107, 53, 0.3);
}

.submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.submit-btn.loading {
  background: #ccc;
}

.loading-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid #fff;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 结果弹窗样式 */
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.result-popup {
  background-color: #fff;
  border-radius: 16px;
  padding: 32px 24px;
  margin: 0 24px;
  text-align: center;
  max-width: 320px;
  width: 100%;
}

.result-icon {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16px;
  font-size: 32px;
  color: #fff;
}

.result-icon.success {
  background-color: #52C41A;
}

.result-icon.error {
  background-color: #FF4757;
}

.icon-check::before {
  content: '✓';
}

.icon-close::before {
  content: '✕';
}

.result-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.result-message {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
  margin-bottom: 24px;
}

.result-actions {
  display: flex;
  gap: 12px;
}

.result-btn {
  flex: 1;
  height: 40px;
  border: 1px solid #D9D9D9;
  border-radius: 20px;
  background-color: #fff;
  color: #333;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.result-btn.primary {
  background-color: #FF6B35;
  border-color: #FF6B35;
  color: #fff;
}

.result-btn:hover {
  opacity: 0.8;
}

/* 银行信息样式 */
.bank-info {
  margin: 16px;
  background-color: #fff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
}

.icon-bank::before {
  content: '🏦';
  margin-right: 8px;
}

.icon-upload::before {
  content: '📤';
  margin-right: 8px;
}

.bank-details {
  margin-bottom: 16px;
}

.bank-item {
  display: flex;
  justify-content: space-between;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
}

.bank-item:last-child {
  border-bottom: none;
}

.bank-label {
  color: #666;
  font-size: 14px;
}

.bank-value {
  color: #333;
  font-weight: 500;
  font-size: 15px; /* 更改为15px */
  word-break: break-all;
}

/* 上传区域样式 */
.upload-section {
  margin: 16px;
  background-color: #fff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.upload-area {
  border: 2px dashed #ddd;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  background-color: #fafafa;
  transition: all 0.3s ease;
}

.upload-area:hover {
  border-color: #FF6B35;
  background-color: #fff9f6;
}

.upload-icon {
  font-size: 32px;
  color: #999;
  margin-bottom: 10px;
}

.icon-camera::before {
  content: '📷';
}

.upload-text {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #333;
}

.upload-hint {
  font-size: 12px;
  color: #999;
}

.preview-content {
  position: relative;
}

.preview-image {
  max-width: 100%;
  max-height: 200px;
  border-radius: 8px;
}

.change-btn {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background: rgba(0,0,0,0.6);
  color: #fff;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 12px;
}

.upload-status {
  text-align: center;
  margin-top: 10px;
  font-size: 14px;
  color: #666;
}

/* 转账人信息 */
.transfer-name-container {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #f0f0f0;
}

.transfer-name-label {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  font-weight: 500;
}

.transfer-name-input {
  width: 100%;
  height: 44px;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 0 15px;
  font-size: 14px;
  color: #333;
  background-color: #fff;
  outline: none;
}

.transfer-name-input:focus {
  border-color: #FF6B35;
}
</style>
