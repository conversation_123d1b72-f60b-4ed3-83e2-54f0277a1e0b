<template>
  <div class="guide">
    <!-- 头部 -->
    <div class="header">
      <div class="back-icon" @click="goBack">
        <img src="@/assets/icons/back-icon.svg" alt="返回">
      </div>
      <div class="title">详细内容</div>
    </div>

    <!-- 内容区域 -->
    <div class="content">      <div class="loading" v-if="loading">
        <van-loading size="24px" color="#FF618B">加载中...</van-loading>
      </div>
      
      <!-- iframe显示攻略内容 -->
      <iframe 
        v-if="guideUrl && !loading"
        :src="guideUrl" 
        class="guide-iframe"
        frameborder="0"
        @load="onIframeLoad"
        @error="onIframeError"
      ></iframe>
        <!-- 错误状态 -->
      <div class="error-state" v-if="error">
        <div class="error-icon">
          <svg width="48" height="48" viewBox="0 0 48 48" fill="none">
            <circle cx="24" cy="24" r="20" fill="#f5f5f5"/>
            <path d="M24 14v12M24 30h.01" stroke="#999" stroke-width="2" stroke-linecap="round"/>
          </svg>
        </div>
        <div class="error-text">{{ errorMessage }}</div>
        <button class="retry-btn" @click="retryLoad">重试</button>
      </div>
        <!-- 空状态 -->
      <div class="empty-state" v-if="!guideUrl && !loading && !error">
        <van-empty description="暂无详细内容" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Guide',
  data() {
    return {
      guideUrl: '',
      loading: true,
      error: false,
      errorMessage: '加载失败，请重试'
    }
  },
  created() {
    this.initGuide()
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
      // 初始化攻略页面
    initGuide() {
      // 从路由参数获取URL
      const urlFromQuery = this.$route.query.url
      
      if (urlFromQuery) {
        this.guideUrl = urlFromQuery
        this.loading = false
      } else {
        // 如果没有URL参数，则调用API获取
        this.fetchGuideUrl()
      }
    },
    
    // 获取攻略URL
    async fetchGuideUrl() {
      const userToken = localStorage.getItem('user_token')
      
      if (!userToken) {
        this.$toast('请先登录')
        this.$nextTick(() => { this.$router.replace('/login') })
        return
      }
      
      try {
        this.loading = true
        this.error = false
        
        const response = await this.$http.get('/member/getPage', {
          params: {
            name: 'help1'
          },
          headers: {
            'authorization': userToken
          }
        })
        
        if (response.data.code === 200) {
          const guideUrl = response.data.data
          
          if (guideUrl && typeof guideUrl === 'string') {
            this.guideUrl = guideUrl
          } else {
            this.error = true
            this.errorMessage = '获取攻略链接失败'
          }
        } else {
          this.error = true
          this.errorMessage = response.data.msg || '获取新手攻略失败'
        }
      } catch (error) {
        console.error('获取新手攻略失败:', error)
        this.error = true
        this.errorMessage = '网络异常，请稍后重试'
      } finally {
        this.loading = false
      }
    },
    
    // iframe加载完成
    onIframeLoad() {
      this.loading = false
      this.error = false
    },
      // iframe加载错误
    onIframeError() {
      this.loading = false
      this.error = true
      this.errorMessage = '页面加载失败，请检查网络连接'
    },
    
    // 重试加载
    retryLoad() {
      if (this.guideUrl) {
        this.loading = true
        this.error = false
        // 重新加载iframe
        const iframe = document.querySelector('.guide-iframe')
        if (iframe) {
          iframe.src = this.guideUrl
        }
      } else {
        this.fetchGuideUrl()
      }
    }
  }
}
</script>

<style scoped>
.guide {
  min-height: 100vh;
  background-color: #f6f6f6;
  display: flex;
  flex-direction: column;
  padding: 0;  /* 确保没有内边距 */
  margin: 0;   /* 确保没有外边距 */
}

/* 头部样式 */
.header {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  background-color: #fff;
  position: relative;
  flex-shrink: 0;
}

.back-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.back-icon img {
  width: 20px;
  height: 20px;
}

.title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

/* 内容区域 */
.content {
  flex: 1;
  position: relative;
  display: flex;
  flex-direction: column;
  padding: 0; /* 确保没有内边距 */
  margin: 0;  /* 确保没有外边距 */
}

/* 加载状态 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
}

/* iframe样式 */
.guide-iframe {
  width: 100%;
  flex: 1;
  min-height: calc(100vh - 60px);
  border: none;
  background-color: #fff;
}

/* 错误状态 */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 20px;
  text-align: center;
}

.error-icon {
  margin-bottom: 16px;
}

.error-text {
  font-size: 14px;
  color: #666;
  margin-bottom: 20px;
}

.retry-btn {
  background-color: #FF618B;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.retry-btn:hover {
  background-color: #e55a7a;
}

.retry-btn:active {
  background-color: #d54971;
}

/* 空状态 */
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .header {
    padding: 8px 12px;
  }
  
  .title {
    font-size: 16px;
  }
  
  .guide-iframe {
    min-height: calc(100vh - 56px);
  }
}
</style>
