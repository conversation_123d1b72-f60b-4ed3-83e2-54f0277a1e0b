<template>
  <div class="register">
    <!-- 背景图形 -->
    <div class="background">
      <!-- 渐变背景 -->
      <div class="gradient-bg"></div>
      <!-- Logo区域 -->
      <div class="logo-container">
        <img src="@/assets/images/login/logo.png" alt="Logo" class="logo">
        <h1 class="brand-name">善之力</h1>
      </div>
    </div>

    <!-- 注册表单 -->
    <div class="register-container">
      <div class="register-form">
        <div class="form-title">注册</div>
        <div class="form-group">
          <div class="input-box">
            <input type="tel" v-model="phone" placeholder="请输入您的手机号" maxlength="11">
          </div>
        </div>

        <div class="form-group">
          <div class="input-box">
            <input type="text" v-model="inviteCode" placeholder="请输入邀请码">
          </div>
        </div>

        <div class="form-group">
          <div class="input-box">
            <input :type="passwordVisible ? 'text' : 'password'" v-model="password" placeholder="请输入密码">
            <div class="toggle-password" @click="togglePasswordVisible">
              <i class="icon-eye" :class="{ 'visible': passwordVisible }"></i>
            </div>
          </div>
        </div>

        <div class="form-group">
          <div class="input-box">
            <input :type="confirmPasswordVisible ? 'text' : 'password'" v-model="confirmPassword" placeholder="请确认密码">
            <div class="toggle-password" @click="toggleConfirmPasswordVisible">
              <i class="icon-eye" :class="{ 'visible': confirmPasswordVisible }"></i>
            </div>
          </div>
        </div>

        <div class="agreement">
          <div class="checkbox-wrapper">
            <div class="checkbox" :class="{ 'checked': agreeToTerms }" @click="toggleAgreement">
              <div class="checkbox-inner" v-if="agreeToTerms"></div>
            </div>
          </div>
          <span>我已阅读并同意</span>
          <span class="link" @click="viewTerms">《用户协议》</span>
          <span>和</span>
          <span class="link" @click="viewPrivacy">《隐私协议》</span>
        </div>

        <button class="register-btn" @click="register">
          注册
        </button>

        <div class="login-section" @click="goToLogin">
          <span class="login-text">已有账号，去登录</span>
          <i class="icon-arrow"></i>
        </div>
      </div>
    </div>
  </div>
</template>

<script>

export default {
  name: 'Register', data() {
    return {
      phone: '',
      inviteCode: '',
      password: '',
      confirmPassword: '',
      passwordVisible: false,
      confirmPasswordVisible: false,
      agreeToTerms: true,
      deviceInfo: null // 存储设备信息
    }
  },
  created() {
    // 初始化设备信息
    this.initDeviceInfo()
  }, methods: {
    // 初始化设备信息
    initDeviceInfo() {
      try {
        // 生成设备ID（使用时间戳+随机数）
        const deviceId = Date.now().toString() + Math.floor(Math.random() * 1000000).toString()

        // 获取用户代理信息
        const userAgent = navigator.userAgent

        // 简单的平台检测
        let platform = 'web'
        let model = 'Unknown'
        let system = 'Unknown'

        if (/Android/i.test(userAgent)) {
          platform = 'android'
          const androidMatch = userAgent.match(/Android\s([0-9\.]+)/)
          system = androidMatch ? `Android ${androidMatch[1]}` : 'Android'

          // 尝试获取设备型号
          const modelMatch = userAgent.match(/\(([^\)]+)\)/)
          if (modelMatch && modelMatch[1]) {
            const parts = modelMatch[1].split(';')
            model = parts[parts.length - 1].trim() || 'Android Device'
          } else {
            model = 'Android Device'
          }
        } else if (/iPhone|iPad|iPod/i.test(userAgent)) {
          platform = 'ios'
          const iosMatch = userAgent.match(/OS ([0-9_]+)/)
          system = iosMatch ? `iOS ${iosMatch[1].replace(/_/g, '.')}` : 'iOS'

          if (/iPhone/i.test(userAgent)) {
            model = 'iPhone'
          } else if (/iPad/i.test(userAgent)) {
            model = 'iPad'
          } else {
            model = 'iPod'
          }
        } else {
          platform = 'web'
          system = navigator.platform || 'Web Browser'
          model = 'Web Browser'
        }

        this.deviceInfo = {
          deviceId: deviceId,
          platform: platform,
          model: model,
          system: system
        }
      } catch (error) {
        console.error('初始化设备信息失败:', error)
        // 如果获取失败，使用默认值
        this.deviceInfo = {
          deviceId: Date.now().toString(),
          platform: 'web',
          model: 'Unknown',
          system: 'Unknown'
        }
      }
    },
    togglePasswordVisible() {
      this.passwordVisible = !this.passwordVisible
    },
    toggleConfirmPasswordVisible() {
      this.confirmPasswordVisible = !this.confirmPasswordVisible
    }, toggleAgreement() {
      this.agreeToTerms = !this.agreeToTerms
    }, async register() {
      if (!this.phone || this.phone.length !== 11) {
        this.$toast('请输入正确的手机号码')
        return
      }

      if (!this.inviteCode) {
        this.$toast('请输入邀请码')
        return
      }

      if (!this.password || this.password.length < 6) {
        this.$toast('密码长度不能小于6位')
        return
      }

      if (this.password !== this.confirmPassword) {
        this.$toast('两次输入的密码不一致')
        return
      } if (!this.agreeToTerms) {
        this.$toast('请先阅读并同意用户协议和隐私协议')
        return
      }

      // 检查设备信息是否初始化
      if (!this.deviceInfo) {
        this.initDeviceInfo()
      }

      try {
        const requestData = {
          mobile: this.phone,
          invite: this.inviteCode,
          password: this.password,
          password_confirm: this.confirmPassword,
          platform: this.deviceInfo.platform
        }

        const response = await this.$http.post('/register/doCreate', requestData)

        if (response.data.code === 200) {
          this.$toast('注册成功')
          setTimeout(() => {
            this.$nextTick(() => { this.$router.replace('/login') })
          }, 1000)
        } else {
          this.$toast(response.data.msg || '注册失败，请稍后重试')
        }
      } catch (error) {
        console.error('注册失败:', error)
        this.$toast('网络错误，请稍后重试')
      }
    },
    viewTerms() {
      // 查看用户协议
    },
    viewPrivacy() {
      // 查看隐私协议
    }, goToLogin() {
      this.$nextTick(() => { this.$router.replace('/login') })
    }
  }
}
</script>

<style scoped>
.register {
  min-height: 100vh;
  font-family: PingFang SC, Arial, sans-serif;
  position: relative;
  overflow: hidden;
  background: #FFFFFF;
}

/* 背景部分样式 */
.background {
  position: relative;
  height: 300px;
}

.gradient-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #EACFD8 2.88%, #50B9F9 25.48%, #0474FC 38.94%, #BAB8E0 78%, #95BAEB 95.19%);
}

.logo-container {
  position: absolute;
  top: 35%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 2;
}

.logo {
  width: 76.58px;
  height: 76.58px;
  background: #FFFFFF;
  border-radius: 25px;
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05), inset 0px 0px 2px 0px rgba(0, 0, 0, 0.25);
  margin-bottom: 15px;
  object-fit: contain;
}

.brand-name {
  color: #FFFFFF;
  font-size: 24px;
  font-weight: 700;
  margin: 0;
  font-family: 'Alibaba PuHuiTi 2.0', sans-serif;
}

.register-container {
  position: absolute;
  top: 210px;
  left: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(6.7px);
  border-radius: 20px 20px 0 0;
  padding: 30px 38px;
  box-sizing: border-box;
  min-height: calc(100vh - 210px);
}

.register-form {
  width: 100%;
}

.form-title {
  font-family: "Coda Caption", sans-serif;
  font-weight: 800;
  font-size: 20px;
  color: #333;
  margin-bottom: 12px;
}

.form-subtitle {
  font-size: 14px;
  color: #0474FC;
  margin-bottom: 20px;
  line-height: 1.4;
  max-width: 240px;
}

.form-group {
  margin-bottom: 14px;
}

.input-box {
  position: relative;
  width: 100%;
  height: 52px;
  background: #FAFAFA;
  border-radius: 35px;
  padding: 0 20px;
  display: flex;
  align-items: center;
}

.input-box input {
  flex: 1;
  height: 100%;
  border: none;
  outline: none;
  background: transparent;
  font-size: 16px;
  color: #333;
}

.input-box input::placeholder {
  color: #BBBCC0;
}

.toggle-password {
  width: 19px;
  height: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-left: 10px;
}

.icon-eye {
  width: 16px;
  height: 9px;
  border: 1px solid #BBBCC0;
  border-radius: 4.5px;
  position: relative;
}

.icon-eye::before {
  content: '';
  position: absolute;
  width: 3.6px;
  height: 3.6px;
  background: #BBBCC0;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.icon-eye.visible::before {
  display: none;
}

.agreement {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #333;
  margin: 30px 0;
}

.checkbox-wrapper {
  display: inline-flex;
  align-items: center;
  margin-right: 6px;
}

.checkbox {
  width: 16px;
  height: 16px;
  border: 1px solid #0474FC;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.checkbox-inner {
  width: 10px;
  height: 10px;
  background: #0474FC;
  border-radius: 50%;
}

.agreement .link {
  color: #0474FC;
  margin: 0 2px;
  cursor: pointer;
}

.register-btn {
  width: 100%;
  height: 44px;
  background: #0474FC;
  border-radius: 35px;
  color: #fff;
  font-size: 16px;
  border: none;
  cursor: pointer;
  margin-bottom: 27px;
}

.login-section {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #0474FC;
  cursor: pointer;
}

.login-text {
  margin-right: 6px;
}

.icon-arrow {
  width: 16px;
  height: 12px;
  position: relative;
}

.icon-arrow::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  width: 12px;
  height: 1px;
  background: #0474FC;
  transform: translateY(-50%);
}

.icon-arrow::after {
  content: '';
  position: absolute;
  top: 50%;
  right: 0;
  width: 6px;
  height: 6px;
  border-top: 1px solid #0474FC;
  border-right: 1px solid #0474FC;
  transform: translateY(-50%) rotate(45deg);
}
</style>
