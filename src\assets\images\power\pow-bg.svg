<!-- 善之力项目Banner背景 -->
<svg width="375" height="280" viewBox="0 0 375 280" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#EACFD8" />
      <stop offset="25%" stop-color="#50B9F9" />
      <stop offset="50%" stop-color="#0474FC" />
      <stop offset="75%" stop-color="#BAB8E0" />
      <stop offset="100%" stop-color="#95BAEB" />
    </linearGradient>
    <filter id="blur1" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceGraphic" stdDeviation="29" />
    </filter>
    <filter id="blur2" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceGraphic" stdDeviation="20" />
    </filter>
  </defs>
  <rect width="375" height="280" fill="#FFFFFF" />
  <rect width="375" height="190" fill="url(#gradient)" />
  <circle cx="250" cy="30" r="80" fill="#FEEEEE" filter="url(#blur1)" opacity="0.6" />
  <circle cx="100" cy="50" r="120" fill="#E8EFFB" filter="url(#blur2)" opacity="0.6" />
</svg>
