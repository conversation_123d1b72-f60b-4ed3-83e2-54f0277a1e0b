<template>
  <div class="recharge">
    <!-- 头部 -->
    <div class="header">
      <div class="back-icon" @click="goBack">
        <img src="@/assets/icons/back-icon.svg" alt="返回">
      </div>
      <div class="title">充值</div>
      <div class="right-icon" @click="goToRechargeDetail">
        <span>明细</span>
      </div>
    </div>

    <!-- 账户余额卡片 -->
    <div class="balance-card">
      <div class="balance-card-inner">
        <div class="balance-icon">
          <img src="@/assets/images/mine/user-avatar.png" alt="用户头像">
        </div>
        <div class="hearts-pattern">
          <img src="@/assets/images/login/hearts-bg.png" alt="装饰图案">
        </div>
        <div class="balance-info">
          <div class="balance-label">账户余额</div>
          <div class="balance-amount">{{ accountBalance }}</div>
        </div>
      </div>
    </div>

    <!-- 金额选择 -->
    <div class="amount-section">
      <div class="section-title">选择金额</div>
      <div class="amount-grid">
        <div 
          class="amount-item" 
          :class="{ active: amount === quickAmount }" 
          v-for="quickAmount in quickAmounts" 
          :key="quickAmount"
          @click="selectAmount(quickAmount)"
        >
          {{ quickAmount }}
          <div class="check-icon" v-if="amount === quickAmount">
            <svg width="24" height="21" viewBox="0 0 24 21" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 16.1698L22.5 2.5" stroke="#FF6589" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M9 16.1698L1.5 9.33962" stroke="#FF6589" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
        </div>
      </div>
    </div>

    <!-- 充值金额输入 -->
    <div class="input-amount-section">
      <div class="section-title">充值金额</div>
      <div class="amount-input-container">
        <input 
          class="amount-input" 
          v-model="amount" 
          type="number" 
          placeholder="请输入充值金额，最少500元"
          @input="onAmountChange"
        >
      </div>
    </div>

    <!-- 充值说明 -->
    <div class="recharge-notes">
      <div class="notes-header">
        <div class="notes-icon">
          <svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M11 21C16.5228 21 21 16.5228 21 11C21 5.47715 16.5228 1 11 1C5.47715 1 1 5.47715 1 11C1 16.5228 5.47715 21 11 21Z" stroke="#FF6689" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M11 15V11" stroke="#FF6689" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M11 7H11.01" stroke="#FF6689" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <div class="notes-title">充值说明</div>
      </div>
      <div class="notes-content">        1.单笔充值金额不得低于500元<br>
        2.充值时间：周一至周日24小时<br>
        3.使用网银或手机银行付款金额必须与提交的充值金额一致<br>
        （如：充值界面输入500元，则付款金额也必须是500元）<br>
        4.如有其他疑问，请咨询在线客服。
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="next-button" @click="confirmRecharge">
      下一步
    </div>
    
    <!-- 支付渠道选择弹窗 -->
    <div class="payment-channels-popup" v-if="showPaymentChannels">
      <div class="popup-overlay" @click="showPaymentChannels = false"></div>
      <div class="popup-content">
        <div class="popup-header">
          <div class="popup-title">选择支付渠道</div>
          <div class="popup-close" @click="showPaymentChannels = false">×</div>
        </div>
        <div class="popup-body">
          <!-- 加载状态-->
          <div class="loading-state" v-if="loadingChannels">
            <van-loading type="spinner" color="#FF618B">加载支付渠道中..</van-loading>
          </div>
          
          <!-- 支付渠道列表 -->
          <div 
            class="channel-item" 
            v-for="channel in paymentChannels" 
            :key="channel.id" 
            @click="selectPaymentChannel(channel)"
          >
            <div class="channel-icon">
              <img :src="getChannelIcon(channel.type)" alt="支付渠道图标">
            </div>
            <div class="channel-info">
              <div class="channel-name">{{ channel.title }}</div>
              <div class="channel-desc">{{ getChannelDesc(channel.type) }}</div>
            </div>
            <div class="channel-select">
              <div class="select-circle" :class="{'selected': selectedChannel && selectedChannel.id === channel.id}"></div>
            </div>
          </div>
          
          <!-- 空状态-->
          <div class="empty-state" v-if="!loadingChannels && paymentChannels.length === 0">
            <div class="empty-text">暂无可用支付渠道</div>
          </div>
        </div>
        <div class="popup-footer">
          <div class="confirm-button" @click="proceedToPayment">确认支付</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ImageErrorHandler from '@/utils/ImageErrorHandler.js'

export default {
  name: 'Recharge',
  data() {
    return {
      amount: '500',
      accountBalance: '20215.99',
      quickAmounts: ['500', '5000', '10000', '20000', '50000', '100000', '200000', '500000'],
      showPaymentChannels: false,
      selectedChannel: null,
      paymentChannels: [],
      loadingChannels: false
    }
  },
  methods: {
    // 处理图片加载错误
    handleImageError(event, type) {
      ImageErrorHandler.handleImageError(event, type)
    },
    
    goBack() {
      this.$router.go(-1)
    },
    
    goToRechargeDetail() {
      this.$router.push('/recharge-detail')
    },
    
    selectAmount(amount) {
      this.amount = amount
    },
    
    onAmountChange() {
      // 当输入金额改变时，清除快捷选择状态
      const inputAmount = this.amount.toString()
      if (!this.quickAmounts.includes(inputAmount)) {
        // 可以在这里添加额外的逻辑
      }
    },
    
    async confirmRecharge() {
      if (!this.amount || parseFloat(this.amount) <= 0) {
        this.$toast('请输入有效的充值金额')
        return
      }

      if (parseFloat(this.amount) < 500) {
        this.$toast('充值金额最少500元')
        return
      }

      // 显示支付渠道选择弹窗并加载支付渠道
      this.showPaymentChannels = true
      await this.loadPaymentChannels()
    },
    
    // 加载支付渠道
    async loadPaymentChannels() {
      const userToken = localStorage.getItem('user_token')
      if (!userToken) {
        this.$toast('请先登录')
        this.$nextTick(() => { this.$router.replace('/login') })
        return
      }
      
      this.loadingChannels = true
      
      try {
        const response = await this.$http.get(`/finance/getDepositPayment?amount=${this.amount}`, {
          headers: {
            'authorization': userToken
          }
        })
        
        if (response.data.code === 200) {
          this.paymentChannels = response.data.data.payment || []
          
          if (this.paymentChannels.length === 0) {
            this.$toast('当前金额暂无可用支付渠道')
          }
        } else {
          this.$toast(response.data.msg || '获取支付渠道失败')
          this.paymentChannels = []
        }
      } catch (error) {
        console.error('获取支付渠道失败:', error)
        this.$toast('网络异常，请稍后重试')
        this.paymentChannels = []
      } finally {
        this.loadingChannels = false
      }
    },
    
    // 根据支付类型获取图标
    getChannelIcon(type) {
      const iconMap = {
        1: require('@/assets/icons/user-icon.svg'),        // 微信支付
        2: require('@/assets/icons/points-icon.svg'),      // 支付宝
        3: require('@/assets/icons/shop-icon.svg'),        // 银联支付
        4: require('@/assets/icons/order-icon.svg')        // 网银支付
      }
      
      try {
        return iconMap[type] || require('@/assets/icons/default-icon.svg')
      } catch (error) {
        console.warn(`支付渠道 ${type} 的图标加载失败`)
        return require('@/assets/icons/default-icon.svg')
      }
    },
    
    // 根据支付类型获取描述
    getChannelDesc(type) {
      const descMap = {
        1: '快速到账，方便安全',        2: '安全便捷，即时到账',
        3: '支持各大银行卡支付',
        4: '各大银行网银直接支付'
      }
      
      return descMap[type] || '安全便捷的支付方式'
    },
    
    selectPaymentChannel(channel) {
      this.selectedChannel = channel
    },
    
    proceedToPayment() {
      if (!this.selectedChannel) {
        this.$toast('请选择支付渠道')
        return
      }
      
      // 这里可以进行支付处理
      this.$toast.loading({
        message: '正在跳转支付...',
        forbidClick: true
      })
      
      setTimeout(() => {
        this.$toast.clear()
        this.$toast.success('支付处理成功')
        
        // 模拟支付成功后跳转
        setTimeout(() => {
          this.$router.push({
            path: '/recharge-detail',
            query: { 
              amount: this.amount,
              channelId: this.selectedChannel.id,
              channelTitle: this.selectedChannel.title,
              success: true
            }
          })
        }, 1000)
      }, 1500)
    }
  }
}
</script>

<style scoped>
.recharge {
  min-height: 100vh;
  background-color: #F8F9FA;
  padding-bottom: 80px;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 16px;
  background-color: #fff;
  position: relative;
}

.back-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon img {
  width: 9.6px;
  height: 16.5px;
}

.title {
  font-size: 17px;
  font-weight: 400;
  color: #111827;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.right-icon {
  font-size: 14px;
  color: #666;
}

/* 账户余额卡片 */
.balance-card {
  margin: 15px;
  height: 123px;
  position: relative;
  border-radius: 10px;
}

.balance-card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 10px;
  background: linear-gradient(180deg, #FF7D87 0%, #FF5C89 100%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding-left: 104px;
  overflow: hidden;
}

.balance-icon {
  position: absolute;
  left: 13px;
  top: 16px;
  width: 92px;
  height: 92px;
  border-radius: 50%;
  overflow: hidden;
}

.balance-icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hearts-pattern {
  position: absolute;
  right: 5px;
  top: -20px;
  width: 145px;
  height: 145px;
  opacity: 0.4;
  pointer-events: none;
}

.hearts-pattern img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.balance-info {
  color: #FFFFFF;
}

.balance-label {
  font-size: 14px;
  margin-bottom: 12px;
}

.balance-amount {
  font-size: 16px;
  font-weight: 600;
}

/* 金额选择部分 */
.amount-section {
  background-color: #FFFFFF;
  margin: 15px;
  padding: 15px;
  border-radius: 8px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  margin-bottom: 12px;
}

.amount-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(3, auto);
  gap: 10px;
}

.amount-item {
  height: 36px;
  background-color: #FFFFFF;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 500;
  color: #666666;
  position: relative;
  border: 1px solid #EAEAEA;
  cursor: pointer;
  transition: all 0.3s ease;
}

.amount-item:hover {
  border-color: #FF6589;
}

.amount-item.active {
  border: 2px solid #FF6589;
  background-color: rgba(255, 101, 137, 0.05);
  color: #FF6589;
}

.check-icon {
  position: absolute;
  right: -2px;
  top: -2px;
  width: 24px;
  height: 21px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 充值金额输入 */
.input-amount-section {
  background-color: #FFFFFF;
  margin: 15px;
  padding: 15px;
  border-radius: 8px;
}

.amount-input-container {
  background-color: #FFFFFF;
  border-radius: 8px;
  border: 1px solid #EAEAEA;
  height: 50px;
  display: flex;
  align-items: center;
  padding: 0 15px;
}

.amount-input {
  width: 100%;
  height: 100%;
  border: none;
  outline: none;
  font-size: 14px;
  color: #333;
}

.amount-input::placeholder {
  color: #B2B2B9;
}

/* 充值说明 */
.recharge-notes {
  background-color: #FFFFFF;
  margin: 15px;
  padding: 20px;
  border-radius: 9px;
}

.notes-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.notes-icon {
  margin-right: 10px;
}

.notes-title {
  font-size: 16px;
  color: #333333;
}

.notes-content {
  font-size: 14px;
  color: #9E9E9E;
  line-height: 1.5;
}

/* 底部按钮 */
.next-button {
  position: fixed;
  bottom: 20px;
  left: 16px;
  right: 16px;
  height: 40px;
  background: linear-gradient(180deg, #FF7D87 0%, #FF5C89 100%);
  color: #fff;
  font-size: 15px;
  font-weight: 500;
  border-radius: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: opacity 0.3s ease;
}

.next-button:active {
  opacity: 0.8;
}

/* 支付渠道弹窗样式 */
.payment-channels-popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.popup-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.popup-content {
  position: relative;
  width: 100%;
  background-color: #FFFFFF;
  border-radius: 16px 16px 0 0;
  padding: 20px;
  z-index: 1001;
  animation: slideUp 0.3s ease-out;
  max-height: 70vh;
  overflow-y: auto;
}

@keyframes slideUp {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 15px;
  border-bottom: 1px solid #f5f5f5;
}

.popup-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.popup-close {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #999;
  cursor: pointer;
}

.popup-body {
  padding: 15px 0;
}

.loading-state {
  text-align: center;
  padding: 40px 0;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
}

.empty-text {
  color: #999;
  font-size: 14px;
}

.channel-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.channel-item:hover {
  background-color: rgba(255, 101, 137, 0.05);
}

.channel-item:last-child {
  border-bottom: none;
}

.channel-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  overflow: hidden;
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
}

.channel-icon img {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

.channel-info {
  flex: 1;
}

.channel-name {
  font-size: 16px;
  color: #333;
  margin-bottom: 4px;
  font-weight: 500;
}

.channel-desc {
  font-size: 12px;
  color: #999;
}

.channel-select {
  padding: 0 10px;
}

.select-circle {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 1px solid #ddd;
  position: relative;
  transition: all 0.3s ease;
}

.select-circle.selected {
  border: 1px solid #FF6589;
  background-color: rgba(255, 101, 137, 0.1);
}

.select-circle.selected:after {
  content: "";
  position: absolute;
  width: 10px;
  height: 10px;
  background-color: #FF6589;
  border-radius: 50%;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.popup-footer {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #f5f5f5;
}

.confirm-button {
  width: 100%;
  height: 40px;
  background: linear-gradient(180deg, #FF7D87 0%, #FF5C89 100%);
  color: #fff;
  font-size: 15px;
  font-weight: 500;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: opacity 0.3s ease;
}

.confirm-button:active {
  opacity: 0.8;
}
</style>
