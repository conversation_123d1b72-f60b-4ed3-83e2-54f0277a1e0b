<template>
  <div class="bank-card">
    <!-- 头部 -->
    <div class="header">
      <div class="back-icon" @click="goBack">
        <img src="@/assets/icons/back-icon.svg" alt="返回">
      </div>
      <div class="title">银行卡管理</div>
    </div>

    <!-- 银行卡列表 -->
    <div class="card-list">
      <div class="card-item" v-for="(card, index) in bankCards" :key="index" @click="showCardActions(card)">
        <div class="card-icon">
          <img :src="card.icon" alt="银行图标">
        </div>
        <div class="card-info">
          <div class="card-name">{{ card.bankName }}</div>
          <div class="card-type">{{ card.cardType }}</div>
          <div class="card-number">{{ formatCardNumber(card.cardNumber) }}</div>
        </div>
      </div>

      <!-- 空状态 -->
      <div class="empty-state" v-if="bankCards.length === 0">
        <van-empty description="暂无银行卡" />
      </div>
    </div>

    <!-- 按钮区域 -->
    <div class="button-group">
      <!-- 添加银行卡按钮 -->
      <div class="action-btn add-card-btn" @click="goToAddCard">
        <van-icon name="plus" />
        <span>添加银行卡</span>
      </div>
      
      <!-- 银行卡绑定按钮 -->
      <div class="action-btn bind-card-btn" @click="goToBindCard">
        <van-icon name="edit" />
        <span>绑定银行卡</span>
      </div>
    </div>

    <!-- 银行卡操作弹窗 -->
    <van-action-sheet v-model="showActionSheet" :actions="cardActions" cancel-text="取消" @select="onSelectAction"
      @cancel="showActionSheet = false" />

    <!-- 删除确认弹窗 -->
    <van-dialog v-model="showDeleteConfirm" title="删除银行卡" message="确认要删除该张银行卡吗" show-cancel-button
      @confirm="confirmDelete" />

    <!-- 设置默认确认弹窗 -->
    <van-dialog v-model="showSetDefaultConfirm" title="设置默认银行卡" message="确认要将该银行卡设为默认卡吗" show-cancel-button
      @confirm="confirmSetDefault" />
  </div>
</template>

<script>

export default {
  name: "BankCard",
  data() {
    return {
      bankCards: [
        {
          id: 1,
          bankName: "中国工商银行",
          cardType: "储蓄卡",
          cardNumber: "6222021234567890123",
          icon: require("@/assets/images/avatar.jpg"),
          isDefault: true
        },
        {
          id: 2,
          bankName: "中国建设银行",
          cardType: "储蓄卡",
          cardNumber: "6227001234567890123",
          icon: require("@/assets/images/avatar.jpg"),
          isDefault: false
        },
        {
          id: 3,
          bankName: "中国农业银行",
          cardType: "信用卡",
          cardNumber: "6228481234567890123",
          icon: require("@/assets/images/avatar.jpg"),
          isDefault: false
        }
      ],
      showActionSheet: false,
      showDeleteConfirm: false,
      showSetDefaultConfirm: false,
      currentCard: null,
      cardActions: [
        { name: "设为默认", color: "#FF618B" },
        { name: "删除", color: "#ee0a24" }
      ]
    };
  },
  methods: {
    goBack() {
      this.$router.go(-1);
    },
    goToAddCard() {
      this.$router.push("/bank-card/add");
    },
    goToBindCard() {
      this.$router.push("/bank-card/bind");
    },
    formatCardNumber(number) {
      if (!number) return "";
      return number.replace(/(\d{4})(?=\d)/g, "$1 ");
    },
    showCardActions(card) {
      this.currentCard = card;

      // 如果已经是默认卡，则不显示"设为默认"选项
      if (card.isDefault) {
        this.cardActions = [
          { name: "删除", color: "#ee0a24" }
        ];
      } else {
        this.cardActions = [
          { name: "设为默认", color: "#FF618B" },
          { name: "删除", color: "#ee0a24" }
        ];
      }

      this.showActionSheet = true;
    },
    onSelectAction(action) {
      this.showActionSheet = false;

      if (action.name === "删除") {
        this.showDeleteConfirm = true;
      } else if (action.name === "设为默认") {
        this.showSetDefaultConfirm = true;
      }
    },
    confirmDelete() {
      if (!this.currentCard) return;

      // 如果是默认卡且不是唯一的卡，需要先将其他卡设为默认卡
      if (this.currentCard.isDefault && this.bankCards.length > 1) {
        // 找到第一张非当前卡的卡片设为默认
        const newDefaultCard = this.bankCards.find(card => card.id !== this.currentCard.id);
        if (newDefaultCard) {
          newDefaultCard.isDefault = true;
        }
      }

      // 从列表中移除当前卡片
      const index = this.bankCards.findIndex(card => card.id === this.currentCard.id);
      if (index !== -1) {
        this.bankCards.splice(index, 1);
      }

      this.$toast("银行卡已删除");
    },
    confirmSetDefault() {
      if (!this.currentCard) return;

      // 将所有卡片设置为非默认
      this.bankCards.forEach(card => {
        card.isDefault = false;
      });

      // 将当前卡片设为默认
      this.currentCard.isDefault = true;

      this.$toast("已设为默认银行卡");
    }
  }
};
</script>

<style scoped>
.bank-card {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 30px;
}

.header {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  background-color: #fff;
  position: relative;
}

.back-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.card-list {
  margin: 15px;
}

.card-item {
  background: linear-gradient(135deg, #FF618B 0%, #FF8C9F 100%);
  border-radius: 12px;
  padding: 20px;
  color: white;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  box-shadow: 0 4px 10px rgba(255, 97, 139, 0.2);
}

.card-icon {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  overflow: hidden;
  margin-right: 15px;
}

.card-icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.card-info {
  flex: 1;
}

.card-name {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 5px;
}

.card-type {
  font-size: 14px;
  opacity: 0.8;
  margin-bottom: 10px;
}

.card-number {
  font-size: 16px;
  letter-spacing: 1px;
}

.empty-state {
  text-align: center;
  padding: 30px 0;
}

.button-group {
  display: flex;
  justify-content: center;
  padding: 0 15px;
  margin-top: 30px;
}

.action-btn {
  flex: 1;
  height: 44px;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  margin: 0 8px;
}

.add-card-btn {
  background: linear-gradient(to right, #FF618B, #FF8C9F);
  color: white;
  box-shadow: 0 4px 8px rgba(255, 97, 139, 0.3);
}

.bind-card-btn {
  background: #fff;
  color: #FF618B;
  border: 1px solid #FF618B;
}

.action-btn .van-icon {
  margin-right: 5px;
  font-size: 18px;
}
</style>
