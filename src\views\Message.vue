<template>
  <div class="message">
    <!-- 头部 -->
    <div class="header">
      <div class="back-icon" @click="goBack">
        <img src="@/assets/icons/back-icon.svg" alt="返回">
      </div>
      <div class="title">消息中心</div>
    </div>

    <!-- 消息标签 -->
    <div class="message-tabs">
      <div class="message-tab" v-for="(tab, index) in tabs" :key="index" :class="{ active: activeTab === tab.value }"
        @click="activeTab = tab.value">
        {{ tab.name }}
      </div>
    </div>

    <!-- 消息列表 -->
    <div class="message-list">
      <div class="message-item" v-for="(item, index) in filteredMessages" :key="index" :class="{ unread: !item.read }"
        @click="readMessage(item)">
        <div class="message-icon">
          <img :src="getMessageIcon(item.type)" alt="消息图标">
          <div class="unread-dot" v-if="!item.read"></div>
        </div>
        <div class="message-content">
          <div class="message-title">{{ item.title }}</div>
          <div class="message-preview">{{ item.content }}</div>
          <div class="message-time">{{ item.time }}</div>
        </div>
      </div>

      <!-- 空状态 -->
      <div class="empty-state" v-if="filteredMessages.length === 0">
        <van-empty description="暂无消息" />
      </div>
    </div>

    <!-- 消息详情弹窗 -->
    <van-popup v-model="showMessageDetail" position="bottom" round :style="{ height: '70%' }">
      <div class="message-detail">
        <div class="detail-header">
          <div class="detail-title">{{ currentMessage.title }}</div>
          <div class="detail-time">{{ currentMessage.time }}</div>
        </div>
        <div class="detail-content">
          {{ currentMessage.content }}
        </div>
        <div class="detail-actions">
          <div class="action-btn delete" @click="deleteMessage">删除</div>
          <div class="action-btn close" @click="showMessageDetail = false">关闭</div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
export default {
  name: 'Message',
  data() {
    return {
      activeTab: 'all',
      tabs: [
        { name: '全部', value: 'all' },
        { name: '系统', value: 'system' },
        { name: '交易', value: 'transaction' },
        { name: '活动', value: 'activity' }
      ],
      messages: [
        {
          id: 1,
          type: 'system',
          title: '系统维护通知',
          content: '尊敬的用户，我们将于2023年5月28日凌晨2:00-4:00进行系统维护，期间部分功能可能无法正常使用，敬请您的理解。',
          time: '2023-05-28 10:30',
          read: false
        },
        {
          id: 2,
          type: 'transaction',
          title: '充值成功通知',
          content: '您已成功充值1000元，账户余额已更新，感谢您的使用。',
          time: '2023-05-27 15:45',
          read: true
        },
        {
          id: 3,
          type: 'activity',
          title: '618购物节活动',
          content: '618购物节期间全场满减，平台推出多重优惠活动，欢迎参与。',
          time: '2023-05-26 09:20',
          read: false
        },
        {
          id: 4,
          type: 'system',
          title: '账号安全提醒',
          content: '为了保护您的账号安全，建议定期修改密码，开启双重身份验证。',
          time: '2023-05-25 14:10',
          read: true
        },        {
          id: 5,
          type: 'transaction',
          title: '提现申请已受理',
          content: '您的提现申请已受理，预计1-3个工作日到账，请您耐心等待。',
          time: '2023-05-24 16:30',
          read: false
        }
      ],
      showMessageDetail: false,
      currentMessage: {}
    }
  },
  computed: {
    filteredMessages() {
      if (this.activeTab === 'all') {
        return this.messages
      }
      return this.messages.filter(msg => msg.type === this.activeTab)
    }
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    getMessageIcon(type) {
      const icons = {
        system: require('@/assets/images/avatar.jpg'),
        transaction: require('@/assets/images/avatar.jpg'),
        activity: require('@/assets/images/avatar.jpg')
      }
      return icons[type] || icons.system
    },
    readMessage(message) {
      // 标记为已读
      const index = this.messages.findIndex(item => item.id === message.id)
      if (index !== -1) {
        this.messages[index].read = true
      }

      // 显示消息详情
      this.currentMessage = message
      this.showMessageDetail = true
    },
    deleteMessage() {
      this.$dialog.confirm({
        title: '删除消息',
        message: '确定要删除这条消息吗？',
      }).then(() => {
        const index = this.messages.findIndex(item => item.id === this.currentMessage.id)
        if (index !== -1) {
          this.messages.splice(index, 1)
        }
        this.showMessageDetail = false
        this.$toast('删除成功')
      }).catch(() => {
        // 取消删除
      })
    }
  }
}
</script>

<style scoped>
.message {
  min-height: 100vh;
  background-color: #f6f6f6;
}

.header {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  background-color: #fff;
  position: relative;
}

.back-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon img {
  width: 20px;
  height: 20px;
}

.title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

.message-tabs {
  display: flex;
  background-color: #fff;
  padding: 0 16px;
  margin-bottom: 10px;
}

.message-tab {
  flex: 1;
  text-align: center;
  font-size: 14px;
  color: #666;
  padding: 12px 0;
  position: relative;
}

.message-tab.active {
  color: #FF618B;
  font-weight: 500;
}

.message-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 2px;
  background-color: #FF618B;
  border-radius: 1px;
}

.message-list {
  padding: 0 16px;
}

.message-item {
  display: flex;
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 10px;
}

.message-item.unread {
  background-color: #fff9fa;
}

.message-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  overflow: hidden;
  margin-right: 12px;
  position: relative;
}

.message-icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.unread-dot {
  position: absolute;
  top: 0;
  right: 0;
  width: 8px;
  height: 8px;
  background-color: #FF618B;
  border-radius: 4px;
}

.message-content {
  flex: 1;
}

.message-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 5px;
}

.message-preview {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.message-time {
  font-size: 12px;
  color: #999;
}

.empty-state {
  padding: 50px 0;
}

.message-detail {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.detail-header {
  margin-bottom: 20px;
}

.detail-title {
  font-size: 18px;
  font-weight: 500;
  color: #333;
  margin-bottom: 10px;
}

.detail-time {
  font-size: 12px;
  color: #999;
}

.detail-content {
  flex: 1;
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  overflow-y: auto;
}

.detail-actions {
  display: flex;
  margin-top: 20px;
}

.action-btn {
  flex: 1;
  height: 44px;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  margin: 0 10px;
}

.action-btn.delete {
  background-color: #f5f5f5;
  color: #666;
}

.action-btn.close {
  background-color: #FF618B;
  color: #fff;
}
</style>
