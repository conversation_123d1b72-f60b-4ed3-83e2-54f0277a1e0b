<template>
  <div class="change-password">
    <!-- 头部 -->
    <div class="header">
      <div class="back-icon" @click="goBack">
        <img src="@/assets/icons/back-icon.svg" alt="返回">
      </div>
      <div class="title">修改密码</div>
    </div>

    <!-- 表单 -->
    <div class="form-section">
      <!-- 当前密码 -->
      <div class="form-item">
        <div class="item-label">当前密码</div>
        <div class="item-input">
          <input :type="showCurrentPassword ? 'text' : 'password'" v-model="currentPassword" placeholder="请输入当前密码">
          <div class="password-toggle" @click="showCurrentPassword = !showCurrentPassword">
            <van-icon :name="showCurrentPassword ? 'eye-o' : 'closed-eye'" />
          </div>
        </div>
      </div> <!-- 新密码 -->
      <div class="form-item">
        <div class="item-label">新密码</div>
        <div class="item-input">
          <input :type="showNewPassword ? 'text' : 'password'" v-model="newPassword" placeholder="请输入新密码">
          <div class="password-toggle" @click="showNewPassword = !showNewPassword">
            <van-icon :name="showNewPassword ? 'eye-o' : 'closed-eye'" />
          </div>
        </div>
      </div> <!-- 确认新密码 -->
      <div class="form-item">
        <div class="item-label">确认新密码</div>
        <div class="item-input">
          <input :type="showConfirmPassword ? 'text' : 'password'" v-model="confirmPassword" placeholder="请再次输入新密码">
          <div class="password-toggle" @click="showConfirmPassword = !showConfirmPassword">
            <van-icon :name="showConfirmPassword ? 'eye-o' : 'closed-eye'" />
          </div>
        </div>
      </div>
    </div>

    <!-- 密码强度 -->
    <div class="password-strength" v-if="newPassword">
      <div class="strength-label">密码强度：</div>
      <div class="strength-bar">
        <div class="strength-inner" :class="strengthClass" :style="{ width: strengthPercent + '%' }"></div>
      </div>
      <div class="strength-text" :class="strengthClass">{{ strengthText }}</div>
    </div>

    <!-- 底部按钮 -->
    <div class="bottom-btn" @click="changePassword">
      确认修改
    </div>
  </div>
</template>

<script>

export default {
  name: 'ChangePassword',
  data() {
    return {
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
      showCurrentPassword: false,
      showNewPassword: false,
      showConfirmPassword: false
    }
  }, computed: {
    strengthScore() {
      let score = 0

      // 基础要求：长度大于等于8位
      if (this.newPassword.length >= 8) score += 1

      // 必须包含数字
      if (/\d/.test(this.newPassword)) score += 1

      // 必须包含英文字母（大写或小写）
      if (/[a-zA-Z]/.test(this.newPassword)) score += 1

      return score
    }, strengthPercent() {
      return (this.strengthScore / 3) * 100
    },
    strengthClass() {
      if (this.strengthScore < 2) return 'weak'
      if (this.strengthScore === 2) return 'medium'
      return 'strong'
    },
    strengthText() {
      if (this.strengthScore < 2) return '弱'
      if (this.strengthScore === 2) return '中'
      return '强'
    }
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    }, validateForm() {
      if (!this.currentPassword) {
        this.$toast('请输入当前密码')
        return false
      }

      if (!this.newPassword) {
        this.$toast('请输入新密码')
        return false
      }

      if (this.newPassword.length < 8) {
        this.$toast('密码长度至少8位')
        return false
      }

      // 检查是否包含数字
      if (!/\d/.test(this.newPassword)) {
        this.$toast('密码必须包含数字')
        return false
      }

      // 检查是否包含英文字母
      if (!/[a-zA-Z]/.test(this.newPassword)) {
        this.$toast('密码必须包含英文字母')
        return false
      }

      if (!this.confirmPassword) {
        this.$toast('请确认新密码')
        return false
      }

      if (this.newPassword !== this.confirmPassword) {
        this.$toast('两次输入的密码不一致')
        return false
      }

      if (this.currentPassword === this.newPassword) {
        this.$toast('新密码不能与当前密码相同')
        return false
      }

      return true
    }, async changePassword() {
      if (!this.validateForm()) return

      // 检查用户登录状态
      const userToken = localStorage.getItem('user_token')
      if (!userToken) {
        this.$toast('请先登录')
        this.$nextTick(() => { this.$router.replace('/login') })
        return
      }

      this.$toast.loading({
        message: '修改中..',
        forbidClick: true,
      })

      try {
        // 调用密码修改API
        const response = await this.$http.post('/member/doPassword', {
          password: this.currentPassword,
          password_new: this.newPassword,
          password_confirm: this.confirmPassword
        }, {
          headers: {
            'authorization': userToken,
            'Content-Type': 'application/json'
          }
        })

        this.$toast.clear()

        if (response.data.code === 200) {
          this.$toast.success('密码修改成功')
          setTimeout(() => {
            this.$router.go(-1)
          }, 1500)
        } else {
          this.$toast(response.data.msg || '密码修改失败')
        }
      } catch (error) {
        this.$toast.clear()
        console.error('密码修改失败:', error)

        // 处理特定错误状态
        if (error.response && (error.response.status === 401 || error.response.status === 403)) {
          this.$toast('登录已过期，请重新登录')
          localStorage.removeItem('user_token')
          this.$nextTick(() => { this.$router.replace('/login') })
        } else {
          this.$toast('网络异常，请稍后重试')
        }
      }
    }
  }
}
</script>

<style scoped>
.change-password {
  min-height: 100vh;
  background-color: #f6f6f6;
  padding-bottom: 80px;
}

.header {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  background-color: #fff;
  position: relative;
}

.back-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon img {
  width: 20px;
  height: 20px;
}

.title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

.form-section {
  background-color: #fff;
  margin-top: 10px;
}

.form-item {
  display: flex;
  flex-direction: column;
  padding: 15px 16px;
  border-bottom: 1px solid #f5f5f5;
}

.form-item:last-child {
  border-bottom: none;
}

.item-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

.item-input {
  display: flex;
  align-items: center;
}

.item-input input {
  flex: 1;
  border: none;
  outline: none;
  font-size: 16px;
  color: #333;
}

.password-toggle {
  padding: 5px;
  color: #999;
}

.password-strength {
  background-color: #fff;
  padding: 15px 16px;
  margin-top: 10px;
  display: flex;
  align-items: center;
}

.strength-label {
  font-size: 14px;
  color: #666;
  margin-right: 10px;
}

.strength-bar {
  flex: 1;
  height: 6px;
  background-color: #f5f5f5;
  border-radius: 3px;
  overflow: hidden;
  margin-right: 10px;
}

.strength-inner {
  height: 100%;
  border-radius: 3px;
}

.strength-inner.weak {
  background-color: #F5222D;
}

.strength-inner.medium {
  background-color: #FF9500;
}

.strength-inner.strong {
  background-color: #52C41A;
}

.strength-text {
  font-size: 14px;
  font-weight: 500;
}

.strength-text.weak {
  color: #F5222D;
}

.strength-text.medium {
  color: #FF9500;
}

.strength-text.strong {
  color: #52C41A;
}

.bottom-btn {
  position: fixed;
  bottom: 20px;
  left: 16px;
  right: 16px;
  height: 44px;
  background-color: #0474FC;
  color: #fff;
  font-size: 16px;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
