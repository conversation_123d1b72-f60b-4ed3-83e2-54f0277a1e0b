<template>
  <div class="recharge-detail">
    <!-- 头部 -->
    <div class="header">
      <div class="back-icon" @click="goBack">
        <img src="@/assets/icons/back-icon.svg" alt="返回">
      </div>
      <div class="title">充值详情</div>
    </div> <!-- 吸顶容器 -->
    <div class="sticky-container" :class="{ 'is-sticky': isSticky }">
      <!-- 状态筛选-->
      <div class="status-filter">
        <div class="status-item" v-for="(item, index) in statusOptions" :key="index"
          :class="{ active: selectedStatus === item.value }" @click="selectStatus(item.value)">
          {{ item.text }}
        </div>
      </div>
    </div>

    <!-- 占位元素，防止内容跳动 -->
    <div class="sticky-placeholder" v-if="isSticky" :style="{ height: stickyHeight + 'px' }"></div>

    <!-- 详细列表 -->
    <div class="detail-list">
      <div class="detail-item" v-for="(item, index) in records" :key="index">
        <div class="detail-header">
          <div class="detail-title">充值</div>
          <div class="detail-status" :class="getStatusClass(item.status)">{{ getStatusText(item.status) }}</div>
        </div>
        <div class="detail-content">
          <div class="detail-amount">+{{ item.amount }}元</div>
          <div class="detail-time">{{ item.create_time }}</div>
        </div>
        <div class="detail-footer">
          <div class="detail-payment">{{ item.remark || '银行转账' }}</div>
          <div class="detail-order">订单号：{{ item.order_id }}</div>
        </div>
      </div>

      <!-- 加载更多 -->
      <div class="loading-more" v-if="loading">
        <van-loading size="24px" color="#0474FC ">加载中..</van-loading>
      </div>

      <!-- 空状态-->
      <div class="empty-state" v-if="records.length === 0 && !loading">
        <van-empty description="暂无充值记录" />
      </div>

      <!-- 没有更多数据 -->
      <div class="no-more" v-if="!hasMore && records.length > 0">
        没有更多数据了
      </div>
    </div>
  </div>
</template>

<script>

export default {
  name: 'RechargeDetail', data() {
    return {
      page: 1,
      loading: false,
      hasMore: true,
      records: [],
      observerTarget: null,
      selectedStatus: 0,
      statusOptions: [
        { text: '全部', value: 0 },
        { text: '处理中', value: 1 },
        { text: '成功', value: 2 },
        { text: '失败', value: 3 }
      ],
      // 吸顶效果相关数据
      isSticky: false,
      stickyHeight: 0,
      stickyTriggerPosition: 0,
      scrollTimeout: null
    }
  },
  created() {
    this.fetchDepositLog()
  },
  mounted() {
    // 创建一个IntersectionObserver 实例，用于检测列表底部并触发加载更多
    this.observerTarget = document.createElement('div')
    document.querySelector('.detail-list').appendChild(this.observerTarget)

    const observer = new IntersectionObserver(entries => {
      // 当目标元素进入视口时，自动加载更多数据
      if (entries[0].isIntersecting && this.hasMore && !this.loading) {
        this.loadMore()
      }
    }, { threshold: 0.1 })

    observer.observe(this.observerTarget)

    // 记录观察器以便在组件销毁时断开连接
    this.observer = observer

    // 初始化吸顶相关设置
    this.initStickyElements()
    this.setupScrollListener()
    this.setupResizeListener()
  },
  beforeDestroy() {
    // 清理 IntersectionObserver
    if (this.observer && this.observerTarget) {
      this.observer.unobserve(this.observerTarget)
      this.observer.disconnect()
    }

    // 清理滚动和调整大小监听
    this.cleanupScrollListener()
    this.cleanupResizeListener()
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    getStatusText(status) {
      // 映射状态码：1=处理中，2=成功，3=失败
      const statusMap = {
        1: '处理中',
        2: '成功',
        3: '失败'
      }
      return statusMap[status] || '未知'
    },
    getStatusClass(status) {
      // 映射状态码对应的CSS类 1=processing, 2=success, 3=failed
      const classMap = {
        1: 'processing',
        2: 'success',
        3: 'failed'
      }
      return classMap[status] || ''
    },
    async fetchDepositLog() {
      try {
        this.loading = true
        const response = await this.$http.get(`/finance/getDepositLog?page=${this.page}&status=${this.selectedStatus}`, {
          headers: {
            'authorization': localStorage.getItem('user_token') || ''
          }
        })
        if (response.data.code === 200) {
          const data = response.data.data

          // 添加新数据到列表
          if (data && data.list && Array.isArray(data.list)) {
            this.records = [...this.records, ...data.list]

            // 判断是否还有更多数据
            if (data.list.length < 10 || !data.list.length) {
              this.hasMore = false
            }
          } else {
            console.error('API返回数据格式不正确', data)
            this.hasMore = false
          }
        } else {
          this.$toast(response.data.msg || '获取数据失败')
        }
      } catch (error) {
        console.error('获取充值记录失败', error)
        this.$toast('获取数据失败，请稍后再试')
      } finally {
        this.loading = false
      }
    },
    loadMore() {
      if (!this.loading && this.hasMore) {
        this.page += 1
        this.fetchDepositLog()
      }
    },

    selectStatus(status) {
      if (this.selectedStatus === status) return

      // 更新选中状态
      this.selectedStatus = status

      // 重置分页和数据
      this.page = 1
      this.records = []
      this.hasMore = true

      // 重新加载数据
      this.fetchDepositLog()
    },

    // 初始化吸顶元素
    initStickyElements() {
      this.$nextTick(() => {
        const stickyContainer = document.querySelector('.sticky-container')
        if (stickyContainer) {
          // 获取头部高度
          const header = document.querySelector('.header')
          const headerHeight = header ? header.offsetHeight : 0

          // 计算触发吸顶的位置，添加缓冲使切换更平滑
          this.stickyTriggerPosition = stickyContainer.offsetTop - headerHeight - 5

          // 保存吸顶容器的高度，用于设置占位元素高度
          this.stickyHeight = stickyContainer.offsetHeight + 5
        }
      })
    },

    // 设置滚动监听
    setupScrollListener() {
      window.addEventListener('scroll', this.handleScrollSticky, { passive: true })
    },

    // 清理滚动监听
    cleanupScrollListener() {
      window.removeEventListener('scroll', this.handleScrollSticky)
      if (this.scrollTimeout) {
        clearTimeout(this.scrollTimeout)
      }
    },

    // 设置窗口大小调整的监听
    setupResizeListener() {
      window.addEventListener('resize', this.handleResize, { passive: true })
    },

    // 清理窗口大小调整的监听
    cleanupResizeListener() {
      window.removeEventListener('resize', this.handleResize)
    },

    // 处理窗口大小变化
    handleResize() {
      // 重新计算吸顶元素的高度和位置
      this.initStickyElements()
    },

    // 处理滚动吸顶效果
    handleScrollSticky() {
      if (this.scrollTimeout) clearTimeout(this.scrollTimeout)

      this.scrollTimeout = setTimeout(() => {
        const scrollTop = document.documentElement.scrollTop || document.body.scrollTop

        // 处理吸顶效果
        this.isSticky = scrollTop > this.stickyTriggerPosition
      }, 100) // 减小节流时间，使吸顶效果更流畅
    }
  }
}
</script>

<style scoped>
.recharge-detail {
  min-height: 100vh;
  background-color: #f6f6f6;
}

.header {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  background-color: #fff;
  position: sticky;
  top: 0;
  z-index: 101;
  /* 头部层级要高于吸顶容器 */
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.back-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon img {
  width: 20px;
  height: 20px;
}

.title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

/* 吸顶容器样式 */
.sticky-container {
  position: relative;
  z-index: 99;
  background-color: #f6f6f6;
  transition: all 0.3s ease;
}

.sticky-container.is-sticky {
  position: fixed;
  top: 44px;
  /* 头部高度，根据实际情况调整 */
  left: 0;
  right: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  z-index: 100;
}

/* 吸顶状态下的状态筛选样式调整 */
.sticky-container.is-sticky .status-filter {
  padding: 8px 16px;
  margin-bottom: 0;
}

/* 吸顶容器的占位元素 */
.sticky-placeholder {
  width: 100%;
  transition: height 0.3s ease;
}

.status-filter {
  display: flex;
  background-color: #fff;
  padding: 10px 16px;
  margin-bottom: 10px;
  overflow-x: auto;
}

.status-item {
  min-width: 70px;
  height: 30px;
  background-color: #f5f5f5;
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #666;
  margin-right: 10px;
  padding: 0 15px;
}

.status-item.active {
  background-color: #0474FC;
  color: #fff;
}

.detail-list {
  padding: 0 16px;
  padding-bottom: 50px;
}

.detail-item {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 10px;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.detail-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.detail-status {
  font-size: 14px;
}

.detail-status.success {
  color: #52C41A;
}

.detail-status.processing {
  color: #FF9500;
}

.detail-status.failed {
  color: #F5222D;
}

.detail-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.detail-amount {
  font-size: 18px;
  font-weight: 500;
  color: #0474FC;
}

.detail-time {
  font-size: 12px;
  color: #999;
}

.detail-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-payment {
  font-size: 14px;
  color: #666;
}

.detail-order {
  font-size: 12px;
  color: #999;
}

.empty-state {
  padding: 50px 0;
}

.loading-more {
  text-align: center;
  padding: 15px 0;
}

.no-more {
  text-align: center;
  color: #999;
  font-size: 12px;
  padding: 15px 0;
}
</style>
