<template>
  <div class="settings">
    <!-- 头部 -->
    <div class="header">
      <div class="back-icon" @click="goBack">
        <img src="@/assets/icons/back-icon.svg" alt="返回">
      </div>
      <div class="title">设置</div>
    </div>

    <!-- 设置列表 -->
    <div class="settings-list">
      <div class="settings-group">
        <div class="settings-item" @click="goToProfile">
          <div class="settings-icon">
            <van-icon name="contact" color="#0474FC" size="24" />
          </div>
          <div class="settings-content">
            <div class="settings-title">个人资料</div>
            <van-icon name="arrow" color="#999" />
          </div>
        </div>
      </div>
      <div class="settings-group">
        <div class="settings-item" @click="goToCustomerService">
          <div class="settings-icon">
            <van-icon name="service-o" color="#0474FC" size="24" />
          </div>
          <div class="settings-content">
            <div class="settings-title">客服中心</div>
            <van-icon name="arrow" color="#999" />
          </div>
        </div>

        <div class="settings-item" @click="goToAbout">
          <div class="settings-icon">
            <van-icon name="info-o" color="#0474FC" size="24" />
          </div>
          <div class="settings-content">
            <div class="settings-title">关于我们</div>
            <van-icon name="arrow" color="#999" />
          </div>
        </div>
      </div>
    </div>

    <!-- 退出登录按钮 -->
    <div class="logout-section">
      <div class="logout-btn" @click="confirmLogout">
        退出登录
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Settings', data() {
    return {
    }
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    }, goToProfile() {
      this.$router.push('/profile')
    },
    goToCustomerService() {
      this.$router.push('/customer-service')
    },
    goToAbout() {
      const userToken = localStorage.getItem('user_token')

      if (!userToken) {
        this.redirectToLogin()
        return
      }

      // 调用API获取新手指南URL
      this.$http.get('/member/getPage', {
        params: {
          name: 'about1'
        },
        headers: {
          'authorization': userToken
        }
      })
        .then(response => {
          if (response.data.code === 200) {
            const guideUrl = response.data.data

            if (guideUrl && typeof guideUrl === 'string') {
              this.$router.push({
                path: '/guide',
                query: {
                  url: guideUrl
                }
              })
            } else {
              this.$toast('获取关于我们内容失败')
            }
          } else {
            this.$toast(response.data.msg || '获取关于我们失败')
          }
        })
        .catch(error => {
          console.error('获取关于我们失败:', error)
          this.$toast('网络异常，请稍后重试')
        })
    },
    confirmLogout() {
      this.$dialog.confirm({
        title: '退出确认',
        message: '确定要退出登录吗？',
      }).then(() => {
        this.logout()
      }).catch(() => {
        // 取消操作
      })
    }, logout() {
      // 清除登录状态
      localStorage.removeItem('user_token')  // 修正：应该是 user_token
      localStorage.removeItem('user_info')   // 修正：应该是 user_info

      this.$toast('已退出登录')
      setTimeout(() => {
        this.$router.replace('/login')
      }, 1000)
    }
  }
}
</script>

<style scoped>
.settings {
  min-height: 100vh;
  background-color: #f6f6f6;
}

.header {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  background-color: #fff;
  position: relative;
}

.back-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon img {
  width: 20px;
  height: 20px;
}

.title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

.settings-list {
  margin-top: 10px;
}

.settings-group {
  margin-bottom: 10px;
  background-color: #fff;
}

.settings-item {
  display: flex;
  align-items: center;
  padding: 16px;
  position: relative;
}

.settings-item::after {
  content: '';
  position: absolute;
  left: 16px;
  right: 16px;
  bottom: 0;
  height: 1px;
  background-color: #f5f5f5;
}

.settings-group .settings-item:last-child::after {
  display: none;
}

.settings-icon {
  width: 24px;
  height: 24px;
  margin-right: 16px;
}

.settings-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.settings-title {
  font-size: 16px;
  color: #333;
}

.settings-value {
  font-size: 14px;
  color: #999;
  margin-right: 10px;
}

.logout-section {
  margin: 20px 16px;
}

.logout-btn {
  height: 44px;
  background-color: #fff;
  color: #0474FC;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
}
</style>
