/* 全局样式重置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  -webkit-tap-highlight-color: transparent;
}

html {
  font-size: 16px;
  -webkit-text-size-adjust: 100%;
}

body {
  margin: 0;
  padding: 0;
  background: #f6f6f6;
  color: #333;
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
}

/* 移动端常用布局辅助类 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-between {
  justify-content: space-between;
}

.flex-around {
  justify-content: space-around;
}

.flex-center {
  justify-content: center;
}

.items-center {
  align-items: center;
}

.flex-1 {
  flex: 1;
}

/* 字体相关 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-gray {
  color: #999;
}

.text-primary {
  color: #EA555F;
}

.text-sm {
  font-size: 12px;
}

.text-md {
  font-size: 14px;
}

.text-lg {
  font-size: 16px;
}

.text-xl {
  font-size: 18px;
}

.text-bold {
  font-weight: 500;
}

/* 间距相关 */
.p-sm {
  padding: 5px;
}

.p-md {
  padding: 10px;
}

.p-lg {
  padding: 15px;
}

.m-sm {
  margin: 5px;
}

.m-md {
  margin: 10px;
}

.m-lg {
  margin: 15px;
}

.mt-md {
  margin-top: 10px;
}

.mb-md {
  margin-bottom: 10px;
}

/* 圆角相关 */
.rounded-sm {
  border-radius: 4px;
}

.rounded-md {
  border-radius: 8px;
}

.rounded-lg {
  border-radius: 16px;
}

/* 超出文本显示省略号 */
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.ellipsis-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 安全区适配 */
.safe-bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

/* 阴影效果 */
.shadow-sm {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.shadow-md {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
} 