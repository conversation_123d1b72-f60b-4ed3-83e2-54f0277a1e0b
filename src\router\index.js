import Vue from 'vue'
import Router from 'vue-router'
import Home from '../views/Home.vue'

Vue.use(Router)

export default new Router({
  routes: [
    {
      path: '/',
      name: 'Home',
      component: Home,
      meta: {
        title: '首页'
      }
    },
    {
      path: '/mall',
      name: 'Mall',
      component: () => import('../views/Mall.vue'),
      meta: {
        title: '商城'
      }
    },
    {
      path: '/power',
      name: 'Power',
      component: () => import('../views/Power.vue'),
      meta: {
        title: '善之力'
      }
    },
    {
      path: '/live',
      name: 'Live',
      component: () => import('../views/Live.vue'),
      meta: {
        title: '直播间'
      }
    }, {
      path: '/mine',
      name: 'Mine',
      component: () => import('../views/Mine.vue'),
      meta: {
        title: '我的'
      }
    },
    {
      path: '/express',
      name: 'Express',
      component: () => import('../views/Express.vue'),
      meta: {
        title: '快递查询'
      }
    },
    {
      path: '/news',
      name: 'News',
      component: () => import('../views/News.vue'),
      meta: {
        title: '新闻公告'
      }
    },
    {
      path: '/member-level',
      name: 'MemberLevel',
      component: () => import('../views/MemberLevel.vue'),
      meta: {
        title: '会员等级'
      }
    },
    {
      path: '/points-mall',
      name: 'PointsMall',
      component: () => import('../views/PointsMall.vue'),
      meta: {
        title: '积分商城'
      }
    },
    {
      path: '/product-detail',
      name: 'ProductDetail',
      component: () => import('../views/ProductDetail.vue'),
      meta: {
        title: '商品详情'
      }
    },
    {
      path: '/login',
      name: 'Login',
      component: () => import('../views/Login.vue'),
      meta: {
        title: '登录'
      }
    },
    {
      path: '/register',
      name: 'Register',
      component: () => import('../views/Register.vue'),
      meta: {
        title: '注册'
      }
    },
    {
      path: '/forgot-password',
      name: 'ForgotPassword',
      component: () => import('../views/ForgotPassword.vue'),
      meta: {
        title: '忘记密码'
      }
    }, {
      path: '/project-detail',
      name: 'ProjectDetail',
      component: () => import('../views/ProjectDetail.vue'),
      meta: {
        title: '项目详情'
      }
    },
    {
      path: '/project-detail-ok',
      name: 'ProjectDetailOk',
      component: () => import('../views/ProjectDetailOk.vue'),
      meta: {
        title: '订单确认'
      }
    },
    {
      path: '/team',
      name: 'Team',
      component: () => import('../views/Team.vue'),
      meta: {
        title: '我的团队'
      }
    },
    {
      path: '/order',
      name: 'Order',
      component: () => import('../views/Order.vue'),
      meta: {
        title: '我的订单'
      }
    }, {
      path: '/project-order',
      name: 'ProjectOrder',
      component: () => import('../views/ProjectOrder.vue'),
      meta: {
        title: '认购订单'
      }
    },
    {
      path: '/project-order-detail',
      name: 'ProjectOrderDetail',
      component: () => import('../views/ProjectOrderDetail.vue'),
      meta: {
        title: '订单详情'
      }
    },
    {
      path: '/product-contract',
      name: 'ProductContract',
      component: () => import('../views/ProductContract.vue'),
      meta: {
        title: '电子合同'
      }
    },
    {
      path: '/settings',
      name: 'Settings',
      component: () => import('../views/Settings.vue'),
      meta: {
        title: '设置'
      }
    },
    {
      path: '/customer-service',
      name: 'CustomerService',
      component: () => import('../views/CustomerService.vue'),
      meta: {
        title: '客服中心'
      }
    },
    {
      path: '/wallet',
      name: 'Wallet',
      component: () => import('../views/Wallet.vue'),
      meta: {
        title: '我的钱包'
      }
    },
    {
      path: '/recharge',
      name: 'Recharge',
      component: () => import('../views/Recharge.vue'),
      meta: {
        title: '充值'
      }
    }, {
      path: '/recharge-submit',
      name: 'RechargeSubmit',
      component: () => import('../views/RechargeSubmit.vue'),
      meta: {
        title: '充值提交'
      }
    }, {
      path: '/recharge-detail',
      name: 'RechargeDetail',
      component: () => import('../views/RechargeDetail.vue'),
      meta: {
        title: '充值明细'
      }
    },
    {
      path: '/integral-detail',
      name: 'IntegralDetail',
      component: () => import('../views/IntegralDetail.vue'),
      meta: {
        title: '积分明细'
      }
    },
    {
      path: '/recharge-rating',
      name: 'RechargeRating',
      component: () => import('../views/RechargeRating.vue'),
      meta: {
        title: '充值评级'
      }
    },
    {
      path: '/withdraw',
      name: 'Withdraw',
      component: () => import('../views/Withdraw.vue'),
      meta: {
        title: '提现'
      }
    },
    {
      path: '/withdraw-detail',
      name: 'WithdrawDetail',
      component: () => import('../views/WithdrawDetail.vue'),
      meta: {
        title: '提现明细'
      }
    }, {
      path: '/transfer',
      name: 'Transfer',
      component: () => import('../views/Transfer.vue'),
      meta: {
        title: '转账'
      }
    },
    {
      path: '/transfer-detail',
      name: 'TransferDetail',
      component: () => import('../views/TransferDetail.vue'),
      meta: {
        title: '转账详细'
      }
    },
    {
      path: '/invite',
      name: 'Invite',
      component: () => import('../views/Invite.vue'),
      meta: {
        title: '邀请返利'
      }
    },
    {
      path: '/transaction-record',
      name: 'TransactionRecord',
      component: () => import('../views/TransactionRecord.vue'),
      meta: {
        title: '交易记录'
      }
    },
    {
      path: '/message',
      name: 'Message',
      component: () => import('../views/Message.vue'),
      meta: {
        title: '消息管理'
      }
    },
    {
      path: '/profile',
      name: 'Profile',
      component: () => import('../views/Profile.vue'),
      meta: {
        title: '基本信息'
      }
    },
    {
      path: '/qualification',
      name: 'Qualification',
      component: () => import('../views/Qualification.vue'),
      meta: {
        title: '平台资质'
      }
    },
    {
      path: '/bank-card',
      name: 'BankCard',
      component: () => import('../views/BankCard.vue'),
      meta: {
        title: '银行卡管理'
      }
    }, {
      path: '/bank-card/add',
      name: 'AddBankCard',
      component: () => import('../views/AddBankCard.vue'),
      meta: {
        title: '添加银行卡'
      }
    },
    {
      path: '/bank-card/bind',
      name: 'BindBankCard',
      component: () => import('../views/BindBankCard.vue'),
      meta: {
        title: '银行卡信息'
      }
    },
    {
      path: '/verification',
      name: 'Verification',
      component: () => import('../views/Verification.vue'),
      meta: {
        title: '实名认证'
      }
    },
    {
      path: '/income',
      name: 'Income',
      component: () => import('../views/Income.vue'),
      meta: {
        title: '我的收益'
      }
    },
    {
      path: '/favorite',
      name: 'Favorite',
      component: () => import('../views/Favorite.vue'),
      meta: {
        title: '我的收藏'
      }
    }, {
      path: '/change-password',
      name: 'ChangePassword',
      component: () => import('../views/ChangePassword.vue'),
      meta: {
        title: '修改密码'
      }
    },
    {
      path: '/change-password-finance',
      name: 'ChangePasswordFinance',
      component: () => import('../views/ChangePasswordFinance.vue'),
      meta: {
        title: '修改资金密码'
      }
    },
    {
      path: '/address',
      name: 'Address',
      component: () => import('../views/Address.vue'),
      meta: {
        title: '收货地址'
      }
    },
    {
      path: '/address/add',
      name: 'AddAddress',
      component: () => import('../views/AddAddress.vue'),
      meta: {
        title: '新增地址'
      }
    },
    {
      path: '/address/edit/:id',
      name: 'EditAddress',
      component: () => import('../views/AddAddress.vue'),
      meta: {
        title: '编辑地址'
      }
    },
    {
      path: '/guide',
      name: 'Guide',
      component: () => import('../views/Guide.vue'),
      meta: {
        title: '新手攻略'
      }
    }, {
      path: '/sign-in',
      name: 'SignIn',
      component: () => import('../views/SignIn.vue'),
      meta: {
        title: '签到抽奖'
      }
    },
    {
      path: '/sign-in-calendar',
      name: 'SignInCalendar',
      component: () => import('../views/SignInCalendar.vue'),
      meta: {
        title: '签到日历'
      }
    },
    {
      path: '*',
      redirect: '/'
    }
  ]
})