<template>
  <div class="tab-bar">
    <div class="tab-bar-inner">
      <!-- 首页 -->
      <a href="#home" class="tab-item" :class="{ active: active === 0 }" @click="onTabClick(0, '/', $event)">
        <div class="tab-icon">
          <img src="@/assets/icons/home-icon.svg" alt="首页" />
        </div>
        <div class="tab-text" :class="{ 'active-text': active === 0 }">首页</div>
      </a>

      <!-- 商城 -->
      <a href="#mall" class="tab-item" :class="{ active: active === 1 }" @click="onTabClick(1, '/points-mall', $event)">
        <div class="tab-icon">
          <img src="@/assets/icons/cart-icon.svg" alt="商城" />
        </div>
        <div class="tab-text" :class="{ 'active-text': active === 1 }">商城</div>
      </a>

      <!-- 善之力 -->
      <a href="#power" class="tab-item shanzhili-tab" @click="onTabClick(2, '/power', $event)">
        <div class="tab-icon shanzhili-icon">
          <div class="shanzhili-bg"></div>
          <img src="@/assets/icons/like-icon.svg" alt="善之力" />
        </div>
        <div class="tab-text shanzhili-text">善之力</div>
      </a>

      <!-- 直播间 -->
      <a href="#live" class="tab-item" :class="{ active: active === 3 }" @click="onTabClick(3, '/live', $event)">
        <div class="tab-icon">
          <img src="@/assets/icons/live-icon.svg" alt="直播间" />
        </div>
        <div class="tab-text" :class="{ 'active-text': active === 3 }">直播间</div>
      </a>

      <!-- 我的 -->
      <a href="#mine" class="tab-item" :class="{ active: active === 4 }" @click="onTabClick(4, '/mine', $event)">
        <div class="tab-icon">
          <img src="@/assets/icons/user-icon.svg" alt="我的" />
        </div>
        <div class="tab-text" :class="{ 'active-text': active === 4 }">我的</div>
      </a>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TabBar',
  props: {
    active: {
      type: Number,
      default: 0
    }
  },
  methods: {
    onTabClick(index, path, event) {
      // 阻止默认的锚点跳转行为
      event.preventDefault();

      if (this.active !== index) {
        this.$router.push(path);
        this.$emit('change', index);
      }
    }
  }
}
</script>

<style scoped>
.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 80px;
  z-index: 100;
  overflow: visible;
}

.tab-bar-inner {
  position: relative;
  display: flex;
  align-items: center;
  height: 100%;
  background-image: url('@/assets/images/tab-bg.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  padding-bottom: env(safe-area-inset-bottom);
  /* 如果背景图片加载失败，使用白色背景 */
  background-color: #ffffff;
  box-shadow: 0 -1px 5px rgba(0, 0, 0, 0.08);
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  cursor: pointer;
  position: relative;
  text-decoration: none;
  color: inherit;
  /* 移除链接默认样式 */
  outline: none;
  -webkit-tap-highlight-color: transparent;
  /* 移除移动端点击高亮 */
}

.tab-icon {
  height: 24px;
  margin-bottom: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tab-icon img {
  width: 24px;
  height: 24px;
}

.tab-text {
  font-size: 12px;
  color: #999999;
  line-height: 1.2;
}

.active-text {
  color: #3575FF;
  font-weight: 500;
}

/* 善之力特殊样式 */
.shanzhili-tab {
  position: relative;
  z-index: 10;
}

.shanzhili-icon {
  position: relative;
  transform: translateY(-20px);
  /* 向上浮起更多 */
  z-index: 10;
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.shanzhili-bg {
  position: absolute;
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, #4A90E2 0%, #3575FF 100%);
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  box-shadow:
    0 8px 20px rgba(53, 117, 255, 0.3),
    0 4px 12px rgba(53, 117, 255, 0.2),
    0 0 0 3px rgba(255, 255, 255, 0.8);
  z-index: -1;
}

.shanzhili-icon img {
  position: relative;
  z-index: 1;
  width: 28px;
  height: 28px;
  filter: brightness(0) invert(1);
  /* 将图标变为白色 */
}

.shanzhili-text {
  color: #3575FF !important;
  font-weight: 600;
  font-size: 12px;
  transform: translateY(-8px);
  /* 文字也稍微向上移动 */
}

/* 为其他tab项添加hover效果 */
.tab-item:not(.shanzhili-tab):hover .tab-icon img {
  transform: scale(1.1);
  transition: transform 0.2s ease;
}

.tab-item:not(.shanzhili-tab):hover .tab-text {
  color: #3575FF;
  transition: color 0.2s ease;
}

/* 善之力按钮的hover效果 */
.shanzhili-tab:hover .shanzhili-bg {
  transform: translate(-50%, -50%) scale(1.05);
  transition: transform 0.2s ease;
}

.shanzhili-tab:hover .shanzhili-icon img {
  transform: scale(1.1);
  transition: transform 0.2s ease;
}
</style>