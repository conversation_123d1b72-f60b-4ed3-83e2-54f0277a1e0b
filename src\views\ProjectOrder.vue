<template>
  <div class="project-order">
    <!-- 头部导航 -->
    <div class="header">
      <div class="back-icon" @click="goBack">
        <img src="@/assets/icons/back-icon.svg" @error="handleImageError($event, 'icon')" alt="返回">
      </div>
      <div class="title">认购订单</div>
      <div class="right-placeholder"></div>
    </div>

    <!-- 标签页导航 - 吸顶容器 -->
    <div class="sticky-container" :class="{ 'is-sticky': isSticky }">
      <div class="tab-nav">
        <div class="tab-item" :class="{ active: activeType === 0 }" @click="switchType(0)">
          全部
        </div>
        <div class="tab-item" :class="{ active: activeType === 1 }" @click="switchType(1)">
          进行中
        </div>
        <div class="tab-item" :class="{ active: activeType === 2 }" @click="switchType(2)">
          已结束
        </div>
      </div>
    </div>

    <!-- 占位元素，防止内容跳动 -->
    <div class="sticky-placeholder" v-if="isSticky" :style="{ height: stickyHeight + 'px' }"></div>
    <!-- 订单列表 -->
    <div class="order-list" v-if="orders.length > 0">
      <div class="order-item" v-for="order in orders" :key="order.id">
        <div class="order-card">
          <!-- 项目信息 -->
          <div class="project-info">
            <div class="project-title">{{ order.product_name }}</div>
            <div class="order-number">订单号：{{ order.order_id }}</div>
            <div class="project-status" :class="getStatusClass(order.status)">
              {{ getStatusText(order.status) }}
            </div>
          </div>

          <!-- 分割线 -->
          <div class="divider"></div>

          <!-- 金额信息 -->
          <div class="amount-info">
            <div class="amount-col">
              <div class="amount-label">订单金额</div>
              <div class="amount-value">{{ order.amount }}</div>
            </div>
            <div class="amount-col">
              <div class="amount-label">{{ getRateLabel(order) }}</div>
              <div class="amount-value">{{ order.daily_earnings || '12.00' }}</div>
            </div>
          </div>

          <!-- 按钮区域 -->
          <div class="button-group">
            <!-- 电子合同按钮 -->
            <div class="action-button contract-btn" @click="viewContract(order)">
              电子合同
            </div>
            <!-- 查看订单按钮 -->
            <div class="action-button order-btn" @click="viewOrderDetail(order)">
              查看订单
            </div>
          </div>
        </div>
      </div>
      <!-- 加载状态 -->
      <div class="loading-state" v-if="loading">
        <div class="loading-spinner"></div>
        <div class="loading-text">加载中...</div>
      </div>

      <!-- 没有更多数据 -->
      <div class="no-more" v-if="finished && !loading && orders.length > 0">
        没有更多了~
      </div>
    </div>

    <!-- 空状态 -->
    <div class="empty-state" v-else-if="!loading">
      <div class="empty-icon">📝</div>
      <div class="empty-text">暂无认购订单</div>
    </div>
  </div>
</template>

<script>
import ImageErrorHandler from '@/utils/ImageErrorHandler'

export default {
  name: 'ProjectOrder',
  // 路由导航守卫 - 进入页面前检查登录状态
  beforeRouteEnter(to, from, next) {
    const userToken = localStorage.getItem('user_token')
    if (!userToken) {
      // 没有token，跳转到登录页
      next('/login')
    } else {
      next()
    }
  }, data() {
    return {
      activeType: 0, // 0：全部，1：进行中，2：已结束
      page: 1,
      loading: false,
      finished: false,
      orders: [],
      tabWidth: 0,      // 吸顶效果相关
      isSticky: false, // 是否处于吸顶状态
      stickyHeight: 0, // 吸顶元素高度
      stickyTriggerPosition: 0, // 触发吸顶的位置
      scrollTimeout: null, // 滚动节流定时器
      resizeTimeout: null // 窗口大小变化防抖定时器
    }
  },
  computed: {
    // 激活指示器样式
    indicatorStyle() {
      const tabWidth = this.tabWidth || (window.innerWidth / 3)
      return {
        transform: `translateX(${this.activeType * tabWidth}px)`,
        width: `${tabWidth}px`
      }
    }
  }, mounted() {
    this.initTabWidth()
    this.loadOrderData()

    // 初始化吸顶元素（页面加载完成后立即执行）
    this.initStickyElements()

    // 监听滚动事件，实现触底加载更多和吸顶效果
    this.setupScrollListener()

    // 监听窗口大小变化
    window.addEventListener('resize', this.handleResize)

    // 手动触发一次滚动处理，确保初始状态正确
    this.$nextTick(() => {
      this.handleScroll()
    })
  }, beforeDestroy() {
    // 移除事件监听
    this.cleanupScrollListener()
    window.removeEventListener('resize', this.handleResize)

    // 清理定时器
    if (this.resizeTimeout) {
      clearTimeout(this.resizeTimeout)
    }
  },
  methods: {
    // 处理图片加载错误
    handleImageError(event, type) {
      ImageErrorHandler.handleImageError(event, type)
    },

    // 返回上一页
    goBack() {
      this.$router.go(-1)
    },

    // 初始化标签页宽度
    initTabWidth() {
      this.$nextTick(() => {
        const tabItems = document.querySelectorAll('.tab-item')
        if (tabItems && tabItems.length > 0) {
          this.tabWidth = tabItems[0].offsetWidth
        }
      })
    },    // 切换标签页
    switchType(type) {
      if (this.activeType !== type) {
        this.activeType = type
        this.page = 1
        this.orders = []
        this.finished = false
        this.loadOrderData()

        // 切换标签后重新初始化吸顶元素，确保吸顶效果正常
        this.$nextTick(() => {
          this.initStickyElements()
          this.handleScroll() // 立即更新吸顶状态
        })
      }
    },

    // 加载订单数据
    loadOrderData() {
      if (this.loading || this.finished) return

      this.loading = true

      const userToken = localStorage.getItem('user_token')
      if (!userToken) {
        this.$toast('请先登录')
        this.$router.push('/login')
        return
      }
      // 请求订单列表数据
      this.$http.get('/order/getProductList', {
        params: {
          type: this.activeType, // 0为全部，1为进行中，2为已结束
          page: this.page
        },
        headers: {
          'authorization': userToken
        }
      }).then(response => {
        if (response.data.code === 200) {
          const data = response.data.data

          // 处理订单列表数据
          if (data.list && data.list.length > 0) {
            // 将新数据添加到列表
            this.orders = [...this.orders, ...data.list]
            this.page++

            // 如果是首次加载数据，重新计算吸顶元素
            if (this.page === 2) {
              this.$nextTick(() => {
                this.initStickyElements()
              })
            }
          } else {
            // 如果当前页没有数据，则标记为加载完成
            this.finished = true
          }
        } else {
          this.$toast(response.data.msg || '获取订单列表失败')
          this.finished = true
        }

        this.loading = false
      })
        .catch(error => {
          this.loading = false
          this.finished = true

          // 检查是否是认证错误
          if (error.response) {
            const status = error.response.status
            if (status === 401 || status === 403) {
              // token过期或无效，清除本地存储并跳转到登录页
              localStorage.removeItem('user_token')
              localStorage.removeItem('user_info')
              this.$toast('登录已过期，请重新登录')
              this.$router.replace('/login')
              return
            }
          }

          console.error('获取订单列表失败:', error)
          this.$toast('网络异常，请稍后重试')
        })
    },    // 初始化吸顶元素
    initStickyElements() {
      this.$nextTick(() => {
        const stickyContainer = document.querySelector('.sticky-container')
        if (stickyContainer) {
          // 获取头部高度
          const header = document.querySelector('.header')
          const headerHeight = header ? header.offsetHeight : 0

          // 计算触发吸顶的位置（添加10px的缓冲，使切换更平滑）
          this.stickyTriggerPosition = stickyContainer.offsetTop - headerHeight - 10

          // 保存吸顶容器的高度，用于设置占位元素高度
          this.stickyHeight = stickyContainer.offsetHeight + 5

          // 根据当前滚动位置初始化吸顶状态
          const scrollTop = document.documentElement.scrollTop || document.body.scrollTop
          this.isSticky = scrollTop > this.stickyTriggerPosition

          // 调试日志，帮助验证吸顶逻辑
          if (process.env.NODE_ENV === 'development') {
            console.log('初始化吸顶元素:', {
              containerTop: stickyContainer.offsetTop,
              headerHeight,
              triggerPosition: this.stickyTriggerPosition,
              scrollTop,
              isSticky: this.isSticky
            })
          }
        }
      })
    },

    // 设置滚动监听
    setupScrollListener() {
      window.addEventListener('scroll', this.handleScroll, { passive: true })
    },

    // 清理滚动监听
    cleanupScrollListener() {
      window.removeEventListener('scroll', this.handleScroll)
      if (this.scrollTimeout) {
        clearTimeout(this.scrollTimeout)
      }
    },    // 处理窗口大小变化
    handleResize() {
      // 清除之前的resize定时器（如果有）
      if (this.resizeTimeout) {
        clearTimeout(this.resizeTimeout)
      }

      // 使用定时器防抖，提高性能
      this.resizeTimeout = setTimeout(() => {
        this.$nextTick(() => {
          this.initStickyElements()
          this.initTabWidth()
          this.handleScroll() // 立即更新吸顶状态
        })
      }, 200)
    },    // 处理滚动事件（吸顶效果和加载更多）
    handleScroll() {
      if (this.scrollTimeout) clearTimeout(this.scrollTimeout);

      this.scrollTimeout = setTimeout(() => {
        const scrollTop = document.documentElement.scrollTop || document.body.scrollTop
        const scrollHeight = document.documentElement.scrollHeight || document.body.scrollHeight
        const clientHeight = document.documentElement.clientHeight || window.innerHeight

        // 处理吸顶效果 - 使用单行简洁写法，确保在滚回顶部时能正确恢复
        const shouldSticky = scrollTop > this.stickyTriggerPosition;

        // 只有状态变化时才更新，减少不必要的渲染
        if (this.isSticky !== shouldSticky) {
          this.isSticky = shouldSticky;

          // 调试日志，帮助验证吸顶逻辑
          if (process.env.NODE_ENV === 'development') {
            console.log('滚动状态变化:', {
              scrollTop,
              triggerPosition: this.stickyTriggerPosition,
              isSticky: this.isSticky
            })
          }
        }

        // 触底加载更多，距离底部100px时开始加载下一页
        if (scrollTop + clientHeight >= scrollHeight - 100 && !this.loading && !this.finished) {
          this.loadOrderData()
        }
      }, 50) // 减小节流时间，使吸顶效果更流畅
    },
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        1: '项目进行中',
        2: '项目已结束'
      }
      return statusMap[status] || '未知状态'
    },

    // 获取状态类名
    getStatusClass(status) {
      return status === 1 ? 'status-active' : 'status-finished'
    },

    // 获取收益率标签
    getRateLabel(order) {
      // 简化为统一使用"每日收益"
      return '每日收益'
    },

    // 获取收益率值
    getRateValue(order) {
      // 展示收益率值加百分号
      return order.rate + '%'
    },

    // 获取总收益
    getEarnings(order) {
      return order.earnings || '0.00'
    },

    // 获取项目周期
    getProjectCycle(order) {
      return order.cycle || '未知'
    },

    // 查看电子合同
    viewContract(order) {
      this.$router.push(`/product-contract?id=${order.id}`)
    },

    // 查看订单详情
    viewOrderDetail(order) {
      this.$router.push(`/project-order-detail?id=${order.id}`)
    }
  }
}
</script>

<style scoped>
.project-order {
  min-height: 100vh;
  background-color: #F8F9FA;
  padding-bottom: 20px;
}

/* 头部导航 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  height: 44px;
  background-color: #fff;
  position: sticky;
  top: 0;
  z-index: 101;
  /* 头部层级要高于吸顶容器 */
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.back-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.back-icon img {
  width: 10px;
  height: 16px;
}

.title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 17px;
  font-weight: 500;
  color: #111827;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
}

.right-placeholder {
  width: 24px;
  height: 24px;
}

/* 吸顶容器样式 */
.sticky-container {
  position: relative;
  z-index: 99;
  background-color: #fff;
  width: 100%;
  transition: all 0.3s ease;
}

/* 吸顶状态样式 */
.sticky-container.is-sticky {
  position: fixed;
  top: 44px;
  /* 头部高度 */
  left: 0;
  right: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  z-index: 100;
}

/* 吸顶状态下的标签页导航微调 */
.sticky-container.is-sticky .tab-nav {
  padding: 14px 0;
  /* 吸顶时稍微缩小内边距 */
}

/* 吸顶容器的占位元素 */
.sticky-placeholder {
  width: 100%;
  transition: height 0.3s ease;
}

/* 标签页导航 */
.tab-nav {
  display: flex;
  background-color: #fff;
  padding: 16px 0;
  position: relative;
}

.tab-item {
  flex: 1;
  text-align: center;
  font-size: 14px;
  color: #000000;
  line-height: 17px;
  position: relative;
  transition: color 0.3s ease;
  cursor: pointer;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.tab-item.active {
  color: #0474FC;
  font-weight: 500;
}

.tab-item.active::after {
  content: "";
  position: absolute;
  bottom: -12px;
  left: 50%;
  transform: translateX(-50%);
  width: 21px;
  height: 3px;
  background-color: #0474FC;
  border-radius: 2px;
}

/* 订单列表 */
.order-list {
  padding: 10px;
}

.order-item {
  margin-bottom: 15px;
}

.order-card {
  background-color: #fff;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  position: relative;
}

/* 项目图片样式已删除 */

/* 项目信息 */
.project-info {
  display: flex;
  flex-direction: column;
  padding: 25px 20px 0;
  position: relative;
}

.project-title {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #000000;
  margin-bottom: 4px;
}

.order-number {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 400;
  font-size: 12px;
  color: #9E9E9E;
}

.project-status {
  position: absolute;
  right: 20px;
  bottom: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 400;
  font-size: 12px;
}

.status-active {
  color: #6BA771;
}

.status-finished {
  color: #9E9E9E;
}

/* 分割线 */
.divider {
  height: 1px;
  background-color: #D9D9D9;
  margin: 15px 0;
}

/* 金额信息 */
.amount-info {
  display: flex;
  padding: 0 70px;
  justify-content: space-between;
}

.amount-col {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.amount-label {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 400;
  font-size: 12px;
  color: #9E9E9E;
  margin-bottom: 5px;
}

.amount-value {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 400;
  font-size: 16px;
  color: #0474FC;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 按钮区域 */
.button-group {
  display: flex;
  padding: 15px 38px;
  gap: 50px;
  justify-content: space-between;
}

.action-button {
  width: 112px;
  height: 32px;
  border-radius: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 500;
  font-size: 15px;
  color: #fff;
  background-color: #0170FD;
  cursor: pointer;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30px 20px;
  gap: 12px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(4, 116, 252, 0.1);
  border-top: 3px solid #0474FC;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  color: #666666;
  font-size: 14px;
  font-weight: 500;
}

/* 没有更多数据 */
.no-more {
  text-align: center;
  padding: 15px 0;
  color: #999;
  font-size: 14px;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #666666;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
  color: #0474FC;
}

.empty-text {
  color: #666666;
  font-size: 15px;
  font-weight: 500;
}
</style>
