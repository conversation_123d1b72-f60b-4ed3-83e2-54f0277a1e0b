<template>
  <div class="project-detail"> <!-- 头部导航 -->
    <div class="header">
      <div class="back-icon" @click="goBack">
        <img src="@/assets/icons/back-icon.svg" @error="handleImageError($event, 'icon')" alt="返回">
      </div>
      <div class="title">项目详情</div>
      <div class="right-placeholder"></div>
    </div>

    <!-- 顶部蓝色渐变背景 -->
    <div class="top-gradient"></div>
    <!-- 项目信息卡片 -->
    <div class="project-card">
      <div class="project-card-content">
        <!-- 圆形模糊背景 -->
        <div class="blur-circles">
          <div class="blur-circle-1"></div>
          <div class="blur-circle-2"></div>
        </div>

        <!-- 项目标题 -->
        <div class="project-title-section">
          <div class="project-tag">{{ project.title }}</div>
          <div class="project-subtitle">{{ project.subtitle }}</div>
        </div>
        <!-- 项目收益显示已移至标签区 -->
        <!-- 项目标签 -->
        <div class="project-labels">
          <div class="label">{{ project.returnFrequency }}</div>
          <div class="label">{{ project.returnType }}</div>
          <div class="label">{{ project.period }}</div>
          <div class="label">{{ project.yield }} 收益</div>
        </div>

        <!-- 项目金额信息 -->
        <div class="amount-info">
          <div class="amount-item">
            <div class="amount-label">最低购买金额</div>
            <div class="amount-value">{{ project.minAmount }}</div>
          </div>
          <div class="amount-item">
            <div class="amount-label">最高购买金额</div>
            <div class="amount-value">{{ project.maxAmount }}</div>
          </div>
          <div class="amount-item">
            <div class="amount-label">收益</div>
            <div class="amount-value">{{ project.maxPayout }}</div>
          </div>
        </div>
      </div>
    </div> <!-- 收益计算 -->
    <div class="calculation-card">
      <div class="calculation-title">收益计算</div>
      <div class="calculation-content">
        <div class="calculation-item">
          <div class="item-title">结算时间</div>
          <div class="item-desc">{{ project.returnFrequency }}，{{ project.returnType }}</div>
        </div>

        <div class="calculation-item">
          <div class="calc-formula" v-html="formattedRateCalculation"></div>
        </div>

        <div class="calculation-item">
          <div class="item-title">限购次数</div>
          <div class="item-value">{{ project.purchaseLimit }}</div>
        </div>

        <div class="calculation-item">
          <div class="item-title">项目可购买金额</div>
          <div class="item-value">{{ project.availableAmount }}</div>
        </div>
      </div>
    </div>

    <!-- 立即认购按钮 -->
    <div class="purchase-button" @click="purchase">
      <div class="button-text">立即认购</div>
    </div> <!-- 加载中遮罩 -->
    <div class="loading-overlay" v-if="loadingOverlay">
      <div class="loading-spinner"></div>
      <div class="loading-text">加载中...</div>
    </div>
  </div>
</template>

<script>
import ImageErrorHandler from '@/utils/ImageErrorHandler'

export default {
  name: 'ProjectDetail',
  data() {
    return {
      projectId: null,
      loading: false,
      loadingOverlay: false,
      project: {
        id: 0,
        title: '',
        subtitle: '',
        yield: '',
        returnType: '',
        returnFrequency: '',
        period: '',
        periodValue: 0,
        minAmount: '',
        maxAmount: '',
        maxPayout: '',
        purchaseLimit: '',
        availableAmount: '',
        pic: '',
        poster: '',
        content: '',
        rateCalculation: ''
      }
    }
  },
  created() {
    // 获取URL中的项目ID
    this.projectId = this.$route.query.id;
    if (this.projectId) {
      this.fetchProjectDetail();
    } else {
      this.$toast('项目ID不存在');
      this.$router.go(-1);
    }
  }, computed: {
    // 格式化收益计算文本
    formattedRateCalculation() {
      if (!this.project.rateCalculation) return '';

      // 替换换行符为HTML换行标签
      return this.project.rateCalculation.replace(/\n/g, '<br>');
    }
  },
  methods: {    // 处理图片加载错误
    handleImageError(event, type) {
      ImageErrorHandler.handleImageError(event, type)
    },

    goBack() {
      this.$router.go(-1)
    },// 获取项目详情
    fetchProjectDetail() {
      this.loading = true;
      this.loadingOverlay = true;

      // 获取token
      const userToken = localStorage.getItem('user_token');

      if (!userToken) {
        this.$toast('请先登录');
        this.$nextTick(() => { this.$router.replace('/login') });
        this.loading = false;
        this.loadingOverlay = false;
        return;
      }

      // 发起API请求
      this.$http.get(`/product/getDetails?id=${this.projectId}`, {
        headers: {
          'authorization': userToken
        }
      })
        .then(response => {
          if (response.data.code === 200) {
            const data = response.data.data;
            const info = data.info;

            // 处理获取到的项目数据
            this.project = {
              id: info.id,
              title: info.name,
              subtitle: info.channel_name,
              yield: info.rate,
              returnType: info.rate_type.split('分')[1] || '到期返本',
              returnFrequency: info.rate_type.split('分')[0] || '每分钟返息',
              period: `${info.cycle}期限`,
              periodValue: parseInt(info.cycle) || 10,
              minAmount: info.min_amount,
              maxAmount: info.max_amount,
              maxPayout: info.earnings,
              purchaseLimit: info.buy_number > 0 ? `${info.buy_number}次` : '无限制',
              availableAmount: `${info.total_amount}元`,
              pic: info.pic,
              poster: info.poster,
              content: info.content,
              rateCalculation: info.rate_calculation
            };
            this.$nextTick(() => {
              // 更新结算时间和计算公式
              this.updateCalculationInfo(info);
            });

          } else {
            this.$toast(response.data.msg || '获取项目详情失败');
          }

          this.loading = false;
          this.loadingOverlay = false;
        })
        .catch(error => {
          console.error('获取项目详情失败:', error);
          this.$toast('网络异常，请稍后重试');
          this.loading = false;
          this.loadingOverlay = false;
        });
    },
    // 更新收益计算信息
    updateCalculationInfo(info) {
      // 更新DOM中的元素并更新内容
      const calculationItems = document.querySelectorAll('.calculation-item');
      if (calculationItems.length > 0) {
        // 更新结算时间描述
        const settlementDesc = calculationItems[0].querySelector('.item-desc');
        if (settlementDesc) {
          settlementDesc.textContent = info.rate_time || '自投资起开始计息，每分钟返息，到期返本';
        }
      }
    },
    purchase() {
      // 跳转到订单确认页面
      this.$router.push({
        name: 'ProjectDetailOk',
        query: {
          id: this.project.id,
          amount: parseFloat(this.project.minAmount) || 1000
        }
      })
    }
  }
}
</script>

<style scoped>
.project-detail {
  min-height: 100vh;
  background-color: #F6F6F6;
  padding-bottom: 80px;
  position: relative;
  overflow: hidden;
}

/* 头部导航 */
.header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  padding: 0 16px;
  z-index: 10;
  background: #FFFFFF;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.back-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.back-icon img {
  width: 10px;
  height: 16px;
}

.title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 17px;
  font-weight: 500;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  color: #111827;
}

.right-placeholder {
  width: 24px;
  height: 24px;
}

/* 顶部蓝色渐变背景 */
.top-gradient {
  position: absolute;
  top: 44px;
  left: 0;
  width: 100%;
  height: 202px;
  background: linear-gradient(to right, #EACFD8, #50B9F9, #0474FC, #BAB8E0, #95BAEB);
  z-index: 1;
}

/* 项目信息卡片 */
.project-card {
  position: relative;
  margin: 82px 16px 0;
  z-index: 3;
}

.project-card-content {
  background-color: white;
  border-radius: 10px;
  padding: 24px;
  position: relative;
  overflow: hidden;
  min-height: 182px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

/* 圆形模糊背景 */
.blur-circles {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  overflow: hidden;
  z-index: 0;
}

.blur-circle-1 {
  position: absolute;
  width: 148px;
  height: 140px;
  left: -32px;
  top: -74px;
  background-color: #FEF9EE;
  border-radius: 50%;
  filter: blur(30px);
}

.blur-circle-2 {
  position: absolute;
  width: 254px;
  height: 122px;
  right: -22px;
  top: -57px;
  background-color: rgba(232, 239, 251, 0.72);
  border-radius: 50%;
  filter: blur(20px);
}

/* 项目标题区域 */
.project-title-section {
  position: relative;
  z-index: 1;
  margin-bottom: 15px;
}

.project-tag {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  margin-bottom: 6px;
}

.project-subtitle {
  font-size: 12px;
  color: #666;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* 项目收益区域样式已移除，收益显示已集成到标签区域 */

/* 项目标签 */
.project-labels {
  display: flex;
  margin-bottom: 20px;
  position: relative;
  z-index: 1;
}

.label {
  background-color: #FFFAEE;
  border-radius: 4px;
  padding: 3px 10px;
  font-size: 12px;
  color: #EE8300;
  margin-right: 8px;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
}

.label:nth-child(3) {
  background-color: #E8EFFB;
  color: #0094FF;
}

.label:nth-child(2) {
  display: none;
  /* 根据Figma设计稿隐藏第二个标签 */
}

/* 收益标签特殊样式 */
.label:nth-child(4) {
  color: #FF3B30;
  font-family: 'DIN Black', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 14px;
  font-weight: 900;
  padding: 3px 10px;
}

/* 项目金额信息 */
.amount-info {
  display: flex;
  justify-content: space-between;
  margin-top: 30px;
  position: relative;
  z-index: 1;
}

.amount-item {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.amount-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.amount-value {
  font-family: 'DIN', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 16px;
  font-weight: 700;
  color: #0474FC;
}

/* 收益计算卡片 */
.calculation-card {
  margin: 25px 16px 0;
  background-color: white;
  border-radius: 9px;
  padding: 20px;
  position: relative;
  z-index: 3;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.calculation-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
}

.calculation-content {
  padding: 0 5px;
}

.calculation-item {
  margin-bottom: 16px;
}

.item-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 6px;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
}

.item-desc,
.calc-formula,
.item-value {
  font-size: 14px;
  color: #9E9E9E;
  line-height: 1.5;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* 立即认购按钮 */
.purchase-button {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 73px;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.button-text {
  width: 355px;
  height: 47px;
  background-color: #0170FD;
  border-radius: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 15px;
  font-weight: 500;
  color: #F8F9FA;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* 加载中遮罩 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(4, 116, 252, 0.1);
  border-top: 3px solid #0474FC;
  border-radius: 50%;
  animation: spin 1s cubic-bezier(0.55, 0.25, 0.25, 0.7) infinite;
  margin-bottom: 15px;
}

.loading-text {
  font-size: 14px;
  color: #0474FC;
  font-weight: 500;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
