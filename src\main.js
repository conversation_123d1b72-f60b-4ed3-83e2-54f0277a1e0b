import Vue from 'vue'
import App from './App.vue'
import router from './router'
import Vant from 'vant'
import { Toast, Dialog } from 'vant'
import 'vant/lib/index.css'
import './style/mobile.css'
import 'amfe-flexible/index.js'
import axios from 'axios'

Vue.config.productionTip = false
Vue.use(Vant)
Vue.use(Toast)
Vue.use(Dialog)

// 配置 axios
// 无论在什么环境下都直接使用API地址
axios.defaults.baseURL = 'http://api.43bt.com'
axios.defaults.timeout = 10000
// 添加请求拦截器，用于调试
axios.interceptors.request.use(
  config => {
    return config
  },
  error => {
    return Promise.reject(error)
  }
)
Vue.prototype.$http = axios
Vue.prototype.$toast = Toast
Vue.prototype.$dialog = Dialog

// 路由守卫
router.beforeEach((to, from, next) => {
  document.title = to.meta.title || '移动端首页'
  next()
})

new Vue({
  router,
  render: h => h(App)
}).$mount('#app')