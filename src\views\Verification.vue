<template>
    <div class="verification">
        <!-- 头部 -->
        <div class="header">
            <div class="back-icon" @click="goBack">
                <img src="@/assets/icons/back-icon.svg" alt="返回">
            </div>
            <div class="title">实名认证</div>
            <div v-if="isVerified" class="verified-badge">已认证</div>
        </div>

        <!-- 加载中 -->
        <div class="loading-container" v-if="loading">
            <div class="loading-spinner">
                <van-loading type="spinner" color="#ee6a7b" />
            </div>
            <div class="loading-text">加载中..</div>
        </div>

        <!-- 表单区域 -->
        <template v-else>
            <div class="form-section">
                <!-- 姓名 -->
                <div class="form-item">
                    <div class="item-label">姓名</div>
                    <div class="item-input">
                        <input type="text" v-model="verificationInfo.name" placeholder="请输入您的姓名" :disabled="isVerified">
                    </div>
                </div>

                <!-- 身份证号码 -->
                <div class="form-item">
                    <div class="item-label">身份证号码</div>
                    <div class="item-input">
                        <input type="text" v-model="verificationInfo.idCard" placeholder="请输入您的身份证号码" :disabled="isVerified">
                    </div>
                </div>
            </div>
            
            <div class="bank-section">
                <div class="section-title">银行卡信息</div>
                
                <!-- 持卡人姓名 -->
                <div class="form-item">
                    <div class="item-label">持卡人姓名</div>
                    <div class="item-input">
                        <input type="text" v-model="verificationInfo.bankHolderName" placeholder="请输入您的姓名" :disabled="isVerified">
                    </div>
                </div>
                
                <!-- 所属银行 -->
                <div class="form-item">
                    <div class="item-label">所属银行</div>
                    <div class="item-input">
                        <input type="text" v-model="verificationInfo.bankName" placeholder="请输入您的开户银行" :disabled="isVerified">
                    </div>
                </div>
                
                <!-- 银行卡号 -->
                <div class="form-item">
                    <div class="item-label">银行卡号</div>
                    <div class="item-input">
                        <input type="text" v-model="verificationInfo.bankCard" placeholder="请输入您的银行卡号" :disabled="isVerified">
                    </div>
                </div>
                
                <!-- 开户网点 -->
                <div class="form-item">
                    <div class="item-label">开户网点</div>
                    <div class="item-input">
                        <input type="text" v-model="verificationInfo.bankBranch" placeholder="请输入您的开户银行支行" :disabled="isVerified">
                    </div>
                </div>
            </div>
            
            <!-- 提交按钮 -->
            <button class="submit-btn" 
                @click="submitVerification" 
                :disabled="submitting || isVerified"
                :class="{'verified-btn': isVerified}">
                {{ submitting ? '提交中..' : (isVerified ? '已完成实名认证' : '确认') }}
            </button>
        </template>
    </div>
</template>

<script>
import ImageErrorHandler from '@/utils/ImageErrorHandler'

export default {
    name: 'Verification',
    data() {
        return {
            submitting: false,
            loading: false,          // 加载状态
            isVerified: false,       // 是否已认证
            verificationInfo: {
                name: '',             // 姓名
                idCard: '',           // 身份证号
                bankHolderName: '',   // 持卡人姓名
                bankName: '',         // 银行名称
                bankCard: '',         // 银行卡号
                bankBranch: ''        // 开户网点
            }
        }
    },
    created() {
        this.fetchBankAndVerificationInfo()
    },
    methods: {
        // 处理图片加载错误
        handleImageError(event, type) {
            ImageErrorHandler.handleImageError(event, type)
        },
        
        // 返回上一页
        goBack() {
            this.$router.go(-1)
        },
          // 获取银行卡信息和认证状态
        fetchBankAndVerificationInfo() {
            const userToken = localStorage.getItem('user_token')
            if (!userToken) {
                this.$toast('请先登录')
                // 使用 $nextTick 确保 toast 显示后再跳转
                this.$nextTick(() => {
                    this.$router.replace('/login')
                })
                return
            }
            
            this.loading = true
            
            // 显示加载提示
            this.$toast.loading({
                message: '加载中..',
                forbidClick: true,
                duration: 0
            })
            
            // 调用API获取银行卡信息
            this.$http.get('/member/getBank', {
                headers: {
                    'authorization': userToken
                }
            })
            .then(response => {
                this.$toast.clear()
                if (response.data.code === 200) {
                    const data = response.data.data                    // 更新认证状态
                    this.isVerified = (data.info && data.info.kyc === true) || data.kyc === true
                    
                    // 检查是否已完成实名认证
                    if (this.isVerified) {
                        this.$toast('您已完成实名认证')
                        // 延迟返回上一页，让用户有时间看到提示
                        setTimeout(() => {
                            try {
                                window.history.back() // 尝试另一种返回方式
                            } catch(e) {
                                console.error('返回失败:', e)
                                this.$router.go(-1) // 如果history.back()失败，使用router.go
                            }
                        }, 1500)
                        return
                    }
                    
                    // 检查用户是否已有银行卡信息
                    if (data.info) {
                        const info = data.info
                        
                        // 填充表单数据
                        this.verificationInfo = {
                            name: info.name || '',
                            idCard: '', // API 不返回身份证号码，保持为空
                            bankHolderName: info.name || '',
                            bankName: info.bank_name || '',
                            bankCard: info.bank_card || '',
                            bankBranch: info.bank_address || ''
                        }
                        
                        // 如果持卡人姓名与实名认证姓名相同，自动复制
                        if (!this.verificationInfo.name && this.verificationInfo.bankHolderName) {
                            this.verificationInfo.name = this.verificationInfo.bankHolderName
                        }
                    }                } else {
                    this.$toast('获取银行卡信息失败')
                }
            })
            .catch(error => {
                this.$toast.clear()
                console.error('获取银行卡信息失败', error)
                this.$toast('获取数据失败，请稍后重试')
            })
            .finally(() => {
                this.loading = false
            })
        },
        
        // 提交认证信息
        submitVerification() {
            // 检查是否已完成实名认证
            if (this.isVerified) {
                this.$toast('您已完成实名认证，无需重复提交')
                setTimeout(() => {                    try {
                        window.history.back() // 尝试另一种返回方式
                    } catch(e) {
                        console.error('返回失败:', e)
                        this.$router.go(-1) // 如果history.back()失败，使用router.go
                    }
                }, 1500)
                return
            }
            
            // 表单验证
            if (!this.verificationInfo.name) {
                this.$toast('请输入姓名')
                return
            }
            
            if (!this.verificationInfo.idCard) {
                this.$toast('请输入身份证号码')
                return
            }
            
            if (!this.verificationInfo.bankHolderName) {
                this.$toast('请输入持卡人姓名')
                return
            }
            
            if (!this.verificationInfo.bankName) {
                this.$toast('请输入银行名称')
                return
            }
            
            if (!this.verificationInfo.bankCard) {
                this.$toast('请输入银行卡号')
                return
            }
            
            if (!this.verificationInfo.bankBranch) {
                this.$toast('请输入开户网点')
                return
            }
            
            // 身份证号码格式验证
            const idCardReg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
            if (!idCardReg.test(this.verificationInfo.idCard)) {
                this.$toast('请输入正确的身份证号码')
                return
            }
            
            // 银行卡号验证
            if (!/^\d{16,19}$/.test(this.verificationInfo.bankCard)) {
                this.$toast('请输入正确的银行卡号')
                return
            }
            
            // 确认提交对话框
            this.$dialog.confirm({
                title: '提交确认',
                message: '确认提交实名认证信息？',
                confirmButtonText: '确认',
                cancelButtonText: '取消'
            })
            .then(() => {
                this.submitForm()
            })
            .catch(() => {
                // 用户取消操作
            })
        },
          // 提交表单到API
        submitForm() {
            const userToken = localStorage.getItem('user_token')
            if (!userToken) {
                this.$toast('请先登录')
                // 使用 $nextTick 确保 toast 显示后再跳转
                this.$nextTick(() => {
                    this.$router.replace('/login')
                })
                return
            }
            
            this.submitting = true
            this.$toast.loading({
                message: '提交中..',
                forbidClick: true,
                duration: 0
            })
            
            // 准备提交的数据
            const formData = {
                kyc_name: this.verificationInfo.name,
                kyc_id: this.verificationInfo.idCard,
                name: this.verificationInfo.bankHolderName,
                bank_name: this.verificationInfo.bankName,
                bank_card: this.verificationInfo.bankCard,
                bank_address: this.verificationInfo.bankBranch
            }
            
            // 调用API
            this.$http.post('/member/doKyc', formData, {
                headers: {
                    'authorization': userToken,
                    'Content-Type': 'application/json'
                }
            })
            .then(response => {
                this.$toast.clear()
                
                if (response.data.code === 200) {
                    this.$toast.success('实名认证提交成功')
                    setTimeout(() => {
                        this.$router.go(-1)
                    }, 1500)
                } else {
                    this.$toast(response.data.msg || '提交失败，请重试')
                }
            })
            .catch(error => {
                this.$toast.clear()
                console.error('实名认证提交失败:', error)
                this.$toast('网络异常，请稍后重试')
            })
            .finally(() => {
                this.submitting = false
            })
        }
    }
}
</script>

<style scoped>
.verification {
    min-height: 100vh;
    background-color: #f7f7f7;
    padding-bottom: 20px;
}

.header {
    display: flex;
    align-items: center;
    padding: 10px 16px;
    background-color: #fff;
    position: relative;
}

.verified-badge {
    position: absolute;
    right: 16px;
    background-color: #07c160;
    color: white;
    border-radius: 12px;
    padding: 2px 8px;
    font-size: 12px;
    font-weight: 500;
}

.back-icon {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.back-icon img {
    width: 20px;
    height: 20px;
}

.title {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    font-size: 18px;
    font-weight: 500;
    color: #333;
}

/* 表单部分 */
.form-section, .bank-section {
    background-color: #fff;
    margin-top: 10px;
}

.section-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    padding: 15px 16px 5px;
}

.form-item {
    display: flex;
    align-items: center;
    padding: 15px 16px;
    border-bottom: 1px solid #f5f5f5;
}

.item-label {
    width: 100px;
    font-size: 15px;
    color: #333;
}

.item-input {
    flex: 1;
}

.item-input input {
    width: 100%;
    border: none;
    outline: none;
    font-size: 15px;
    color: #333;
    background-color: transparent;
}

.item-input input::placeholder {
    color: #c8c8c8;
}

/* 提交按钮 */
.submit-btn {
    width: calc(100% - 30px);
    background-color: #ee6a7b;
    color: #fff;
    text-align: center;
    padding: 12px 15px;
    margin: 40px 15px 20px;
    border-radius: 5px;
    font-size: 16px;
    border: none;
    cursor: pointer;
}

.submit-btn:disabled {
    background-color: #f0a0a8;
    cursor: not-allowed;
}

.verified-btn {
    background-color: #07c160 !important;
}

/* 加载状态 */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
}

.loading-spinner {
    margin-bottom: 15px;
}

.loading-text {
    font-size: 14px;
    color: #666;
}
</style>
