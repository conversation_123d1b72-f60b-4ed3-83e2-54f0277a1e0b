<template>
  <div class="wallet">
    <!-- 头部 -->
    <div class="header">
      <div class="back-icon" @click="goBack">
        <img src="@/assets/icons/back-icon.svg" alt="返回">
      </div>
      <div class="title">我的钱包</div>
      <div class="right-icon" @click="goToDetail">
        <van-icon name="records" size="24" />
      </div>
    </div>

    <!-- 钱包卡片 -->
    <div class="wallet-card">      <div class="card-header">
        <div class="card-title">账户余额 (元)</div>
        <div class="card-balance">{{ walletInfo.balance }}</div>
      </div>
      <div class="card-actions">
        <div class="action-btn" @click="showRechargePopup">充值</div>
        <div class="action-btn" @click="showWithdrawPopup">提现</div>
      </div>
    </div>

    <!-- 收益统计 -->
    <div class="income-stats">
      <div class="stat-item">
        <div class="stat-value">{{ walletInfo.todayIncome }}</div>
        <div class="stat-label">今日收益</div>
      </div>
      <div class="stat-item">
        <div class="stat-value">{{ walletInfo.monthIncome }}</div>
        <div class="stat-label">本月收益</div>
      </div>
      <div class="stat-item">
        <div class="stat-value">{{ walletInfo.totalIncome }}</div>
        <div class="stat-label">累计收益</div>
      </div>
    </div>

    <!-- 交易记录 -->
    <div class="transaction-section">
      <div class="section-header">
        <div class="section-title">交易记录</div>
        <div class="section-more" @click="goToDetail">
          查看全部
          <van-icon name="arrow" />
        </div>
      </div>

      <div class="transaction-list">
        <div class="transaction-item" v-for="(item, index) in transactions" :key="index">
          <div class="transaction-icon" :class="item.type">
            <van-icon :name="getIconByType(item.type)" size="24" />
          </div>
          <div class="transaction-info">
            <div class="transaction-title">{{ item.title }}</div>
            <div class="transaction-time">{{ item.time }}</div>
          </div>
          <div class="transaction-amount" :class="{ 'income': item.amount > 0 }">
            {{ item.amount > 0 ? '+' : '' }}{{ item.amount }}
          </div>
        </div>
      </div>
    </div>    <!-- 银行卡 -->
    <div class="bank-section">
      <div class="section-header">
        <div class="section-title">我的银行卡</div>
        <div class="section-more" @click="addBankCard">
          添加
          <van-icon name="plus" />
        </div>
      </div>

      <div class="bank-list" v-if="bankCards.length > 0">
        <div class="bank-item" v-for="(card, index) in bankCards" :key="index" @click="selectBankCard(card)">
          <div class="bank-logo">
            <img :src="card.logo" alt="银行logo">
          </div>
          <div class="bank-info">
            <div class="bank-name">{{ card.bankName }}</div>
            <div class="bank-number">{{ maskCardNumber(card.cardNumber) }}</div>
          </div>
          <van-icon name="success" v-if="card.isDefault" color="#FF618B" />
        </div>
      </div>

      <div class="empty-bank" v-else @click="addBankCard">
        <van-icon name="plus" size="40" color="#ddd" />
        <div class="empty-text">添加银行卡</div>
      </div>
    </div>

    <!-- 充值弹窗 -->
    <van-popup v-model="showRecharge" round position="bottom">
      <div class="popup-container">
        <div class="popup-header">
          <div class="popup-title">充值</div>
          <van-icon name="cross" @click="showRecharge = false" />
        </div>        <div class="amount-input">
          <div class="input-label">充值金额</div>
          <div class="input-wrapper">
            <span class="currency">￥</span>
            <input type="number" v-model="rechargeAmount" placeholder="请输入充值金额">
          </div>
        </div>
        <div class="quick-amounts">
          <div class="amount-tag" v-for="amount in quickAmounts" :key="amount"
            :class="{ active: rechargeAmount == amount }" @click="rechargeAmount = amount">
            {{ amount }}元
          </div>
        </div>
        <div class="popup-btn" @click="recharge">确认充值</div>
      </div>
    </van-popup>

    <!-- 提现弹窗 -->
    <van-popup v-model="showWithdraw" round position="bottom">
      <div class="popup-container">
        <div class="popup-header">
          <div class="popup-title">提现</div>
          <van-icon name="cross" @click="showWithdraw = false" />
        </div>        <div class="amount-input">
          <div class="input-label">提现金额</div>
          <div class="input-wrapper">
            <span class="currency">￥</span>
            <input type="number" v-model="withdrawAmount" placeholder="请输入提现金额">
          </div>
        </div>
        <div class="withdraw-tip">
          可提现余额{{ walletInfo.balance }}元
        </div>
        <div class="popup-btn" @click="withdraw">确认提现</div>
      </div>
    </van-popup>
  </div>
</template>

<script>
export default {
  name: 'Wallet',
  data() {
    return {
      walletInfo: {
        balance: '1,280.50',
        todayIncome: '68.20',
        monthIncome: '580.75',
        totalIncome: '2,350.60'
      },
      transactions: [        {
          title: '直播间奖励',
          time: '2023-05-21 14:30',
          amount: 50.00,
          type: 'reward'
        },
        {
          title: '商品购买',
          time: '2023-05-20 10:15',
          amount: -128.50,
          type: 'shopping'
        },
        {
          title: '团队奖励',
          time: '2023-05-19 16:45',
          amount: 86.30,
          type: 'team'
        },
        {
          title: '提现到银行卡',
          time: '2023-05-18 09:20',
          amount: -500.00,
          type: 'withdraw'
        }
      ],
      bankCards: [
        {
          bankName: '中国农业银行',
          cardNumber: '6228480038009376521',
          logo: require('@/assets/images/avatar.jpg'),
          isDefault: true
        },
        {
          bankName: '中国建设银行',
          cardNumber: '6222020302073952378',
          logo: require('@/assets/images/avatar.jpg'),
          isDefault: false
        }
      ],
      showRecharge: false,
      showWithdraw: false,
      rechargeAmount: '',
      withdrawAmount: '',
      quickAmounts: [50, 100, 200, 500, 1000]
    }
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    goToDetail() {
      this.$toast('交易详情功能开发中')
    },
    getIconByType(type) {
      const iconMap = {
        reward: 'gift-o',
        shopping: 'cart-o',
        team: 'friends-o',
        withdraw: 'cash-back-record'
      }
      return iconMap[type] || 'balance-o'
    },
    maskCardNumber(number) {
      if (!number) return ''
      return number.replace(/^(\d{4})\d+(\d{4})$/, '$1 **** **** $2')
    },
    showRechargePopup() {
      this.rechargeAmount = ''
      this.showRecharge = true
    },
    showWithdrawPopup() {
      this.withdrawAmount = ''
      this.showWithdraw = true
    },
    recharge() {      if (!this.rechargeAmount || this.rechargeAmount <= 0) {
        this.$toast('请输入有效的充值金额')
        return
      }

      this.$toast(`充值${this.rechargeAmount}元成功`)
      this.showRecharge = false
    },
    withdraw() {
      if (!this.withdrawAmount || this.withdrawAmount <= 0) {
        this.$toast('请输入有效的提现金额')
        return
      }

      const balance = parseFloat(this.walletInfo.balance.replace(/,/g, ''))
      if (this.withdrawAmount > balance) {
        this.$toast('提现金额不能大于可用余额')
        return
      }

      this.$toast(`提现${this.withdrawAmount}元申请已提交，请等待审核`)
      this.showWithdraw = false
    },
    addBankCard() {
      this.$toast('添加银行卡功能开发中')
    },
    selectBankCard(card) {
      this.$toast(`已选择${card.bankName}卡`)
    }
  }
}
</script>

<style scoped>
.wallet {
  min-height: 100vh;
  background-color: #f6f6f6;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 16px;
  background-color: #fff;
}

.back-icon,
.right-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon img {
  width: 20px;
  height: 20px;
}

.title {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

.wallet-card {
  background: linear-gradient(135deg, #FF618B 0%, #FF8C6B 100%);
  border-radius: 10px;
  padding: 20px;
  margin: 15px;
  color: #fff;
}

.card-header {
  margin-bottom: 20px;
}

.card-title {
  font-size: 14px;
  opacity: 0.8;
  margin-bottom: 5px;
}

.card-balance {
  font-size: 32px;
  font-weight: 500;
}

.card-actions {
  display: flex;
  justify-content: space-around;
}

.action-btn {
  background-color: rgba(255, 255, 255, 0.2);
  padding: 8px 30px;
  border-radius: 20px;
  font-size: 16px;
}

.income-stats {
  display: flex;
  background-color: #fff;
  border-radius: 10px;
  margin: 15px;
  padding: 15px 0;
}

.stat-item {
  flex: 1;
  text-align: center;
  border-right: 1px solid #f5f5f5;
}

.stat-item:last-child {
  border-right: none;
}

.stat-value {
  font-size: 18px;
  font-weight: 500;
  color: #FF618B;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #999;
}

.transaction-section,
.bank-section {
  background-color: #fff;
  border-radius: 10px;
  margin: 15px;
  padding: 15px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.section-more {
  font-size: 14px;
  color: #999;
  display: flex;
  align-items: center;
}

.transaction-list {
  display: flex;
  flex-direction: column;
}

.transaction-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
}

.transaction-item:last-child {
  border-bottom: none;
}

.transaction-icon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.transaction-icon.reward {
  background-color: rgba(255, 97, 139, 0.1);
  color: #FF618B;
}

.transaction-icon.shopping {
  background-color: rgba(64, 158, 255, 0.1);
  color: #409EFF;
}

.transaction-icon.team {
  background-color: rgba(82, 196, 26, 0.1);
  color: #52C41A;
}

.transaction-icon.withdraw {
  background-color: rgba(144, 147, 153, 0.1);
  color: #909399;
}

.transaction-info {
  flex: 1;
}

.transaction-title {
  font-size: 16px;
  color: #333;
  margin-bottom: 5px;
}

.transaction-time {
  font-size: 12px;
  color: #999;
}

.transaction-amount {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.transaction-amount.income {
  color: #FF618B;
}

.bank-list {
  display: flex;
  flex-direction: column;
}

.bank-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
}

.bank-item:last-child {
  border-bottom: none;
}

.bank-logo {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  overflow: hidden;
  margin-right: 12px;
}

.bank-logo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.bank-info {
  flex: 1;
}

.bank-name {
  font-size: 16px;
  color: #333;
  margin-bottom: 5px;
}

.bank-number {
  font-size: 14px;
  color: #999;
}

.empty-bank {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 0;
  border: 1px dashed #ddd;
  border-radius: 10px;
}

.empty-text {
  font-size: 14px;
  color: #999;
  margin-top: 10px;
}

.popup-container {
  padding: 20px;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.popup-title {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

.amount-input {
  margin-bottom: 20px;
}

.input-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

.input-wrapper {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.currency {
  font-size: 24px;
  font-weight: 500;
  color: #333;
  margin-right: 10px;
}

.input-wrapper input {
  flex: 1;
  border: none;
  outline: none;
  font-size: 24px;
  color: #333;
}

.quick-amounts {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20px;
}

.amount-tag {
  background-color: #f5f5f5;
  color: #666;
  padding: 8px 15px;
  border-radius: 20px;
  margin-right: 10px;
  margin-bottom: 10px;
}

.amount-tag.active {
  background-color: rgba(255, 97, 139, 0.1);
  color: #FF618B;
}

.withdraw-tip {
  font-size: 14px;
  color: #999;
  margin-bottom: 20px;
}

.popup-btn {
  background-color: #FF618B;
  color: #fff;
  text-align: center;
  padding: 12px 0;
  border-radius: 25px;
  font-size: 16px;
}
</style>
