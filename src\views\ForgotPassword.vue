<template>
  <div class="forgot-password">
    <!-- 背景 -->
    <div class="background">
      <img src="@/assets/images/figma/background-image.png" alt="背景" class="background-image">
      <div class="background-overlay"></div>
    </div>

    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="time">9:41</div>
      <div class="right-icons">
        <div class="signal-icon"></div>
        <div class="wifi-icon"></div>
        <div class="battery-icon"></div>
      </div>
    </div>

    <!-- 返回按钮 -->
    <div class="back-button" @click="goBack">
      <img src="@/assets/images/figma/back-arrow-icon.svg" alt="返回" class="back-icon">
    </div>

    <!-- 主要内容 -->
    <div class="main-content">
      <div class="content-wrapper">
        <h1 class="page-title">更改密码</h1>
        
        <div class="form-container">
          <!-- 手机号输入 -->
          <div class="form-field">
            <label class="field-label">手机号码</label>
            <div class="input-wrapper">
              <div class="country-code">
                <span class="plus-sign">+</span>
                <img src="@/assets/images/figma/arrow-down-icon.svg" alt="下拉" class="dropdown-icon">
                <div class="separator"></div>
              </div>
              <input 
                type="tel" 
                v-model="phone" 
                placeholder="************"
                class="phone-input"
              >
            </div>
          </div>

          <!-- 验证码输入 -->
          <div class="form-field">
            <label class="field-label">验证码</label>
            <div class="input-wrapper verification-wrapper">
              <input 
                type="text" 
                v-model="verificationCode" 
                placeholder="请输入验证码"
                class="verification-input"
              >
              <button 
                class="send-code-btn" 
                @click="sendCode"
                :disabled="countdown > 0"
              >
                {{ countdown > 0 ? `${countdown}s` : '发送' }}
              </button>
            </div>
          </div>

          <!-- 密码输入 -->
          <div class="form-field">
            <label class="field-label">密码</label>
            <div class="input-wrapper password-wrapper">
              <input 
                :type="showPassword ? 'text' : 'password'" 
                v-model="password" 
                placeholder="请输入密码"
                class="password-input"
              >
              <div class="eye-icon" @click="togglePasswordVisibility">
                <img 
                  :src="showPassword ? '@/assets/images/figma/eye-icon-1.svg' : '@/assets/images/figma/eye-icon-2.svg'" 
                  alt="密码可见性"
                >
              </div>
            </div>
          </div>

          <!-- 确认密码输入 -->
          <div class="form-field">
            <label class="field-label">确认密码</label>
            <div class="input-wrapper password-wrapper">
              <input 
                :type="showConfirmPassword ? 'text' : 'password'" 
                v-model="confirmPassword" 
                placeholder="请再次输入密码"
                class="password-input"
              >
              <div class="eye-icon" @click="toggleConfirmPasswordVisibility">
                <img 
                  :src="showConfirmPassword ? '@/assets/images/figma/eye-icon-3.svg' : '@/assets/images/figma/eye-icon-4.svg'" 
                  alt="密码可见性"
                >
              </div>
            </div>
          </div>

          <!-- 同意条款 -->
          <div class="agreement-section">
            <div 
              class="checkbox" 
              :class="{ 'checked': agreeToTerms }"
              @click="toggleAgreement"
            ></div>
            <span class="agreement-text">
              我同意
              <span class="link" @click="openTerms">条款和条件</span>
              和
              <span class="link" @click="openPrivacy">隐私政策</span>
              。
            </span>
          </div>
        </div>

        <!-- 创建账户按钮 -->
        <button 
          class="create-account-btn"
          @click="resetPassword"
          :disabled="!canSubmit"
        >
          创建账户
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ForgotPassword',
  data() {
    return {
      phone: '',
      verificationCode: '',
      password: '',
      confirmPassword: '',
      showPassword: false,
      showConfirmPassword: false,
      agreeToTerms: false,
      countdown: 0,
      countdownTimer: null
    }
  },
  computed: {
    canSubmit() {
      return this.phone && 
             this.verificationCode && 
             this.password && 
             this.confirmPassword && 
             this.password === this.confirmPassword &&
             this.agreeToTerms
    }
  },
  beforeDestroy() {
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer)
    }
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    
    togglePasswordVisibility() {
      this.showPassword = !this.showPassword
    },
    
    toggleConfirmPasswordVisibility() {
      this.showConfirmPassword = !this.showConfirmPassword
    },
    
    toggleAgreement() {
      this.agreeToTerms = !this.agreeToTerms
    },
    
    sendCode() {
      if (!this.phone) {
        this.$toast('请输入手机号')
        return
      }
      
      if (!/^1[3-9]\d{9}$/.test(this.phone)) {
        this.$toast('请输入正确的手机号')
        return
      }
      
      // 开始倒计时
      this.countdown = 60
      this.countdownTimer = setInterval(() => {
        this.countdown--
        if (this.countdown <= 0) {
          clearInterval(this.countdownTimer)
          this.countdownTimer = null
        }
      }, 1000)
      
      // 发送验证码逻辑
      this.sendVerificationCode()
    },
    
    async sendVerificationCode() {
      try {
        const response = await this.$http.post('/sms/send', {
          mobile: this.phone,
          type: 'reset_password'
        })
        
        if (response.data.code === 200) {
          this.$toast('验证码已发送')
        } else {
          this.$toast(response.data.msg || '发送失败')
          // 重置倒计时
          this.countdown = 0
          if (this.countdownTimer) {
            clearInterval(this.countdownTimer)
            this.countdownTimer = null
          }
        }
      } catch (error) {
        console.error('发送验证码失败:', error)
        this.$toast('发送失败，请重试')
        // 重置倒计时
        this.countdown = 0
        if (this.countdownTimer) {
          clearInterval(this.countdownTimer)
          this.countdownTimer = null
        }
      }
    },
    
    async resetPassword() {
      if (!this.canSubmit) {
        return
      }
      
      if (this.password.length < 6) {
        this.$toast('密码长度不能少于6位')
        return
      }
      
      try {
        const response = await this.$http.post('/password/reset', {
          mobile: this.phone,
          code: this.verificationCode,
          password: this.password,
          password_confirmation: this.confirmPassword
        })
        
        if (response.data.code === 200) {
          this.$toast('密码重置成功')
          setTimeout(() => {
            this.$router.push('/login')
          }, 1500)
        } else {
          this.$toast(response.data.msg || '重置失败')
        }
      } catch (error) {
        console.error('重置密码失败:', error)
        this.$toast('重置失败，请重试')
      }
    },
    
    openTerms() {
      // 打开服务条款
      this.$router.push('/terms')
    },
    
    openPrivacy() {
      // 打开隐私政策
      this.$router.push('/privacy')
    }
  }
}
</script>

<style scoped>
.forgot-password {
  width: 100vw;
  height: 100vh;
  position: relative;
  overflow: hidden;
}

/* 背景 */
.background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.background-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.background-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0) 0%,
    rgba(94, 216, 119, 0) 35%,
    rgba(0, 0, 0, 0.38) 65%,
    rgba(0, 0, 0, 1) 100%
  );
}

/* 状态栏 */
.status-bar {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 47px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;
  z-index: 10;
}

.time {
  color: #FFFFFF;
  font-family: Urbanist;
  font-weight: 600;
  font-size: 15px;
  line-height: 20px;
  text-align: center;
  letter-spacing: -0.5px;
}

.right-icons {
  display: flex;
  align-items: center;
  gap: 5px;
}

.signal-icon,
.wifi-icon,
.battery-icon {
  width: 17px;
  height: 11px;
  background: #FFFFFF;
  /* 这里可以添加具体的图标样式 */
}

.battery-icon {
  width: 24px;
  height: 11px;
  border: 1px solid rgba(255, 255, 255, 0.35);
  border-radius: 3px;
  position: relative;
}

.battery-icon::after {
  content: '';
  position: absolute;
  right: -2px;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 4px;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 1px;
}

/* 返回按钮 */
.back-button {
  position: absolute;
  top: 64px;
  left: 24px;
  width: 44px;
  height: 44px;
  background: rgba(0, 0, 0, 0.39);
  border-radius: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
}

.back-icon {
  width: 24px;
  height: 24px;
}

/* 主要内容 */
.main-content {
  position: absolute;
  top: 176px;
  left: 21px;
  right: 21px;
  bottom: 0;
  background: rgba(0, 0, 0, 0.58);
  border-radius: 20px;
  z-index: 5;
  overflow: hidden;
}

.content-wrapper {
  padding: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.page-title {
  color: #FFFFFF;
  font-family: Urbanist;
  font-weight: 600;
  font-size: 24px;
  line-height: 34px;
  margin: 0 0 57px 0;
}

.form-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 18px;
}

/* 表单字段 */
.form-field {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.field-label {
  color: #FFFFFF;
  font-family: Urbanist;
  font-weight: 700;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: -0.5px;
}

.input-wrapper {
  height: 45px;
  background: rgba(230, 233, 237, 0.17);
  border: 1px solid #71848E;
  border-radius: 100px;
  display: flex;
  align-items: center;
  padding: 0 18px;
}

/* 手机号输入 */
.country-code {
  display: flex;
  align-items: center;
  gap: 7px;
  margin-right: 7px;
}

.plus-sign {
  color: #FFFFFF;
  font-family: Urbanist;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: -0.5px;
}

.dropdown-icon {
  width: 10px;
  height: 10px;
}

.separator {
  width: 1px;
  height: 12px;
  background: #71848E;
}

.phone-input {
  flex: 1;
  background: transparent;
  border: none;
  outline: none;
  color: #ADB3B7;
  font-family: Urbanist;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
}

.phone-input::placeholder {
  color: #ADB3B7;
}

/* 验证码输入 */
.verification-wrapper {
  justify-content: space-between;
}

.verification-input {
  flex: 1;
  background: transparent;
  border: none;
  outline: none;
  color: #ADB3B7;
  font-family: Urbanist;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
}

.verification-input::placeholder {
  color: #ADB3B7;
}

.send-code-btn {
  color: #FFFFFF;
  font-family: Urbanist;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  background: transparent;
  border: none;
  cursor: pointer;
  min-width: 40px;
  text-align: right;
}

.send-code-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 密码输入 */
.password-wrapper {
  justify-content: space-between;
}

.password-input {
  flex: 1;
  background: transparent;
  border: none;
  outline: none;
  color: #ADB3B7;
  font-family: Urbanist;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
}

.password-input::placeholder {
  color: #ADB3B7;
}

.eye-icon {
  width: 16px;
  height: 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.eye-icon img {
  width: 100%;
  height: 100%;
  opacity: 0.66;
}

/* 同意条款 */
.agreement-section {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 20px;
}

.checkbox {
  width: 14px;
  height: 14px;
  background: transparent;
  border: 1px solid #FFFFFF;
  border-radius: 2px;
  cursor: pointer;
  position: relative;
  flex-shrink: 0;
}

.checkbox.checked {
  background: #FFFFFF;
}

.checkbox.checked::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 4px;
  width: 4px;
  height: 8px;
  border: solid #000000;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.agreement-text {
  color: #FFFFFF;
  font-family: Urbanist;
  font-weight: 500;
  font-size: 10px;
  line-height: 20px;
  flex: 1;
}

.agreement-text .link {
  color: #FFFFFF;
  text-decoration: underline;
  cursor: pointer;
}

/* 创建账户按钮 */
.create-account-btn {
  width: 200px;
  height: 40px;
  background: linear-gradient(90deg, #69C80A 0%, #02B460 100%);
  border-radius: 5px;
  border: none;
  color: #030303;
  font-family: Urbanist;
  font-weight: 800;
  font-size: 16px;
  line-height: 20px;
  text-align: center;
  cursor: pointer;
  align-self: center;
  margin: 40px 0 20px 0;
}

.create-account-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.create-account-btn:active {
  opacity: 0.8;
}
</style>