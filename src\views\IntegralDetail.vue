<template>
  <div class="integral-detail">
    <!-- 头部 -->
    <div class="header">
      <div class="back-icon" @click="goBack">
        <img src="@/assets/icons/back-icon.svg" alt="返回">
      </div>
      <div class="title">积分明细</div>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-section">
      <div class="filter-item" @click="showTypeFilter = true">
        <span>{{ typeText }}</span>
        <van-icon name="arrow-down" />
      </div>
    </div>

    <!-- 详细列表 -->
    <div class="detail-list">
      <div class="detail-item" v-for="(item, index) in filteredRecords" :key="index">
        <div class="detail-header">
          <div class="detail-title">{{ getTypeText(item.type) }}</div>
          <div class="detail-amount" :class="getAmountClass(item.type)">{{ getAmountPrefix(item.type) }}{{ item.amount
            }} <span class="unit-icon">积分</span></div>
        </div>
        <div class="detail-content">
          <div class="detail-desc" v-if="item.details">{{ item.details }}</div>
          <div class="detail-time">{{ item.create_time }}</div>
        </div>
      </div>

      <!-- 空状态 -->
      <div class="empty-state" v-if="filteredRecords.length === 0 && !loading">
        <van-empty description="暂无积分记录" />
      </div>

      <!-- 加载状态 -->
      <div class="loading-state" v-if="loading">
        <van-loading type="spinner" color="#0474FC ">加载中...</van-loading>
      </div>

      <!-- 没有更多数据 -->
      <div class="no-more" v-if="finished && filteredRecords.length > 0">
        - 没有更多数据了 -
      </div>
    </div>

    <!-- 类型筛选弹窗 -->
    <van-popup v-model="showTypeFilter" position="bottom" round>
      <div class="filter-popup">
        <div class="popup-title">选择类型</div>
        <div class="filter-options">
          <div class="filter-option" v-for="(option, index) in typeOptions" :key="index"
            :class="{ active: selectedType === option.value }" @click="selectType(option.value)">
            {{ option.text }}
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
export default {
  name: 'IntegralDetail',
  beforeRouteEnter(to, from, next) {
    const token = localStorage.getItem('user_token')
    if (!token) {
      next('/login')
    } else {
      next()
    }
  },
  data() {
    return {
      showTypeFilter: false,
      selectedType: 'all',
      loading: false,
      finished: false,
      currentPage: 1,
      typeOptions: [
        { text: '全部', value: 'all' },
        { text: '获得积分', value: 'in' },
        { text: '消费积分', value: 'out' }
      ],
      records: [], typeMap: {
        // 获得积分类型 (根据API返回数据)
        10077: { text: '签到奖励', type: 'in' },
        10078: { text: '邀请奖励', type: 'in' },
        10079: { text: '完成任务', type: 'in' },
        10080: { text: '活动奖励', type: 'in' },
        10081: { text: '充值赠送', type: 'in' },
        10082: { text: '系统奖励', type: 'in' },
        10083: { text: '推广奖励', type: 'in' },
        10093: { text: '活动奖励', type: 'in' },

        // 消费积分类型
        20092: { text: '商品兑换', type: 'out' },
        20093: { text: '积分购买', type: 'out' },
        20094: { text: '服务消费', type: 'out' },
        20095: { text: '积分转赠', type: 'out' },
        20096: { text: '系统扣除', type: 'out' }
      }
    }
  },
  computed: {
    typeText() {
      const option = this.typeOptions.find(item => item.value === this.selectedType)
      return option ? option.text : '全部'
    }, filteredRecords() {
      let result = [...this.records]

      // 按类型筛选
      if (this.selectedType !== 'all') {
        result = result.filter(item => {
          const typeInfo = this.typeMap[item.type]
          if (typeInfo) {
            return typeInfo.type === this.selectedType
          }
          // 如果没有在typeMap中定义，根据type值判断
          const recordType = item.type >= 20000 ? 'out' : 'in'
          return recordType === this.selectedType
        })
      }

      return result
    }
  },
  created() {
    this.checkLoginAndLoadData()
  },
  mounted() {
    // 添加滚动事件监听
    window.addEventListener('scroll', this.handleScroll)
  },
  beforeDestroy() {
    // 移除滚动事件监听
    window.removeEventListener('scroll', this.handleScroll)
  }, methods: {
    checkLoginAndLoadData() {
      const token = localStorage.getItem('user_token')
      if (!token) {
        this.$toast('请先登录')
        this.$router.replace('/login')
        return
      }
      this.loadIntegralData()
    },
    goBack() {
      this.$router.go(-1)
    }, getTypeText(type) {
      const typeInfo = this.typeMap[type]
      if (typeInfo) {
        return typeInfo.text
      }
      // 为未定义的类型提供默认文本
      return type >= 20000 ? '积分消费' : '积分获得'
    }, getAmountClass(type) {
      const typeInfo = this.typeMap[type]
      if (typeInfo) {
        return typeInfo.type === 'out' ? 'amount-out' : 'amount-in'
      }
      // 如果没有在typeMap中定义，根据type值判断
      // 一般来说，20000开头的是消费，10000开头的是获得
      return type >= 20000 ? 'amount-out' : 'amount-in'
    },
    getAmountPrefix(type) {
      const typeInfo = this.typeMap[type]
      if (typeInfo) {
        return typeInfo.type === 'out' ? '-' : '+'
      }
      // 如果没有在typeMap中定义，根据type值判断
      return type >= 20000 ? '-' : '+'
    },
    selectType(type) {
      this.selectedType = type
      this.showTypeFilter = false
      // 不需要重新请求API，只使用前端过滤即可
    }, handleScroll() {
      // 获取页面高度、滚动位置和窗口高度
      const scrollHeight = document.documentElement.scrollHeight || document.body.scrollHeight
      const scrollTop = document.documentElement.scrollTop || document.body.scrollTop
      const clientHeight = document.documentElement.clientHeight || window.innerHeight

      // 当滚动到距离底部100px时加载更多数据，防止重复加载
      if (scrollHeight - scrollTop - clientHeight < 100 && !this.loading && !this.finished) {
        this.loadIntegralData()
      }
    }, async loadIntegralData() {
      if (this.loading || this.finished) return

      this.loading = true

      try {
        // 使用新的积分明细API端点
        const response = await this.$http.get(`/finance/getIntegralLog?page=${this.currentPage}`, {
          headers: {
            'authorization': localStorage.getItem('user_token') || ''
          }
        })

        if (response.data.code === 200) {
          const data = response.data.data || {}

          if (data.list && data.list.length > 0) {
            // 将新数据添加到列表
            this.records = [...this.records, ...data.list]
            this.currentPage++
          } else {
            // 如果当前页没有数据，则标记为加载完成
            this.finished = true
          }
        } else {
          this.$toast(response.data.msg || '获取积分明细失败')
          this.finished = true
        }
      } catch (error) {
        console.error('获取积分明细失败:', error)
        // 检查是否为401/403错误，表示token过期
        if (error.response && (error.response.status === 401 || error.response.status === 403)) {
          this.$toast('登录已过期，请重新登录')
          // 清除本地存储
          localStorage.removeItem('user_token')
          localStorage.removeItem('user_info')
          // 跳转到登录页
          this.$router.replace('/login')
        } else {
          this.$toast('网络错误，请稍后再试')
        }
        this.finished = true
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped>
.integral-detail {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 20px;
}

.header {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  background-color: #fff;
  position: relative;
}

.back-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.filter-section {
  display: flex;
  padding: 12px 15px;
  background: #fff;
  margin-bottom: 10px;
}

.filter-item {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #666;
  cursor: pointer;
}

.filter-item .van-icon {
  margin-left: 5px;
  font-size: 12px;
}

.detail-list {
  padding: 0 15px;
}

.detail-item {
  background: #fff;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.detail-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.detail-amount {
  font-size: 18px;
  font-weight: bold;
}

.amount-in {
  color: #0474FC;
}

.amount-out {
  color: #409EFF;
}

.unit-icon {
  font-size: 12px;
  margin-left: 2px;
  font-weight: normal;
}

.detail-content {
  display: flex;
  flex-direction: column;
}

.detail-desc {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.detail-time {
  font-size: 12px;
  color: #999;
}

.empty-state,
.loading-state {
  padding: 30px 0;
  text-align: center;
}

.no-more {
  text-align: center;
  font-size: 12px;
  color: #999;
  padding: 15px 0;
}

.filter-popup {
  padding: 20px 15px;
}

.popup-title {
  text-align: center;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 20px;
}

.filter-options {
  display: flex;
  flex-direction: column;
}

.filter-option {
  padding: 12px 0;
  text-align: center;
  font-size: 16px;
  color: #333;
  border-bottom: 1px solid #f5f5f5;
}

.filter-option.active {
  color: #0474FC;
  font-weight: bold;
}

.filter-option:last-child {
  border-bottom: none;
}
</style>
