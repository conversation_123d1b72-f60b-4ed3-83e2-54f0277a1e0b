<template>
  <div class="transaction-record">
    <!-- 头部 -->
    <div class="header">
      <div class="back-icon" @click="goBack">
        <img src="@/assets/icons/back-icon.svg" alt="返回">
      </div>
      <div class="title">交易记录</div>
    </div>

    <!-- 交易列表 -->
    <div class="transaction-list">
      <div class="transaction-item" v-for="(item, index) in records" :key="index">
        <div class="transaction-info">
          <div class="transaction-title">{{ item.name }}</div>
          <div class="transaction-details">{{ item.details }}</div>
          <div class="transaction-time">{{ item.create_time }}</div>
        </div>
        <div class="transaction-amount" :class="getAmountClass(item.type)">
          {{ getAmountText(item.type, item.amount) }}
        </div>
      </div>

      <!-- 加载更多 -->
      <div class="loading-more" v-if="loading">
        <van-loading size="24px" color="#FF618B">加载中...</van-loading>
      </div>

      <!-- 空状态 -->
      <div class="empty-state" v-if="records.length === 0 && !loading">
        <van-empty description="暂无交易记录" />
      </div>

      <!-- 没有更多数据 -->
      <div class="no-more" v-if="!hasMore && records.length > 0">
        没有更多数据了
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TransactionRecord',
  data() {
    return {
      page: 1,
      loading: false,
      hasMore: true,
      records: [],
      observerTarget: null
    }
  },
  created() {
    this.fetchTransactionLog()
  },
  mounted() {
    // 创建一个 IntersectionObserver 实例，用于检测列表底部并触发加载更多
    this.observerTarget = document.createElement('div')
    document.querySelector('.transaction-list').appendChild(this.observerTarget)
    
    const observer = new IntersectionObserver(entries => {
      // 当目标元素进入视口时，自动加载更多数据
      if (entries[0].isIntersecting && this.hasMore && !this.loading) {
        this.loadMore()
      }
    }, { threshold: 0.1 })
    
    observer.observe(this.observerTarget)
    
    // 记录观察器以便在组件销毁时断开连接
    this.observer = observer
  },
  beforeDestroy() {
    // 清理 IntersectionObserver
    if (this.observer && this.observerTarget) {
      this.observer.unobserve(this.observerTarget)
      this.observer.disconnect()
    }
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    async fetchTransactionLog() {
      try {
        this.loading = true
        const response = await this.$http.get(`/finance/getLog?page=${this.page}`, {
          headers: {
            'authorization': localStorage.getItem('user_token') || ''
          }
        })
        if (response.data.code === 200) {
          const data = response.data.data
          
          // 添加新数据到列表
          if (data && data.list && Array.isArray(data.list)) {
            this.records = [...this.records, ...data.list]
            
            // 判断是否还有更多数据
            if (data.list.length < 10 || !data.list.length) {
              this.hasMore = false
            }
          } else {
            console.error('API返回数据格式不正确', data)
            this.hasMore = false
          }
        } else {
          this.$toast(response.data.msg || '获取数据失败')
        }
      } catch (error) {
        console.error('获取交易记录失败:', error)
        this.$toast('获取数据失败，请稍后再试')
      } finally {
        this.loading = false
      }
    },
    loadMore() {
      if (!this.loading && this.hasMore) {
        this.page += 1
        this.fetchTransactionLog()
      }
    },
    getAmountClass(type) {
      // 投资项目为支出，其他为收入
      if (type === 21001) {
        return 'expense'
      }
      return 'income'
    },
    getAmountText(type, amount) {
      // 投资项目显示-，其他显示+
      if (type === 21001) {
        return `-${amount}元`
      }
      return `+${amount}元`
    }
  }
}
</script>

<style scoped>
.transaction-record {
  min-height: 100vh;
  background-color: #f6f6f6;
}

.header {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  background-color: #fff;
  position: relative;
}

.back-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon img {
  width: 20px;
  height: 20px;
}

.title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

.transaction-list {
  padding: 0 16px;
  padding-bottom: 50px;
}

.transaction-item {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 10px;
}

.transaction-info {
  flex: 1;
}

.transaction-title {
  font-size: 16px;
  color: #333;
  margin-bottom: 3px;
}

.transaction-details {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.transaction-time {
  font-size: 12px;
  color: #999;
}

.transaction-amount {
  font-size: 16px;
  font-weight: 500;
}

.transaction-amount.income {
  color: #FF618B;
}

.transaction-amount.expense {
  color: #999;
}

.empty-state {
  padding: 50px 0;
}

.loading-more {
  text-align: center;
  padding: 15px 0;
}

.no-more {
  text-align: center;
  color: #999;
  font-size: 12px;
  padding: 15px 0;
}
</style>
