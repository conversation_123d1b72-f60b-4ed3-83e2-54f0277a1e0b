<template>
  <div class="mall">
    <div class="header">
      <span class="title">商城</span>
      <div class="search-box">
        <van-icon name="search" color="#999" size="18" />
        <span class="search-placeholder">搜索商品</span>
      </div>
    </div>

    <!-- Banner -->
    <div class="banner">
      <img src="@/assets/images/banner.jpg" alt="商城banner">
    </div>

    <!-- 分类导航 -->
    <div class="category-nav">
      <div class="category-item">
        <div class="category-icon">
          <van-icon name="shop-o" color="#FF618B" size="24" />
        </div>
        <div class="category-name">全部商品</div>
      </div>
      <div class="category-item">
        <div class="category-icon">
          <van-icon name="gift-o" color="#FF618B" size="24" />
        </div>
        <div class="category-name">积分兑换</div>
      </div>
      <div class="category-item">
        <div class="category-icon">
          <van-icon name="fire-o" color="#FF618B" size="24" />
        </div>
        <div class="category-name">热销榜</div>
      </div>
      <div class="category-item">
        <div class="category-icon">
          <van-icon name="new-arrival-o" color="#FF618B" size="24" />
        </div>
        <div class="category-name">新品上市</div>
      </div>
    </div>

    <!-- 商品列表 -->
    <div class="product-section">
      <div class="section-header">
        <div class="section-title">热销商品</div>
        <div class="view-more">
          <span>查看更多</span>
          <van-icon name="arrow" />
        </div>
      </div>

      <div class="product-list">
        <div class="product-item" v-for="(product, index) in hotProducts" :key="index">
          <div class="product-image">
            <img :src="product.image" alt="商品图片">
          </div>
          <div class="product-info">
            <div class="product-name">{{ product.name }}</div>
            <div class="product-price">
              <span class="price-symbol">￥</span>
              <span class="price-value">{{ product.price }}</span>
            </div>
            <div class="product-sales">销量 {{ product.sales }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 商品推荐 -->
    <div class="product-section">
      <div class="section-header">
        <div class="section-title">商品推荐</div>
        <div class="view-more">
          <span>查看更多</span>
          <van-icon name="arrow" />
        </div>
      </div>

      <div class="product-list">
        <div class="product-item" v-for="(product, index) in newProducts" :key="index">
          <div class="product-image">
            <img :src="product.image" alt="商品图片">
            <div class="product-tag" v-if="product.isNew">新品</div>
          </div>
          <div class="product-info">
            <div class="product-name">{{ product.name }}</div>
            <div class="product-price">
              <span class="price-symbol">￥</span>
              <span class="price-value">{{ product.price }}</span>
            </div>
            <div class="product-sales">销量 {{ product.sales }}</div>
          </div>
        </div>
      </div>
    </div>

    <tab-bar :active="1"></tab-bar>
  </div>
</template>

<script>
import TabBar from '@/components/TabBar.vue'

export default {
  name: 'Mall',
  components: {
    TabBar
  },
  data() {
    return {
      hotProducts: [
        {
          id: 1,
          name: '中华蜂蜜礼盒装 纯天然时令蜂蜜',
          price: 99.9,
          sales: 1234,
          image: require('@/assets/images/product1.jpg')
        },
        {
          id: 2,
          name: '农家散养蛋 30枚 散养走地鸡蛋',
          price: 49.9,
          sales: 865,
          image: require('@/assets/images/product2.jpg')
        },
        {
          id: 3,
          name: '中华大米 5kg装 生态种植',
          price: 79.9,
          sales: 763,
          image: require('@/assets/images/product1.jpg')
        },
        {
          id: 4,
          name: '有机蔬菜礼盒 应季新鲜蔬菜',
          price: 158,
          sales: 542,
          image: require('@/assets/images/product2.jpg')
        }
      ],
      newProducts: [
        {
          id: 101,
          name: '山茶油 500ml 物理压榨一级',
          price: 128,
          sales: 324,
          image: require('@/assets/images/product1.jpg'),
          isNew: true
        },
        {
          id: 102,
          name: '红薯粉条 传统手工制作',
          price: 38.8,
          sales: 156,
          image: require('@/assets/images/product2.jpg'),
          isNew: true
        },
        {
          id: 103,
          name: '菌菇礼盒 香菇木耳组合装',
          price: 88,
          sales: 237,
          image: require('@/assets/images/product1.jpg'),
          isNew: true
        },
        {
          id: 104,
          name: '土蜂蜜 500g装 深山野生',
          price: 118,
          sales: 198,
          image: require('@/assets/images/product2.jpg'),
          isNew: true
        }
      ]
    }
  }
}
</script>

<style scoped>
.mall {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 50px;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  background-color: #fff;
}

.header .title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  padding: 5px 12px;
  border-radius: 16px;
  flex: 1;
  max-width: 200px;
  margin-left: 10px;
}

.search-placeholder {
  color: #999;
  font-size: 14px;
  margin-left: 5px;
}

.banner {
  padding: 0 15px;
  margin: 15px 0;
}

.banner img {
  width: 100%;
  height: 120px;
  object-fit: cover;
  border-radius: 8px;
}

.category-nav {
  display: flex;
  justify-content: space-between;
  padding: 15px;
  background-color: #fff;
  margin-bottom: 10px;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.category-icon {
  width: 50px;
  height: 50px;
  background: rgba(255, 97, 139, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 5px;
}

.category-name {
  font-size: 12px;
  color: #333;
}

.product-section {
  background-color: #fff;
  margin-bottom: 10px;
  padding: 15px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  position: relative;
  padding-left: 10px;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background: linear-gradient(to bottom, #FF618B, #FF8C9F);
  border-radius: 2px;
}

.view-more {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #999;
}

.view-more .van-icon {
  font-size: 12px;
  margin-left: 2px;
}

.product-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 10px;
}

.product-item {
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.product-image {
  position: relative;
  height: 150px;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-tag {
  position: absolute;
  top: 5px;
  right: 5px;
  background-color: #FF618B;
  color: #fff;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
}

.product-info {
  padding: 8px;
}

.product-name {
  font-size: 14px;
  color: #333;
  line-height: 1.4;
  height: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  margin-bottom: 5px;
}

.product-price {
  font-size: 16px;
  font-weight: bold;
  color: #FF618B;
  margin-bottom: 5px;
}

.price-symbol {
  font-size: 12px;
  font-weight: normal;
}

.product-sales {
  font-size: 12px;
  color: #999;
}
</style>
