<template>
  <div class="withdraw">
    <!-- 头部 -->
    <div class="header">
      <div class="back-icon" @click="goBack">
        <img src="@/assets/icons/back-icon.svg" alt="返回">
      </div>
      <div class="title">提现</div>
      <div class="right-icon" @click="goToWithdrawDetail">
        <span>明细</span>
      </div>
    </div>

    <!-- 账户余额卡片 -->
    <div class="balance-card">
      <div class="balance-card-inner">
        <div class="wallet-icon">
          <img src="@/assets/images/recharge.png" alt="钱包">
        </div>
        <div class="balance-info">
          <div class="balance-label">账户余额</div>
          <div class="balance-amount">{{ balance }}</div>
        </div>
        <div class="available-balance">
          <div class="available-label">可提现金额</div>
          <div class="available-amount">{{ availableBalance }}</div>
        </div>
      </div>
    </div>

    <!-- 提现表单 -->
    <div class="withdraw-form">
      <!-- 提现金额输入 -->
      <div class="input-item">
        <input type="number" v-model="amount" placeholder="输入提现金额" class="form-input">
      </div>

      <!-- 资金密码输入 -->
      <div class="input-item" v-if="hasPasswordSet"> <input type="password" v-model="fundPassword" placeholder="请输入资金密码"
          class="form-input">
      </div>

      <!-- 提现到银行卡 -->
      <div class="input-item bank-select" @click="handleBankCardClick">
        <div class="bank-label">提现到</div>
        <div class="bank-value">
          <template v-if="bankInfo">
            <div class="bank-card-preview">
              <div class="bank-icon">
                <img src="@/assets/images/avatar.jpg" alt="银行图标">
              </div>
              <span class="selected-card-info">{{ bankInfo.bank_name }} {{ formatCardNumber(bankInfo.bank_card)
              }}</span>
            </div>
          </template>
          <template v-else>
            <span class="no-card-tip">请绑定银行信息</span>
          </template>
          <svg width="7" height="12" viewBox="0 0 6 12" fill="none" xmlns="http://www.w3.org/2000/svg"
            class="arrow-icon">
            <path d="M1 1L5 6L1 11" stroke="#999999" stroke-width="1.5" stroke-linecap="round"
              stroke-linejoin="round" />
          </svg>
        </div>
      </div>
    </div>

    <!-- 提现说明 -->
    <div class="withdraw-notes">
      <div class="notes-header">
        <div class="notes-icon">
          <svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M11 21C16.5228 21 21 16.5228 21 11C21 5.47715 16.5228 1 11 1C5.47715 1 1 5.47715 1 11C1 16.5228 5.47715 21 11 21Z"
              stroke="#0474FC " stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
            <path d="M11 15V11" stroke="#0474FC " stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
            <path d="M11 7H11.01" stroke="#0474FC " stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
          </svg>
        </div>
        <div class="notes-title">提现说明</div>
      </div>
      <div class="notes-content">
        <div v-for="(rule, index) in withdrawRules" :key="index">
          {{ rule }}
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="confirm-btn" @click="confirmWithdraw">
      确认提现
    </div>

    <!-- 不需要银行卡列表弹窗 -->
  </div>
</template>

<script>

export default {
  name: 'Withdraw',
  data() {
    return {
      balance: '0.00',
      availableBalance: '0.00',
      amount: '',
      fundPassword: '',
      bankInfo: null,
      withdrawRules: [],
      hasPasswordSet: false
    }
  },
  created() {
    this.fetchWithdrawData();
    this.fetchBankInfo();
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    goToWithdrawDetail() {
      this.$router.push('/withdraw-detail')
    },
    // 获取提现相关数据
    async fetchWithdrawData() {
      try {
        const response = await this.$http.get('/finance/getWithdraw', {
          headers: {
            'authorization': localStorage.getItem('user_token') || ''
          }
        });

        if (response.data.code === 200) {
          const data = response.data.data;
          if (data) {
            this.balance = data.info.balance || '0.00';
            this.availableBalance = data.info.withdraw_can || '0.00';
            this.withdrawRules = data.des || [];
            this.hasPasswordSet = data.info.password === true;
          }
        } else {
          this.$toast(response.data.msg || '获取提现信息失败');
        }
      } catch (error) {
        console.error('获取提现信息失败:', error);
        this.$toast('获取提现信息失败，请稍后再试');
      }
    },
    // 获取银行卡信息
    async fetchBankInfo() {
      try {
        const response = await this.$http.get('/member/getBank', {
          headers: {
            'authorization': localStorage.getItem('user_token') || ''
          }
        });

        if (response.data.code === 200) {
          const bankData = response.data.data;
          if (bankData && bankData.info) {
            this.bankInfo = bankData.info;
          }
        }
      } catch (error) {
        console.error('获取银行卡信息失败', error);
      }
    },
    handleBankCardClick() {
      if (!this.bankInfo) {
        // 如果没有绑定银行卡，跳转到银行卡绑定页面
        this.$router.push('/bank-card/bind');
      }
    },
    formatCardNumber(number) {
      if (!number) return '';
      // 仅显示后四位，前面用星号代替
      const lastFour = number.slice(-4);
      const hiddenPart = number.slice(0, -4).replace(/\d/g, '*');
      return hiddenPart.replace(/(.{4})/g, '$1 ').trim() + ' ' + lastFour;
    },
    async confirmWithdraw() {
      if (!this.amount || parseFloat(this.amount) <= 0) {
        return this.$toast('请输入有效的提现金额');
      }

      if (parseFloat(this.amount) < 100) {
        return this.$toast('提现金额最少100元');
      }

      if (this.hasPasswordSet && !this.fundPassword) {
        return this.$toast('请输入资金密码');
      }

      if (!this.bankInfo) {
        this.$toast('请先绑定银行卡');
        setTimeout(() => {
          this.$router.push('/bank-card/bind');
        }, 1500);
        return;
      }

      try {
        const withdrawData = {
          amount: this.amount,
          password: this.fundPassword
        };

        const response = await this.$http.post('/finance/doWithdraw', withdrawData, {
          headers: {
            'authorization': localStorage.getItem('user_token') || ''
          }
        });

        if (response.data.code === 200) {
          this.$toast.success('提现申请已提交');
          setTimeout(() => {
            this.goBack();
          }, 1500);
        } else {
          this.$toast(response.data.msg || '提现失败');
        }
      } catch (error) {
        console.error('提现失败:', error);
        this.$toast('提现失败，请稍后再试');
      }
    }
  }
}
</script>

<style scoped>
.withdraw {
  min-height: 100vh;
  background-color: #F8F9FA;
  padding-bottom: 80px;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 16px;
  background-color: #fff;
  position: relative;
}

.back-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon img {
  width: 9.6px;
  height: 16.5px;
}

.title {
  font-size: 17px;
  font-weight: 400;
  color: #111827;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.right-icon {
  font-size: 14px;
  color: #666;
}

/* 账户余额卡片 */
.balance-card {
  margin: 16px;
  height: 142px;
  position: relative;
  border-radius: 10px;
}

.balance-card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 10px;
  background: linear-gradient(180deg, #0474FC 0%, #2585EB 1%, #1F68D6 43%, #1869F5 79%, #0940A0 100%);
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-left: 20px;
  overflow: hidden;
}

.wallet-icon {
  width: 70px;
  height: 70px;
  margin-right: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.wallet-icon img {
  width: 100%;
  height: auto;
  object-fit: contain;
}

.balance-info {
  color: #FFFFFF;
}

.balance-label {
  font-size: 14px;
  font-weight: 400;
  margin-bottom: 3px;
}

.balance-amount {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 29px;
}

.available-balance {
  background-color: rgba(255, 247, 251, 0.5);
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 42px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.available-label {
  color: #FFFFFF;
  font-size: 14px;
  margin-right: 9px;
}

.available-amount {
  color: #FFFFFF;
  font-size: 16px;
  font-weight: 600;
}

/* 提现表单 */
.withdraw-form {
  margin: 16px;
  margin-top: 32px;
}

.input-item {
  background-color: #FFFFFF;
  border-radius: 8px;
  height: 39px;
  margin-bottom: 10px;
  position: relative;
}

.form-input {
  width: 100%;
  height: 100%;
  border: none;
  outline: none;
  padding: 0 16px;
  font-size: 14px;
  color: #333;
  border-radius: 8px;
}

.form-input::placeholder {
  color: #B2B2B9;
}

.bank-select {
  display: flex;
  align-items: center;
  padding: 0 16px;
  margin-top: 13px;
  height: 48px;
}

.bank-label {
  font-size: 14px;
  color: #000000;
  margin-right: 14px;
}

.bank-value {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  color: #B2B2B9;
  position: relative;
}

.bank-card-preview {
  display: flex;
  align-items: center;
  max-width: calc(100% - 20px);
  overflow: hidden;
}

.bank-icon {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  overflow: hidden;
  margin-right: 8px;
  flex-shrink: 0;
}

.bank-icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.selected-card-info {
  color: #333;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
}

.no-card-tip {
  color: #B2B2B9;
}

.arrow-icon {
  margin-left: 5px;
  flex-shrink: 0;
}

/* 提现说明 */
.withdraw-notes {
  background-color: #FFFFFF;
  margin: 16px;
  padding: 20px;
  border-radius: 9px;
  margin-top: 32px;
}

.notes-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.notes-icon {
  margin-right: 10px;
}

.notes-title {
  font-size: 16px;
  color: #333333;
}

.notes-content {
  font-size: 14px;
  color: #9E9E9E;
  line-height: 1.5;
}

/* 底部按钮 */
.confirm-btn {
  position: fixed;
  bottom: 12px;
  left: 62px;
  right: 62px;
  height: 40px;
  background: #0474FC;
  color: #fff;
  font-size: 15px;
  font-weight: 500;
  border-radius: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 银行卡列表弹窗 */
.bank-card-list {
  padding: 20px 16px;
}

.popup-title {
  text-align: center;
  font-size: 18px;
  font-weight: 500;
  color: #333;
  margin-bottom: 20px;
  position: relative;
}

.card-list {
  max-height: 60vh;
  overflow-y: auto;
}

.card-item {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.card-item:active {
  background-color: #f9f9f9;
}

.card-item::before {
  content: '';
  position: absolute;
  top: 50%;
  right: 15px;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  transition: all 0.3s ease;
}

.card-selected::before {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #0474FC;
  right: 8px;
}

.card-selected {
  border: 1px solid #0474FC;
  background-color: rgba(255, 97, 139, 0.05);
}

.card-selected .card-name {
  color: #0474FC;
}

.card-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  overflow: hidden;
  margin-right: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  flex-shrink: 0;
}

.card-icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.card-detail {
  flex: 1;
  overflow: hidden;
}

.card-name {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.card-number {
  font-size: 14px;
  color: #666;
  letter-spacing: 1px;
}

.card-select {
  margin-left: 10px;
  flex-shrink: 0;
}

.add-card-btn {
  margin-top: 15px;
  height: 44px;
  background-color: #fff;
  color: #0474FC;
  font-size: 15px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px dashed #0474FC;
  transition: all 0.3s ease;
}

.add-card-btn:active {
  background-color: rgba(255, 97, 139, 0.05);
}

.add-card-btn .van-icon {
  margin-right: 8px;
}

.empty-card-list {
  padding: 30px 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 弹窗样式优化 */
:deep(.van-popup) {
  border-radius: 16px 16px 0 0;
  max-height: 80vh;
  overflow: hidden;
}

:deep(.van-popup__close-icon) {
  color: #999;
  font-size: 20px;
  top: 16px;
  right: 16px;
}

:deep(.van-empty) {
  padding: 40px 0;
}

:deep(.van-empty__image) {
  width: 80px;
  height: 80px;
}

:deep(.van-empty__description) {
  color: #999;
  margin-top: 16px;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}

.loading-state p {
  margin-top: 16px;
  font-size: 14px;
  color: #999;
}
</style>
