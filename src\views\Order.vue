<template>
    <div class="order">
      <!-- 头部 -->
      <div class="header">
        <div class="back-icon" @click="goBack">
          <img src="@/assets/icons/back-icon.svg" alt="返回">
        </div>
        <div class="title">我的订单</div>
      </div>
  
      <!-- 订单状态选项 -->
      <div class="order-tabs">
        <div class="tab-item" :class="{ active: activeTab === 'all' }" @click="changeTab('all')">
          全部
        </div>
        <div class="tab-item" :class="{ active: activeTab === 'processing' }" @click="changeTab('processing')">
          处理中
        </div>
        <div class="tab-item" :class="{ active: activeTab === 'shipped' }" @click="changeTab('shipped')">
          已发货
        </div>
        <div class="tab-item" :class="{ active: activeTab === 'refund' }" @click="changeTab('refund')">
          退款
        </div>
      </div>
  
      <!-- 订单列表 -->
      <div class="order-list">
        <div class="order-item" v-for="(order, index) in filteredOrders" :key="index">
          <div class="order-header">
            <div class="order-number">订单号：{{ order.order_id }}</div>
            <div class="order-status" :class="getOrderStatus(order.status)">{{ getStatusText(getOrderStatus(order.status)) }}</div>
          </div>
          <div class="order-content" @click="viewOrderDetail(order)">
            <!-- 根据API返回的实际数据格式显示商品 -->
            <div class="product-item">
              <div class="product-image">
                <img :src="order.shop_pic" alt="商品图片">
              </div>
              <div class="product-info">
                <div class="product-name">{{ order.shop_name }}</div>
                <div class="product-bottom">
                  <div class="product-price">{{ order.price }} <span class="unit-icon">积分</span></div>
                  <div class="product-quantity">x{{ order.number }}</div>
                </div>
              </div>
            </div>
          </div>
          <div class="order-footer">
            <div class="order-total">
              共{{ order.number }}件商品 合计：
              <span class="total-price">{{ order.price }} <span class="unit-icon">积分</span></span>
            </div>
          </div>
        </div>
  
        <!-- 空状态 -->
        <div class="empty-state" v-if="filteredOrders.length === 0 && !loading">
          <van-empty description="暂无订单" />
        </div>
        
        <!-- 加载状态 -->
        <div class="loading-state" v-if="loading">
          <van-loading type="spinner" color="#FF618B">加载中...</van-loading>
        </div>
        
        <!-- 没有更多数据 -->
        <div class="no-more" v-if="finished && filteredOrders.length > 0">
          - 没有更多数据了 -
        </div>
      </div>
    </div>
  </template>
  
  <script>
  export default {
    name: 'Order',
    data() {
      return {
        activeTab: 'all',
        orders: [],
        loading: false,
        finished: false,
        currentPage: 1,
        statusMap: {
          1: 'processing', // 处理中
          2: 'shipped',    // 已发货
          3: 'refund'      // 退款
        }
      }
    },
    computed: {
      filteredOrders() {
        if (this.activeTab === 'all') {
          return this.orders
        }
        return this.orders.filter(order => this.getOrderStatus(order.status) === this.activeTab)
      }
    },
    created() {
      this.loadOrderData()
    },
    mounted() {
      // 添加滚动事件监听
      window.addEventListener('scroll', this.handleScroll)
    },
    beforeDestroy() {
      // 移除滚动事件监听
      window.removeEventListener('scroll', this.handleScroll)
    },
    methods: {
      goBack() {
        this.$router.go(-1)
      },
      getOrderStatus(statusCode) {
        return this.statusMap[statusCode] || 'pending'
      },
      getStatusText(status) {
        const textMap = {
          processing: '处理中',
          shipped: '已发货',
          refund: '退款'
        }
        return textMap[status] || status
      },
  
      viewOrderDetail(order) {
        // 查看订单详情
        this.$toast(`查看订单详情：${order.order_id}`)
        // this.$router.push(`/order/detail?id=${order.order_id}`)
      },
      
      // 加载订单数据
      loadOrderData() {
        if (this.finished || this.loading) return
        
        this.loading = true
        
        // 从localStorage获取token
        const userToken = localStorage.getItem('user_token')
        
        if (!userToken) {
          this.$toast('请先登录')
          this.$nextTick(() => { this.$router.replace('/login') })
          this.loading = false
          return
        }
        
        // 调用API接口获取订单列表
        this.$http.get(`/order/getShopList?page=${this.currentPage}`, {
          headers: {
            'authorization': userToken
          }
        })
          .then(response => {
            if (response.data.code === 200) {
              const data = response.data.data
              
              // 处理订单列表数据
              if (data.list && data.list.length > 0) {                // 更新订单直接添加到订单列表（API返回的数据结构已经是我们需要的）
                this.orders = [...this.orders, ...data.list]
                this.currentPage++
              } else {
                // 如当前页没有数据，标记为加载完成
                this.finished = true
              }
            } else {
              this.$toast(response.data.msg || '获取订单列表失败')
              this.finished = true
            }
            
            this.loading = false
          })
          .catch(error => {
            console.error('获取订单列表失败:', error)
            this.$toast('网络异常，请稍后重试')
            this.loading = false
            this.finished = true
          })
      },
      
      // 处理滚动事件
      handleScroll() {
        // 获取页面高度、滚动位置和窗口高度
        const scrollHeight = document.documentElement.scrollHeight || document.body.scrollHeight
        const scrollTop = document.documentElement.scrollTop || document.body.scrollTop
        const clientHeight = document.documentElement.clientHeight || window.innerHeight
        
        // 当滚动接近底部100px时，加载更多
        if (scrollHeight - scrollTop - clientHeight < 100 && !this.loading && !this.finished) {
          this.loadOrderData()
        }
      },
      
      // 切换标签
      changeTab(tab) {
        if (this.activeTab === tab) return
        
        this.activeTab = tab
        this.orders = []
        this.currentPage = 1
        this.finished = false
        this.loadOrderData()
        
        // 滚动到顶部
        window.scrollTo(0, 0)
      }
    }
  }
  </script>
  
  <style scoped>
  .order {
    min-height: 100vh;
    background-color: #f6f6f6;
  }
  
  .header {
    display: flex;
    align-items: center;
    padding: 10px 16px;
    background-color: #fff;
    position: relative;
  }
  
  .back-icon {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .back-icon img {
    width: 20px;
    height: 20px;
  }
  
  .title {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    font-size: 18px;
    font-weight: 500;
    color: #333;
  }
  
  .order-tabs {
    display: flex;
    background-color: #fff;
    justify-content: space-around;
    padding: 12px 0;
    margin-bottom: 10px;
    position: sticky;
    top: 0;
    z-index: 10;
  }
  
  .tab-item {
    font-size: 14px;
    color: #666;
    position: relative;
    padding: 0 2px;
  }
  
  .tab-item.active {
    color: #FF618B;
    font-weight: 500;
  }
  
  .tab-item.active::after {
    content: "";
    position: absolute;
    bottom: -6px;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 2px;
    background-color: #FF618B;
    border-radius: 1px;
  }
  
  .order-list {
    padding: 0 15px;
  }
  
  .order-item {
    background-color: #fff;
    border-radius: 8px;
    margin-bottom: 10px;
    overflow: hidden;
  }
  
  .order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    border-bottom: 1px solid #f5f5f5;
  }
  
  .order-number {
    font-size: 13px;
    color: #999;
  }
  
  .order-status {
    font-size: 13px;
  }
  
  .order-status.processing {
    color: #2196f3;
  }
  
  .order-status.shipped {
    color: #9c27b0;
  }
  
  .order-status.refund {
    color: #ff5252;
  }
  
  .order-content {
    padding: 15px;
  }
  
  .product-item {
    display: flex;
    margin-bottom: 10px;
  }
  
  .product-item:last-child {
    margin-bottom: 0;
  }
  
  .product-image {
    width: 80px;
    height: 80px;
    border-radius: 4px;
    overflow: hidden;
    margin-right: 10px;
  }
  
  .product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .product-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  
  .product-name {
    font-size: 14px;
    color: #333;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    line-clamp: 2;
  }
  
  .product-spec {
    font-size: 12px;
    color: #999;
    margin: 4px 0;
  }
  
  .product-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .product-price {
    font-size: 14px;
    color: #FF618B;
    font-weight: 500;
    display: flex;
    align-items: center;
  }
  
  .unit-icon {
    font-size: 12px;
    margin-left: 2px;
    font-weight: normal;
  }
  
  .product-quantity {
    font-size: 12px;
    color: #999;
  }
  
  .order-footer {
    padding: 12px 15px;
    padding-bottom: 20px;
    border-top: 1px solid #f5f5f5;
  }
  
  .order-total {
    text-align: right;
    font-size: 13px;
    color: #666;
    margin-bottom: 12px;
  }
  
  .total-price {
    color: #FF618B;
    font-weight: 500;
  }
  
  .empty-state {
    padding: 40px 0;
    text-align: center;
  }
  
  .loading-state {
    padding: 20px 0;
    text-align: center;
  }
  
  .no-more {
    padding: 15px 0;
    text-align: center;
    color: #999;
    font-size: 12px;
  }
  </style>
  