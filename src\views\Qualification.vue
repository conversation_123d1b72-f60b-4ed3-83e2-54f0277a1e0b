<template>
    <div class="qualification">
      <!-- 头部 -->
      <div class="header">
        <div class="back-icon" @click="goBack">
          <img src="@/assets/icons/back-icon.svg" alt="返回">
        </div>
        <div class="title">平台资质</div>
      </div>
  
      <!-- 平台信息 -->
      <div class="platform-info">
        <div class="platform-logo">
          <img src="@/assets/images/avatar.jpg" alt="平台logo">
        </div>
        <div class="platform-name">农产品电商平台</div>
        <div class="platform-slogan">让农产品走向世界</div>
      </div>
  
      <!-- 资质证书 -->
      <div class="certificate-section">
        <div class="section-title">资质证书</div>
        <div class="certificate-list">
          <div class="certificate-item" v-for="(item, index) in certificates" :key="index"
            @click="previewImage(item.image)">
            <div class="certificate-image">
              <img :src="item.image" alt="证书图片">
            </div>
            <div class="certificate-name">{{ item.name }}</div>
          </div>
        </div>
      </div>
  
      <!-- 公司信息 -->
      <div class="company-info">
        <div class="section-title">公司信息</div>
        <div class="info-list">
          <div class="info-item">
            <div class="info-label">公司名称</div>
            <div class="info-value">农产品科技有限公司</div>
          </div>
          <div class="info-item">
            <div class="info-label">统一社会信用代码</div>
            <div class="info-value">91110105MA00XXXXX6B</div>
          </div>
          <div class="info-item">
            <div class="info-label">经营类型</div>
            <div class="info-value">电商</div>
          </div>
          <div class="info-item">
            <div class="info-label">注册资本</div>
            <div class="info-value">1000万元人民币</div>
          </div>
          <div class="info-item">
            <div class="info-label">成立日期</div>
            <div class="info-value">2018-01-01</div>
          </div>
          <div class="info-item">
            <div class="info-label">经营范围</div>
            <div class="info-value">互联网信息服务；计算机软硬件及网络设备开发、销售；食品；农产品销售等</div>
          </div>
        </div>
      </div>
  
      <!-- 联系方式 -->
      <div class="contact-info">
        <div class="section-title">联系方式</div>
        <div class="info-list">
          <div class="info-item">
            <div class="info-label">客服电话</div>
            <div class="info-value">400-123-4567</div>
          </div>
          <div class="info-item">
            <div class="info-label">客服邮箱</div>
            <div class="info-value"><EMAIL></div>
          </div>
          <div class="info-item">
            <div class="info-label">公司地址</div>
            <div class="info-value">北京市朝阳区xxx街道A座10层</div>
          </div>
          <div class="info-item">
            <div class="info-label">官方网站</div>
            <div class="info-value">www.example.com</div>
          </div>
        </div>
      </div>
  
      <!-- 底部版权 -->
      <div class="footer">
        <div class="copyright">© 2023 农产品科技有限公司 版权所有</div>
        <div class="icp">京ICP备12345678号-1</div>
      </div>
  
      <!-- 图片预览 -->
      <van-image-preview v-model="showImagePreview" :images="previewImages" :start-position="previewIndex" />
    </div>
  </template>
  
  <script>
  
  export default {
    name: 'Qualification',
    data() {
      return {
        certificates: [
          {
            name: '营业执照',
            image: require('@/assets/images/avatar.jpg')
          },
          {
            name: '食品经营许可证',
            image: require('@/assets/images/avatar.jpg')
          },
          {
            name: 'ICP备案证',
            image: require('@/assets/images/avatar.jpg')
          },
          {
            name: '增值电信业务经营许可证',
            image: require('@/assets/images/avatar.jpg')
          }
        ],
        showImagePreview: false,
        previewImages: [],
        previewIndex: 0
      }
    },
    created() {
      // 准备预览图片数组
      this.previewImages = this.certificates.map(item => item.image)
    },
    methods: {
      goBack() {
        this.$router.go(-1)
      },
      previewImage(image) {
        const index = this.previewImages.findIndex(item => item === image)
        if (index !== -1) {
          this.previewIndex = index
          this.showImagePreview = true
        }
      }
    }
  }
  </script>
  
  <style scoped>
  .qualification {
    min-height: 100vh;
    background-color: #f6f6f6;
  }
  
  .header {
    display: flex;
    align-items: center;
    padding: 10px 16px;
    background-color: #fff;
    position: relative;
  }
  
  .back-icon {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .back-icon img {
    width: 20px;
    height: 20px;
  }
  
  .title {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    font-size: 18px;
    font-weight: 500;
    color: #333;
  }
  
  .platform-info {
    background-color: #fff;
    padding: 30px 16px;
    text-align: center;
    margin-bottom: 10px;
  }
  
  .platform-logo {
    width: 80px;
    height: 80px;
    border-radius: 40px;
    overflow: hidden;
    margin: 0 auto 15px;
  }
  
  .platform-logo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .platform-name {
    font-size: 18px;
    font-weight: 500;
    color: #333;
    margin-bottom: 10px;
  }
  
  .platform-slogan {
    font-size: 14px;
    color: #999;
  }
  
  .certificate-section {
    background-color: #fff;
    padding: 20px 16px;
    margin-bottom: 10px;
  }
  
  .section-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 15px;
  }
  
  .certificate-list {
    display: flex;
    overflow-x: auto;
    padding-bottom: 10px;
  }
  
  .certificate-list::-webkit-scrollbar {
    display: none;
  }
  
  .certificate-item {
    min-width: 120px;
    margin-right: 15px;
  }
  
  .certificate-image {
    width: 120px;
    height: 160px;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 10px;
  }
  
  .certificate-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .certificate-name {
    font-size: 14px;
    color: #666;
    text-align: center;
  }
  
  .company-info,
  .contact-info {
    background-color: #fff;
    padding: 20px 16px;
    margin-bottom: 10px;
  }
  
  .info-list {
    display: flex;
    flex-direction: column;
  }
  
  .info-item {
    display: flex;
    padding: 10px 0;
    border-bottom: 1px solid #f5f5f5;
  }
  
  .info-item:last-child {
    border-bottom: none;
  }
  
  .info-label {
    width: 120px;
    font-size: 14px;
    color: #999;
  }
  
  .info-value {
    flex: 1;
    font-size: 14px;
    color: #333;
  }
  
  .footer {
    padding: 20px 16px;
    text-align: center;
  }
  
  .copyright {
    font-size: 12px;
    color: #999;
    margin-bottom: 5px;
  }
  
  .icp {
    font-size: 12px;
    color: #999;
  }
  </style>
  