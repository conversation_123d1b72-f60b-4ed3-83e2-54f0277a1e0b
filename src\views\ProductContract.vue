<template>
  <div class="product-contract">
    <!-- 头部 -->
    <div class="header">
      <div class="back-icon" @click="goBack">
        <img 
          src="@/assets/images/project-order/back-icon.svg" 
          @error="handleImageError($event, 'icon')" 
          alt="返回">
      </div>
      <div class="title">电子合同</div>
    </div>
    
    <!-- 合同内容 -->
    <div class="contract-content" v-if="contractInfo">
      <img 
        :src="contractInfo.guarantee_pic" 
        @error="handleImageError($event, 'contract')" 
        class="contract-image" 
        alt="电子合同">
    </div>
    
    <!-- 加载状态 -->
    <div class="loading-state" v-if="loading">
      <van-loading type="spinner" color="#FF618B">加载中...</van-loading>
    </div>
    
    <!-- 空状态 -->
    <div class="empty-state" v-if="!loading && !contractInfo">
      <van-empty description="暂无合同信息" />
    </div>
  </div>
</template>

<script>
import ImageErrorHandler from '@/utils/ImageErrorHandler'

export default {
  name: 'ProductContract',
  data() {
    return {
      id: null,
      loading: false,
      contractInfo: null
    }
  },
  created() {
    this.id = this.$route.query.id
    if (!this.id) {
      this.$toast('订单ID不存在')
      this.goBack()
      return
    }
    this.loadContractData()
  },
  methods: {
    // 处理图片加载错误
    handleImageError(event, type) {
      ImageErrorHandler.handleImageError(event, type)
    },
    
    // 返回上一页
    goBack() {
      this.$router.go(-1)
    },
    
    // 加载合同数据
    loadContractData() {
      this.loading = true
      
      // 从localStorage获取token
      const userToken = localStorage.getItem('user_token')
      
      if (!userToken) {
        this.$toast('请先登录')
        this.$nextTick(() => { this.$router.replace('/login') })
        this.loading = false
        return
      }
      
      // 发起API请求获取电子合同
      this.$http.get(`/order/getProductContract?id=${this.id}`, {
        headers: {
          'authorization': userToken
        }
      })
        .then(response => {
          if (response.data.code === 200) {
            const data = response.data.data
            
            if (data.info) {
              this.contractInfo = data.info
            }
          } else {
            this.$toast(response.data.msg || '获取电子合同失败')
          }
          
          this.loading = false
        })
        .catch(error => {
          console.error('获取电子合同失败:', error)
          this.$toast('网络异常，请稍后重试')
          this.loading = false
        })
    }
  }
}
</script>

<style scoped>
.product-contract {
  min-height: 100vh;
  background-color: #F8F9FA;
}

/* 头部样式 */
.header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background-color: #fff;
  position: relative;
}

.back-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon img {
  width: 10px;
  height: 17px;
}

.title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 17px;
  color: #111827;
}

/* 合同内容 */
.contract-content {
  padding: 16px;
  display: flex;
  justify-content: center;
}

.contract-image {
  width: 100%;
  max-width: 100%;
  height: auto;
}

/* 加载状态 */
.loading-state {
  padding: 50px 0;
  text-align: center;
}

/* 空状态 */
.empty-state {
  padding: 50px 0;
  display: flex;
  justify-content: center;
}
</style>
