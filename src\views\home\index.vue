<template>
  <div class="home-page">
    <!-- 背景装饰 -->
    <div class="background">
      <div class="background-rect"></div>
      <div class="ellipse ellipse-1"></div>
      <div class="ellipse ellipse-2"></div>
    </div>

    <!-- 头部区域 -->
    <div class="header">
      <div class="logo-section">
        <div class="logo">
          <div class="logo-icon"></div>
        </div>
        <div class="brand-text">
          <span class="brand-name">农产品安心购</span>
        </div>
      </div>
      <div class="notification-icon">
        <img src="@/assets/images/figma/home/<USER>" alt="通知">
      </div>
    </div>

    <!-- 广告轮播 -->
    <div class="banner-section">
      <div class="banner-container">
        <img src="@/assets/images/figma/home/<USER>" alt="Banner" class="banner-image">
      </div>
      <div class="banner-indicator">
        <div class="indicator active"></div>
        <div class="indicator"></div>
        <div class="indicator"></div>
      </div>
    </div>

    <!-- 功能菜单区域 -->
    <div class="function-menu">
      <div class="menu-item">
        <div class="menu-icon sign-in"></div>
        <span class="menu-text">签到</span>
      </div>
      <div class="menu-item">
        <div class="menu-icon points"></div>
        <span class="menu-text">积分</span>
      </div>
      <div class="menu-item">
        <div class="menu-icon news"></div>
        <span class="menu-text">资讯</span>
      </div>
      <div class="menu-item">
        <div class="menu-icon faq"></div>
        <span class="menu-text">常见问题</span>
      </div>
      <div class="menu-item">
        <div class="menu-icon about"></div>
        <span class="menu-text">关于我们</span>
      </div>
    </div>

    <!-- 通知栏 -->
    <div class="notification-bar">
      <div class="notification-icon">
        <img src="@/assets/images/figma/home/<USER>" alt="通知" class="notification-image">
      </div>
      <div class="notification-text">
        Thế giới nông sản sạch - Cửa hàng bán thực phẩm
      </div>
    </div>

    <!-- 活动卡片区域 -->
    <div class="activity-section">
      <div class="activity-card main-card">
        <img src="@/assets/images/figma/home/<USER>" alt="活动卡片" class="card-image">
        <div class="card-content">
          <h3 class="card-title">Giới thiệu cấp độ</h3>
          <p class="card-description">Xem chi tiết cấp độ</p>
          <div class="card-button">Đi vào</div>
        </div>
      </div>

      <div class="small-cards-container">
        <div class="activity-card small-card">
          <img src="@/assets/images/figma/home/<USER>" alt="积分卡片" class="card-image">
          <div class="card-content">
            <h3 class="card-title">Tích phân</h3>
            <p class="card-description">Quà tặng đổi điểm</p>
            <div class="card-button blue-button">Đi vào</div>
          </div>
        </div>

        <div class="activity-card small-card">
          <img src="@/assets/images/figma/home/<USER>" alt="邀请卡片" class="card-image">
          <div class="card-content">
            <h3 class="card-title">Mời bạn bè</h3>
            <p class="card-description">Mời bạn bè để nhận được lợi ích</p>
            <div class="card-button">Đi vào</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 最近活动区域 -->
    <div class="recent-activity">
      <div class="section-header">
        <h2 class="section-title">Hoạt động mới nhất</h2>
        <span class="view-more">Hơn</span>
      </div>
      <div class="activity-content-list">
        <div class="activity-content-item">
          <img src="@/assets/images/figma/home/<USER>" alt="活动内容" class="content-image">
          <div class="content-text">Nội dung hoạt động</div>
        </div>
        <div class="activity-content-item">
          <img src="@/assets/images/figma/home/<USER>" alt="活动内容" class="content-image">
          <div class="content-text">Nội dung hoạt động</div>
        </div>
      </div>
    </div>

    <!-- GIF 横幅 -->
    <div class="gif-banner">
      <img src="@/assets/images/figma/home/<USER>" alt="GIF Banner" class="gif-banner-image">
    </div>

    <!-- 热门信息区域 -->
    <div class="popular-news">
      <div class="section-header">
        <h2 class="section-title">Thông tin phổ biến</h2>
        <span class="view-more">Hơn</span>
      </div>
      <div class="news-list">
        <div class="news-item">
          <div class="news-content">
            <div class="news-title">Bộ trưởng Lê Minh Hoan: Đưa nông sản Việt vươn tầm, khai phá nhữn...</div>
            <div class="news-date">23-05-2025</div>
          </div>
          <img src="@/assets/images/figma/home/<USER>" alt="News" class="news-image">
        </div>
        <div class="divider"></div>

        <div class="news-item">
          <div class="news-content">
            <div class="news-title">Bộ trưởng Lê Minh Hoan: Đưa nông sản Việt vươn tầm, khai phá nhữn...</div>
            <div class="news-date">23-05-2025</div>
          </div>
          <img src="@/assets/images/figma/home/<USER>" alt="News" class="news-image">
        </div>
        <div class="divider"></div>

        <div class="news-item">
          <div class="news-content">
            <div class="news-title">Mời tham gia phiên chợ tuần Nông sản An toàn thực phẩm 2020 tại ...</div>
            <div class="news-date">23-05-2025</div>
          </div>
          <img src="@/assets/images/figma/home/<USER>" alt="News" class="news-image">
        </div>
        <div class="divider"></div>

        <div class="news-item">
          <div class="news-content">
            <div class="news-title">Xung quanh chuyện "giải cứu nông sản" trong mùa dịch</div>
            <div class="news-date">23-05-2025</div>
          </div>
          <img src="@/assets/images/figma/home/<USER>" alt="News" class="news-image">
        </div>
      </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="tab-bar">
      <div class="tab-item active">
        <div class="tab-icon home-icon"></div>
        <span class="tab-text">首页</span>
      </div>
      <div class="tab-item">
        <div class="tab-icon trade-icon"></div>
        <span class="tab-text">贸易</span>
      </div>
      <div class="tab-item">
        <div class="tab-icon customer-icon"></div>
        <span class="tab-text">客服</span>
      </div>
      <div class="tab-item">
        <div class="tab-icon mine-icon"></div>
        <span class="tab-text">我的</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'HomePage',
  data() {
    return {
      // 可以在这里添加数据
    }
  },
  mounted() {
    // 页面加载后的逻辑
  },
  methods: {
    // 可以在这里添加方法
  }
}
</script>

<style scoped>
.home-page {
  position: relative;
  width: 100%;
  min-height: 100vh;
  background-color: #fff;
  overflow-x: hidden;
  font-family: 'Urbanist', sans-serif;
}

/* 背景样式 */
.background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.background-rect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 283px;
  background: linear-gradient(180deg, #50CF5D 0%, #9EE4B4 33.33%, #C9F0D5 66.67%, #FFFFFF 100%);
}

.ellipse {
  position: absolute;
  border-radius: 50%;
}

.ellipse-1 {
  top: -88px;
  left: -71px;
  width: 325px;
  height: 325px;
  background: linear-gradient(to bottom right, #12C665, rgba(244, 255, 159, 0.4), rgba(255, 255, 255, 0.09));
  filter: blur(50px);
}

.ellipse-2 {
  top: -101px;
  left: 98px;
  width: 419px;
  height: 419px;
  background: linear-gradient(to bottom left, #89DCF9, rgba(137, 220, 249, 0.35), rgba(255, 255, 255, 0.09));
  filter: blur(50px);
}

/* 头部样式 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 47px 19px 0;
  width: 100%;
  height: 32px;
  z-index: 1;
}

.logo-section {
  display: flex;
  align-items: center;
}

.logo {
  width: 32px;
  height: 32px;
  border-radius: 100px;
  background-color: #fff;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.logo-icon {
  width: 32px;
  height: 32px;
  background-color: #6A401E;
  border-radius: 50%;
}

.brand-text {
  margin-left: 3px;
  color: #0B522C;
  font-weight: 500;
  font-size: 14px;
}

.notification-icon {
  width: 26px;
  height: 26px;
}

/* 广告轮播样式 */
.banner-section {
  margin: 20px 16px;
  position: relative;
}

.banner-container {
  width: 100%;
  height: 148px;
  border-radius: 10px;
  overflow: hidden;
}

.banner-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.banner-indicator {
  position: absolute;
  bottom: 8px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 14px;
}

.indicator {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.2);
}

.indicator.active {
  width: 14px;
  height: 6px;
  border-radius: 6px;
  background-color: rgba(0, 0, 0, 0.8);
}

/* 功能菜单样式 */
.function-menu {
  display: flex;
  justify-content: space-around;
  margin: 0 16px;
  padding: 15px 0;
  background-color: #fff;
  border-radius: 10px;
}

.menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.menu-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(to bottom, rgba(197, 239, 215, 0.29), rgba(38, 205, 152, 0.68));
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 5px;
}

.menu-text {
  color: #454545;
  font-size: 11px;
}

/* 通知栏样式 */
.notification-bar {
  margin: 15px 16px;
  padding: 0 15px;
  height: 40px;
  display: flex;
  align-items: center;
  background-color: rgba(235, 255, 244, 0.57);
  border-radius: 11px;
}

.notification-bar .notification-icon {
  width: 20px;
  height: 23px;
}

.notification-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.notification-text {
  margin-left: 6px;
  color: #A7A7A7;
  font-size: 12px;
  font-family: 'Roboto Condensed', sans-serif;
}

/* 活动卡片区域样式 */
.activity-section {
  margin: 15px 16px;
}

.activity-card {
  position: relative;
  overflow: hidden;
  border-radius: 10px;
}

.main-card {
  width: 100%;
  height: 94px;
  margin-bottom: 15px;
}

.small-cards-container {
  display: flex;
  gap: 11px;
}

.small-card {
  flex: 1;
  height: 93px;
}

.card-image {
  position: absolute;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.card-content {
  position: relative;
  z-index: 1;
  padding: 15px;
  color: white;
}

.card-title {
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 5px 0;
}

.card-description {
  font-size: 10px;
  font-weight: 500;
  margin: 0 0 7px 0;
  color: rgba(255, 255, 255, 0.8);
}

.card-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(to right, #69C80A, #02B460);
  border-radius: 4px;
  padding: 3px 13px;
  font-size: 12px;
  font-weight: 500;
  color: white;
}

.blue-button {
  background: linear-gradient(to right, #4385F5, #377BEF);
}

/* 最近活动区域样式 */
.recent-activity {
  margin: 15px 16px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 700;
  color: #000;
  margin: 0;
}

.view-more {
  font-size: 12px;
  font-weight: 500;
  color: #939393;
}

.activity-content-list {
  display: flex;
  gap: 14px;
  overflow-x: auto;
  padding-bottom: 10px;
}

.activity-content-item {
  position: relative;
  width: 262px;
  height: 114px;
  border-radius: 10px;
  overflow: hidden;
}

.content-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.content-text {
  position: absolute;
  bottom: 7px;
  left: 14px;
  color: white;
  font-size: 16px;
}

/* GIF 横幅样式 */
.gif-banner {
  margin: 15px 16px;
  height: 45px;
  border-radius: 5px;
  overflow: hidden;
}

.gif-banner-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 热门信息区域样式 */
.popular-news {
  margin: 15px 16px;
}

.news-list {
  display: flex;
  flex-direction: column;
}

.news-item {
  display: flex;
  justify-content: space-between;
  padding: 10px 0;
}

.news-content {
  flex: 1;
  margin-right: 10px;
}

.news-title {
  font-size: 14px;
  font-weight: 500;
  color: #454545;
  margin-bottom: 5px;
}

.news-date {
  font-size: 12px;
  color: #939393;
}

.news-image {
  width: 109px;
  height: 65px;
  border-radius: 11px;
  object-fit: cover;
}

.divider {
  height: 1px;
  background-color: #F2F2F2;
  width: 100%;
}

/* 底部导航栏样式 */
.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 70px;
  display: flex;
  justify-content: space-around;
  align-items: center;
  background-color: white;
  border-top: 1px solid #f5f5f5;
}

.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 63px;
  padding: 12px 0;
}

.tab-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 6px;
}

.home-icon {
  background-color: #26BF78;
}

.trade-icon, .customer-icon, .mine-icon {
  background-color: transparent;
  border: 1px solid #26BF78;
}

.tab-text {
  font-size: 10px;
  color: #26BF78;
}

.tab-item.active .tab-icon {
  background-color: #26BF78;
}

.tab-item.active .tab-text {
  color: #26BF78;
}

/* 适配不同尺寸的设备 */
@media screen and (max-width: 375px) {
  .function-menu .menu-text {
    font-size: 10px;
  }
  
  .activity-content-list {
    overflow-x: scroll;
  }
  
  .small-cards-container {
    flex-direction: column;
  }
  
  .small-card {
    width: 100%;
    margin-bottom: 10px;
  }
}

@media screen and (min-width: 376px) {
  .home-page {
    max-width: 500px;
    margin: 0 auto;
  }
  
  .tab-bar {
    max-width: 500px;
  }
}
</style>