<template>
  <div class="status-bar">
    <div class="left">
      <div class="time">{{ currentTime }}</div>
    </div>
    <div class="right">
      <div class="signal">
        <svg width="17" height="11" viewBox="0 0 17 11" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M1 8.5H2V9.5H1V8.5ZM4 6.5H5V9.5H4V6.5ZM7 4.5H8V9.5H7V4.5ZM10 2.5H11V9.5H10V2.5ZM13 0.5H14V9.5H13V0.5Z" fill="black"/>
        </svg>
      </div>
      <div class="wifi">
        <svg width="15" height="11" viewBox="0 0 15 11" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M7.5 2.5C9.8 2.5 11.9 3.4 13.5 5L15 3.5C13 1.6 10.4 0.5 7.5 0.5C4.6 0.5 2 1.6 0 3.5L1.5 5C3.1 3.4 5.2 2.5 7.5 2.5Z" fill="black"/>
          <path d="M7.5 6.5C8.7 6.5 9.8 6.9 10.6 7.7L12.1 6.2C10.9 5 9.3 4.5 7.5 4.5C5.7 4.5 4.1 5.1 2.9 6.2L4.4 7.7C5.2 6.9 6.3 6.5 7.5 6.5Z" fill="black"/>
          <path d="M7.5 10.5C8.3 10.5 9 9.8 9 9C9 8.2 8.3 7.5 7.5 7.5C6.7 7.5 6 8.2 6 9C6 9.8 6.7 10.5 7.5 10.5Z" fill="black"/>
        </svg>
      </div>
      <div class="battery">
        <svg width="24" height="11" viewBox="0 0 24 11" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect opacity="0.35" x="0.5" y="0.5" width="21" height="10" rx="2.16667" stroke="black"/>
          <path opacity="0.4" d="M23 4V7C23.8323 6.5982 24.2923 5.669 23.9952 4.8345C23.8353 4.32603 23.4684 3.9324 23 3.75V4Z" fill="black"/>
          <rect x="2" y="2" width="18" height="7" rx="1.33333" fill="black"/>
        </svg>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'StatusBar',
  data() {
    return {
      currentTime: '9:41'
    }
  },
  mounted() {
    this.updateTime();
    setInterval(this.updateTime, 60000);
  },
  methods: {
    updateTime() {
      const now = new Date();
      const hours = now.getHours();
      const minutes = now.getMinutes();
      this.currentTime = `${hours}:${minutes < 10 ? '0' + minutes : minutes}`;
    }
  }
}
</script>

<style scoped>
.status-bar {
  position: relative;
  height: 44px;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 100;
}

.left {
  display: flex;
  align-items: center;
}

.time {
  font-weight: 700;
  font-size: 15px;
  line-height: 15px;
  letter-spacing: -0.3px;
}

.right {
  display: flex;
  align-items: center;
}

.signal {
  margin-right: 5px;
}

.wifi {
  margin-right: 5px;
}

.battery {
  margin-left: 5px;
}
</style>