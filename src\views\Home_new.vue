<template>
  <div class="home">
    <!-- 顶部背景图 -->
    <div class="top-bg">
      <img src="@/assets/images/home/<USER>" alt="首页背景" />
    </div>

    <!-- 大图banner区域 -->
    <div class="main-banner">
      <div class="banner-image">
        <img src="@/assets/images/home/<USER>" alt="主要宣传图" />
      </div>
    </div>

    <!-- 功能快捷入口 -->
    <div class="quick-actions">
      <div class="action-item" @click="goToPage('/sign-in')">
        <div class="action-icon">
          <div class="icon-bg gradient-yellow">
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path d="M10 2L12 6L16 6L12.5 9L14 13L10 10.5L6 13L7.5 9L4 6L8 6L10 2Z" fill="white" />
            </svg>
          </div>
        </div>
        <div class="action-text">每日签到</div>
      </div>

      <div class="action-item" @click="goToPage('/lottery')">
        <div class="action-icon">
          <div class="icon-bg gradient-red">
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
              <circle cx="10" cy="10" r="8" fill="none" stroke="white" stroke-width="2" />
              <path d="M10 2L12 8L10 14L8 8L10 2Z" fill="white" />
            </svg>
          </div>
        </div>
        <div class="action-text">转盘抽奖</div>
      </div>

      <div class="action-item" @click="goToPage('/express')">
        <div class="action-icon">
          <div class="icon-bg gradient-blue">
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
              <rect x="2" y="6" width="16" height="8" rx="2" fill="white" />
              <path d="M6 10L10 13L14 10" stroke="#3575FF" stroke-width="2" />
            </svg>
          </div>
        </div>
        <div class="action-text">快递查询</div>
      </div>

      <div class="action-item" @click="goToPage('/invite')">
        <div class="action-icon">
          <div class="icon-bg gradient-orange">
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
              <circle cx="10" cy="7" r="3" fill="white" />
              <path d="M5 17C5 13.5 7.5 11 10 11C12.5 11 15 13.5 15 17" stroke="white" stroke-width="2" fill="none" />
            </svg>
          </div>
        </div>
        <div class="action-text">邀请好友</div>
      </div>
    </div>

    <!-- 立即加入按钮 -->
    <div class="join-section">
      <button class="join-btn" @click="goToPage('/register')">
        立即加入
      </button>
    </div>

    <!-- 专属服务标题 -->
    <div class="section-title">
      <span>专属服务</span>
    </div>

    <!-- 快捷服务卡片 -->
    <div class="service-cards">
      <div class="service-card" @click="goToPage('/team')">
        <div class="card-icon team-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <circle cx="12" cy="8" r="4" fill="white" />
            <path d="M6 20C6 16 8.5 14 12 14C15.5 14 18 16 18 20" stroke="white" stroke-width="2" fill="none" />
          </svg>
        </div>
        <div class="card-content">
          <h5 class="card-title">我的团队</h5>
          <p class="card-subtitle">立即参与</p>
        </div>
      </div>

      <div class="service-card" @click="goToPage('/customer-service')">
        <div class="card-icon service-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path
              d="M21 11.5C21 16.1 16.9 20 12 20C10.5 20 9.1 19.6 7.9 18.9L3 20L4.1 15.1C3.4 13.9 3 12.5 3 11.5C3 6.9 7.1 3 12 3C16.9 3 21 6.9 21 11.5Z"
              fill="white" />
            <circle cx="9" cy="11" r="1" fill="#23AE94" />
            <circle cx="12" cy="11" r="1" fill="#23AE94" />
            <circle cx="15" cy="11" r="1" fill="#23AE94" />
          </svg>
        </div>
        <div class="card-content">
          <h5 class="card-title">在线客服</h5>
          <p class="card-subtitle">立即沟通</p>
        </div>
      </div>
    </div>

    <!-- 团队展示标题 -->
    <div class="section-title">
      <span>团队展示</span>
    </div>

    <!-- 慈善项目卡片（横向滑动） -->
    <div class="charity-section">
      <div class="charity-scroll-container" @touchstart="onCharityTouchStart" @touchmove="onCharityTouchMove"
        @touchend="onCharityTouchEnd">
        <div class="charity-cards-wrapper" :style="{ transform: `translateX(${charityScrollOffset}px)` }">
          <div class="charity-card main-card" @click="goToPage('/charity')">
            <div class="charity-image">
              <img src="@/assets/images/home/<USER>" alt="为山区儿童筑梦未来" />
            </div>
            <div class="charity-content">
              <h4 class="charity-title">为山区儿童筑梦未来</h4>
              <p class="charity-amount">已筹款10.5万元</p>
            </div>
            <button class="charity-btn" @click.stop="goToPage('/charity')">
              立即参与
            </button>
          </div>
          <div class="charity-card side-card" @click="goToPage('/charity')">
            <div class="charity-image">
              <img src="@/assets/images/home/<USER>" alt="山区儿童教育" />
            </div>
            <div class="charity-content">
              <h4 class="charity-title">山区儿童教育</h4>
            </div>
          </div>
          <div class="charity-card side-card" @click="goToPage('/charity')">
            <div class="charity-image">
              <img src="@/assets/images/home/<USER>" alt="抗洪救灾" />
            </div>
            <div class="charity-content">
              <h4 class="charity-title">抗洪救灾</h4>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 新闻资讯标题 -->
    <div class="section-title">
      <span>新闻资讯</span>
      <span class="more-link" @click="goToPage('/news')">查看更多 ></span>
    </div>

    <!-- 新闻资讯卡片区域 -->
    <div class="news-section">
      <div class="news-item" @click="goToPage('/news')">
        <div class="news-content">
          <h3 class="news-title">贫困山区教育援助计划启动为贫困地区儿童提供优质教育资源，帮助他们改变命运</h3>
          <span class="news-date">2025-06-03</span>
        </div>
        <div class="news-image">
          <img src="@/assets/images/home/<USER>" alt="教育援助" />
        </div>
      </div>
      <div class="news-item" @click="goToPage('/news')">
        <div class="news-content">
          <h3 class="news-title">抗洪救灾紧急行动为受灾地区提供紧急救援物资和重建家园支持</h3>
          <span class="news-date">2026-06-03</span>
        </div>
        <div class="news-image">
          <img src="@/assets/images/home/<USER>" alt="抗洪救灾" />
        </div>
      </div>
    </div>

    <!-- 浮动头像 -->
    <div class="floating-avatar" :style="floatingStyle" @touchstart="onTouchStart" @touchmove="onTouchMove"
      @touchend="onTouchEnd" @click="goToPage('/profile')">
      <img src="@/assets/images/home/<USER>" alt="用户头像" />
    </div>

    <!-- 底部导航 -->
    <tab-bar :active="0"></tab-bar>
  </div>
</template>

<script>
import TabBar from '@/components/TabBar.vue'

export default {
  name: 'Home',
  components: {
    TabBar
  },
  data() {
    return {
      // 浮动头像位置
      floatingPosition: {
        x: window.innerWidth - 70,
        y: window.innerHeight / 2 - 50
      },
      isDragging: false,
      startPosition: { x: 0, y: 0 },
      initialPosition: { x: 0, y: 0 },

      // 慈善项目滑动相关
      charityScrollOffset: 0,
      charityStartX: 0,
      charityCurrentX: 0,
      charityIsDragging: false
    }
  },
  mounted() {
    this.initFloatingPosition()
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
  },
  computed: {
    floatingStyle() {
      return {
        left: this.floatingPosition.x + 'px',
        top: this.floatingPosition.y + 'px'
      }
    }
  },
  methods: {
    goToPage(path) {
      this.$router.push(path)
    },

    // 初始化浮动头像位置
    initFloatingPosition() {
      const screenWidth = window.innerWidth
      const screenHeight = window.innerHeight

      this.floatingPosition = {
        x: screenWidth - 70,
        y: Math.max(100, screenHeight / 2 - 30)
      }
    },

    // 处理窗口大小变化
    handleResize() {
      const screenWidth = window.innerWidth
      const screenHeight = window.innerHeight

      this.floatingPosition.x = Math.min(this.floatingPosition.x, screenWidth - 70)
      this.floatingPosition.y = Math.min(this.floatingPosition.y, screenHeight - 70)
    },

    // 触摸开始
    onTouchStart(e) {
      e.preventDefault()
      this.isDragging = true
      const touch = e.touches[0]
      this.startPosition = {
        x: touch.clientX,
        y: touch.clientY
      }
      this.initialPosition = {
        x: this.floatingPosition.x,
        y: this.floatingPosition.y
      }
    },

    // 触摸移动
    onTouchMove(e) {
      e.preventDefault()
      if (!this.isDragging) return

      const touch = e.touches[0]
      const deltaX = touch.clientX - this.startPosition.x
      const deltaY = touch.clientY - this.startPosition.y

      const newX = this.initialPosition.x + deltaX
      const newY = this.initialPosition.y + deltaY

      const maxX = window.innerWidth - 60
      const maxY = window.innerHeight - 60

      this.floatingPosition.x = Math.max(0, Math.min(newX, maxX))
      this.floatingPosition.y = Math.max(0, Math.min(newY, maxY))
    },

    // 触摸结束
    onTouchEnd(e) {
      e.preventDefault()
      this.isDragging = false

      const touch = e.changedTouches[0]
      const deltaX = Math.abs(touch.clientX - this.startPosition.x)
      const deltaY = Math.abs(touch.clientY - this.startPosition.y)

      // 如果移动距离小于10px，认为是点击事件
      if (deltaX < 10 && deltaY < 10) {
        setTimeout(() => {
          this.goToPage('/profile')
        }, 100)
      }
    },

    // 慈善项目滑动触摸开始
    onCharityTouchStart(e) {
      this.charityIsDragging = true
      this.charityStartX = e.touches[0].clientX
      this.charityCurrentX = this.charityScrollOffset
    },

    // 慈善项目滑动触摸移动
    onCharityTouchMove(e) {
      if (!this.charityIsDragging) return
      e.preventDefault()

      const currentX = e.touches[0].clientX
      const deltaX = currentX - this.charityStartX
      const newOffset = this.charityCurrentX + deltaX

      // 限制滑动范围
      const maxOffset = 0
      const minOffset = -(window.innerWidth * 0.6) // 可以滑动到第二张和第三张卡片

      this.charityScrollOffset = Math.max(minOffset, Math.min(maxOffset, newOffset))
    },

    // 慈善项目滑动触摸结束
    onCharityTouchEnd(e) {
      this.charityIsDragging = false
    }
  }
}
</script>

<style scoped>
.home {
  min-height: 100vh;
  background: #F7F7F7;
  position: relative;
  padding-bottom: 80px;
}

/* 顶部背景图 */
.top-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 201px;
  z-index: 0;
  overflow: hidden;
}

.top-bg img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 主要banner区域 */
.main-banner {
  position: relative;
  z-index: 1;
  width: 100%;
  height: 206px;
  margin-bottom: -20px;
  /* 让快捷入口上移重叠 */
}

.banner-image {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.banner-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 快捷功能区域 */
.quick-actions {
  display: flex;
  justify-content: space-around;
  align-items: center;
  background: #FFFFFF;
  border-radius: 12px;
  margin: 0 16px 20px;
  padding: 16px 8px;
  position: relative;
  z-index: 2;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  flex: 1;
}

.action-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}

.icon-bg {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.gradient-yellow {
  background: linear-gradient(135deg, #FFB33C 0%, #FFD855 100%);
}

.gradient-red {
  background: linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 100%);
}

.gradient-blue {
  background: linear-gradient(135deg, #4DABF7 0%, #339AF0 100%);
}

.gradient-orange {
  background: linear-gradient(135deg, #FF8C42 0%, #FF6B35 100%);
}

.action-text {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 12px;
  color: #333333;
  text-align: center;
}

/* 立即加入按钮 */
.join-section {
  text-align: center;
  margin: 0 16px 24px;
  position: relative;
  z-index: 2;
}

.join-btn {
  background: linear-gradient(135deg, #4DABF7 0%, #339AF0 100%);
  border: none;
  border-radius: 25px;
  padding: 12px 40px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 600;
  font-size: 16px;
  color: #FFFFFF;
  cursor: pointer;
  box-shadow: 0 4px 20px rgba(52, 154, 240, 0.3);
  width: 100%;
  max-width: 280px;
}

/* 区块标题 */
.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0 16px 16px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 600;
  font-size: 18px;
  color: #333333;
}

.more-link {
  font-size: 14px;
  color: #999999;
  font-weight: 400;
  cursor: pointer;
}

/* 快捷服务卡片 */
.service-cards {
  display: flex;
  gap: 12px;
  margin: 0 16px 24px;
  position: relative;
  z-index: 2;
}

.service-card {
  background: #FFFFFF;
  border-radius: 12px;
  padding: 20px 16px;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
}

.team-icon {
  background: linear-gradient(135deg, #4DABF7 0%, #339AF0 100%);
}

.service-icon {
  background: linear-gradient(135deg, #51CF66 0%, #40C057 100%);
}

.card-content {
  text-align: center;
}

.card-title {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 600;
  font-size: 15px;
  color: #333333;
  margin-bottom: 4px;
}

.card-subtitle {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 12px;
  color: #999999;
}

/* 慈善项目区域 */
.charity-section {
  margin: 0 0 24px;
  position: relative;
  z-index: 2;
}

.charity-scroll-container {
  overflow: hidden;
  padding-left: 16px;
}

.charity-cards-wrapper {
  display: flex;
  gap: 12px;
  transition: transform 0.3s ease;
}

.charity-card {
  background: #1F0E0E;
  border-radius: 12px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  min-height: 140px;
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.main-card {
  width: 70vw;
  min-width: 260px;
}

.side-card {
  width: 20vw;
  min-width: 120px;
}

.charity-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.charity-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.charity-content {
  position: relative;
  z-index: 2;
  margin-bottom: 12px;
}

.charity-title {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 600;
  font-size: 14px;
  color: #FFFFFF;
  margin-bottom: 6px;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

.charity-amount {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 12px;
  color: #E0E0E0;
}

.charity-btn {
  background: linear-gradient(135deg, #51CF66 0%, #40C057 100%);
  border: none;
  border-radius: 20px;
  padding: 8px 16px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 12px;
  color: #FFFFFF;
  cursor: pointer;
  position: relative;
  z-index: 2;
  align-self: flex-start;
}

/* 新闻资讯区域 */
.news-section {
  margin: 0 16px 24px;
  position: relative;
  z-index: 2;
}

.news-item {
  background: #FFFFFF;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.news-content {
  flex: 1;
  margin-right: 12px;
}

.news-title {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 1.4;
  color: #333333;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.news-date {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 12px;
  color: #999999;
}

.news-image {
  width: 80px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.news-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 浮动头像 */
.floating-avatar {
  position: fixed;
  width: 54px;
  height: 54px;
  border-radius: 27px;
  background: linear-gradient(135deg, #4DABF7 0%, #339AF0 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  cursor: pointer;
  box-shadow: 0 4px 16px rgba(77, 171, 247, 0.4);
  overflow: hidden;
}

.floating-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 27px;
}
</style>
