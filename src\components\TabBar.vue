<template>
  <div class="tab-bar">
    <div class="tab-bar-inner">
      <div class="tab-item" :class="{ active: active === 0 }" @click="onTabClick(0, '/')">
        <div class="tab-icon">
          <img src="@/assets/icons/home-icon.svg" alt="首页" />

        </div>
        <div class="tab-text" :class="{ 'active-text': active === 0 }">首页</div>
      </div>
      <div class="tab-item" :class="{ active: active === 1 }" @click="onTabClick(1, '/points-mall')">
        <div class="tab-icon">
          <img src="@/assets/icons/cart-icon.svg" alt="商城" />
        </div>
        <div class="tab-text" :class="{ 'active-text': active === 1 }">商城</div>
      </div>
      <div class="tab-item shanzhili-tab" @click="onTabClick(2, '/power')">
        <div class="tab-icon shanzhili-icon">
          <div class="shanzhili-bg"></div>
          <img src="@/assets/icons/like-icon.svg" alt="善之力" />
        </div>
        <div class="tab-text shanzhili-text">善之力</div>
      </div>
      <div class="tab-item" :class="{ active: active === 3 }" @click="onTabClick(3, '/live')">
        <div class="tab-icon">
          <img src="@/assets/icons/live-icon.svg" alt="直播间" />
        </div>
        <div class="tab-text" :class="{ 'active-text': active === 3 }">直播间</div>
      </div>
      <div class="tab-item" :class="{ active: active === 4 }" @click="onTabClick(4, '/mine')">
        <div class="tab-icon">
          <img src="@/assets/icons/user-icon.svg" alt="我的" />
        </div>
        <div class="tab-text" :class="{ 'active-text': active === 4 }">我的</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TabBar',
  props: {
    active: {
      type: Number,
      default: 0
    }
  },
  methods: {
    onTabClick(index, path) {
      if (this.active !== index) {
        this.$router.push(path);
        this.$emit('change', index);
      }
    }
  }
}
</script>

<style scoped>
.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 80px;
  background-color: #ffffff;
  box-shadow: 0 -1px 5px rgba(0, 0, 0, 0.08);
  z-index: 100;
  overflow: visible;
  /* 允许善之力图标向上突出 */
}

.tab-bar-inner {
  display: flex;
  align-items: center;
  height: 100%;
  padding-bottom: env(safe-area-inset-bottom);
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  cursor: pointer;
}

.tab-icon {
  height: 24px;
  margin-bottom: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tab-icon img {
  width: 24px;
  height: 24px;
}

.tab-text {
  font-size: 12px;
  color: #999999;
  line-height: 1.2;
}

.active-text {
  color: #999999;
}

/* 善之力特殊样式 */
.shanzhili-tab {
  position: relative;
}

.shanzhili-icon {
  position: relative;
  transform: translateY(-15px);
  /* 向上浮起 */
  z-index: 10;
}

.shanzhili-bg {
  position: absolute;
  width: 50px;
  height: 50px;
  background: #3575FF;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 4px 12px rgba(234, 85, 95, 0.3);
  z-index: -1;
}

.shanzhili-icon img {
  position: relative;
  z-index: 1;
  filter: brightness(0) invert(1);
  /* 将图标变为白色 */
}

.shanzhili-text {
  color: #3575FF !important;
  font-weight: 600;
  font-size: 12px;
}
</style>