<template>
  <div class="express-page">
    <!-- 头部导航栏 -->
    <div class="header">
      <div class="back-icon" @click="goBack">
        <img src="@/assets/images/express/back-icon.png" alt="返回" />
      </div>
      <div class="title">快递查询</div>
    </div>

    <!-- 搜索框区域 -->
    <div class="search-container">
      <div class="search-box">
        <input type="text" class="search-input" v-model="trackingNumber" placeholder="请输入快递单号"
          @keyup.enter="searchExpress" />
      </div>
      <div class="search-box">
        <input type="text" class="search-input" v-model="phone" placeholder="请输入收/寄件人电话" @keyup.enter="searchExpress" />
      </div>
      <div class="search-button" @click="searchExpress">
        <span>查询</span>
      </div>
    </div>

    <!-- 快递信息区域 -->
    <div class="express-info" v-if="expressData && expressData.trackingNumber">
      <!-- 收件人信息 -->
      <div class="recipient-info">
        <div class="recipient-badge">
          <div class="badge-circle grey">
            <span>收</span>
          </div>
          <div class="recipient-address">
            <div class="address-line">【收件地址】{{ expressData.recipient.address }}</div>
            <div class="address-detail">{{ expressData.recipient.phone }}</div>
          </div>
        </div>

        <!-- 快递状态 -->
        <div class="express-status">
          <div class="badge-circle" :class="expressData.status === 'delivered' ? 'green' : 'red'">
            <img src="@/assets/images/express/delivery-icon.png" alt="状态" class="delivery-icon" />
          </div>
          <div class="status-text" :class="expressData.status === 'delivered' ? 'green-text' : 'red-text'">
            {{ expressData.statusText }}
          </div>
          <div class="status-box">
            <div class="status-arrow">
              <img src="@/assets/images/express/arrow-down.png" alt="展开" />
            </div>
            <div class="status-detail">
              <div class="status-reminder" v-if="expressData.status === 'delivered'">
                您的快递已于 {{ expressData.estimatedDelivery }} 签收
              </div>
              <div class="status-reminder" v-else>
                温馨提示：您的订单预计{{ expressData.estimatedDelivery }}前送达，请您做好收货安排
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 物流时间线 -->
      <div class="timeline">
        <div class="timeline-line"></div>

        <div class="timeline-items">
          <div v-for="(item, index) in expressData.timeline" :key="index" class="timeline-item">
            <div class="timeline-date">{{ item.date }}</div>
            <div class="timeline-time">{{ item.time }}</div>
            <div class="timeline-dot" :class="{ active: item.active }"></div>
            <div class="timeline-content">
              {{ item.content }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 无数据提示 -->
    <div class="no-data" v-if="searched && (!expressData || !expressData.trackingNumber)">
      <p>未查询到该快递单号信息，请核对后重新查询</p>
    </div>
  </div>
</template>

<script>
import { Toast } from 'vant';
import axios from 'axios';

export default {
  name: 'Express',
  data() {
    return {
      trackingNumber: '', // 快递单号
      phone: '', // 收/寄件人电话
      searched: false, // 是否进行过搜索
      expressData: null, // 快递数据
      loading: false // 加载状态
    };
  },
  methods: {
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },

    // 搜索快递
    searchExpress() {
      if (!this.trackingNumber.trim()) {
        Toast('请输入快递单号');
        return;
      }

      if (!this.phone.trim()) {
        Toast('请输入收/寄件人电话');
        return;
      }

      this.loading = true;
      Toast.loading({
        message: '查询中...',
        forbidClick: true,
        duration: 0
      });      // 发送API请求获取快递信息
      axios.get('/shop/getexpre', {
        params: {
          num: this.trackingNumber,
          phone: this.phone
        }
      })
        .then(response => {
          if (response.data && response.data.code === 200) {
            // 处理数据
            const logisticsData = response.data.data || [];

            // 生成需要的数据结构
            if (logisticsData.length > 0) {
              this.expressData = {
                trackingNumber: this.trackingNumber,
                status: this.getStatusType(logisticsData[0].statusCode),
                statusText: logisticsData[0].status,
                recipient: {
                  name: '收件人',
                  phone: this.phone,
                  address: logisticsData[0].location || ''
                },
                estimatedDelivery: this.getEstimatedDeliveryTime(logisticsData),
                timeline: this.formatLogisticsData(logisticsData)
              };
            } else {
              this.expressData = null;
            }
          } else {
            this.expressData = null;
            Toast('查询失败，请重试');
          }
        })
        .catch(error => {
          console.error('查询快递信息失败', error);
          Toast('查询失败，请重试');
          this.expressData = null;
        })
        .finally(() => {
          this.searched = true;
          this.loading = false;
          Toast.clear();
        });
    },

    // 格式化物流数据
    formatLogisticsData(logisticsData) {
      return logisticsData.map((item, index) => {
        // 处理日期
        const itemDate = new Date(item.time);
        const today = new Date();
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);

        let dateDisplay;
        if (itemDate.toDateString() === today.toDateString()) {
          dateDisplay = '今天';
        } else if (itemDate.toDateString() === yesterday.toDateString()) {
          dateDisplay = '昨天';
        } else {
          const month = itemDate.getMonth() + 1;
          const day = itemDate.getDate();
          dateDisplay = `${month < 10 ? '0' + month : month}-${day < 10 ? '0' + day : day}`;
        }

        // 处理时间
        const hours = itemDate.getHours();
        const minutes = itemDate.getMinutes();
        const timeDisplay = `${hours < 10 ? '0' + hours : hours}:${minutes < 10 ? '0' + minutes : minutes}`;

        return {
          date: dateDisplay,
          time: timeDisplay,
          content: item.context,
          active: index === 0
        };
      });
    },

    // 获取状态类型
    getStatusType(statusCode) {
      // 根据状态码返回状态类型，用于UI显示
      const codeMap = {
        '3': 'delivered', // 已签收
        '501': 'pickup', // 待取件
        '5': 'delivering', // 派送中
        '0': 'transport', // 运输中
        '1002': 'transport', // 干线运输
        '1': 'collected' // 已揽收
      };

      return codeMap[statusCode] || 'transport';
    },

    // 获取预计到达时间
    getEstimatedDeliveryTime(logisticsData) {
      // 如果已签收，返回签收时间
      if (logisticsData.length > 0 && logisticsData[0].statusCode === '3') {
        const signTime = new Date(logisticsData[0].time);
        const year = signTime.getFullYear();
        const month = signTime.getMonth() + 1;
        const day = signTime.getDate();
        const hours = signTime.getHours();
        const minutes = signTime.getMinutes();

        return `${year}年${month}月${day}日 ${hours < 10 ? '0' + hours : hours}:${minutes < 10 ? '0' + minutes : minutes}`;
      }

      // 否则返回一个预计时间（当前时间加上2天）
      const estimatedTime = new Date();
      estimatedTime.setDate(estimatedTime.getDate() + 2);

      const year = estimatedTime.getFullYear();
      const month = estimatedTime.getMonth() + 1;
      const day = estimatedTime.getDate();

      return `${year}年${month}月${day}日 22:00`;
    },
  }
};
</script>

<style scoped>
.express-page {
  background-color: #F8F9FA;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding-bottom: 20px;
}

/* 头部导航栏 */
.header {
  height: 43px;
  background: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.back-icon {
  position: absolute;
  left: 16px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.back-icon img {
  width: 10px;
  height: 17px;
}

.title {
  font-family: 'PingFang SC', sans-serif;
  font-size: 17px;
  font-weight: 600;
  color: #111827;
  text-align: center;
}

/* 搜索区域 */
.search-container {
  padding: 20px 16px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.search-box {
  position: relative;
  height: 38px;
  background: #FFFFFF;
  border-radius: 200px;
  display: flex;
  align-items: center;
  width: 100%;
}

.search-input {
  flex: 1;
  height: 100%;
  border: none;
  outline: none;
  background: transparent;
  padding: 0 15px;
  font-size: 14px;
  color: #333;
}

.search-input::placeholder {
  color: #999999;
}

.search-button {
  height: 44px;
  background: #0094FF;
  border-radius: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #FFFFFF;
  font-size: 16px;
  cursor: pointer;
}

/* 快递信息区域 */
.express-info {
  margin: 0 16px;
  background: #FFFFFF;
  border-radius: 10px;
  padding: 20px 10px;
}

/* 收件人信息 */
.recipient-info {
  padding-bottom: 15px;
}

.recipient-badge {
  display: flex;
  margin-bottom: 20px;
}

.badge-circle {
  width: 25px;
  height: 25px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  flex-shrink: 0;
}

.badge-circle.grey {
  background-color: #BABABC;
}

.badge-circle.red {
  background-color: #E94050;
}

.badge-circle.green {
  background-color: #41B883;
}

.badge-circle span {
  color: #FFFFFF;
  font-size: 14px;
  font-weight: 500;
}

.delivery-icon {
  width: 17px;
  height: 12px;
}

.recipient-address {
  flex: 1;
}

.address-line {
  font-size: 14px;
  color: #555555;
  margin-bottom: 3px;
}

.address-detail {
  font-size: 14px;
  color: #555555;
}

/* 快递状态 */
.express-status {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.status-text {
  font-size: 14px;
  margin: 0 10px;
}

.red-text {
  color: #E94050;
}

.green-text {
  color: #41B883;
}

.status-box {
  flex: 1;
  background: #F8F8F8;
  border-radius: 5px;
  padding: 10px;
}

.status-arrow {
  display: flex;
  justify-content: center;
  margin-bottom: 5px;
}

.status-arrow img {
  width: 11px;
  height: 6px;
}

.status-reminder {
  font-size: 12px;
  color: #555555;
  line-height: 1.25;
}

/* 物流时间线 */
.timeline {
  position: relative;
  padding-left: 75px;
}

.timeline-line {
  position: absolute;
  left: 75px;
  top: 5px;
  bottom: 5px;
  width: 1px;
  background-color: #CECECE;
}

.timeline-items {
  position: relative;
}

.timeline-item {
  position: relative;
  padding: 10px 0;
  display: flex;
}

.timeline-date {
  position: absolute;
  left: -75px;
  top: 10px;
  width: 35px;
  font-size: 12px;
  color: #555555;
}

.timeline-time {
  position: absolute;
  left: -40px;
  top: 10px;
  width: 35px;
  font-size: 12px;
  color: #555555;
}

.timeline-dot {
  position: absolute;
  left: -5px;
  top: 13px;
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background-color: #D9D9D9;
  z-index: 1;
}

.timeline-dot.active {
  background-color: #0094FF;
  width: 7px;
  height: 7px;
  left: -6px;
  top: 12px;
}

.timeline-content {
  font-size: 12px;
  color: #555555;
  line-height: 1.25;
}

/* 无数据提示 */
.no-data {
  margin-top: 100px;
  text-align: center;
  color: #999;
  font-size: 14px;
}
</style>
