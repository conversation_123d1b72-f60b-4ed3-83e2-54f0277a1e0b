<template>
  <div class="sign-in-calendar">
    <!-- 顶部渐变背景 -->
    <div class="header-bg">
      <!-- 返回按钮 -->
      <div class="back-btn" @click="goBack">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <path d="M15 18L9 12L15 6" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
        </svg>
      </div>

      <!-- 奖励内容区域 -->
      <div class="reward-content">
        <!-- 奖励提示文字 -->
        <div class="reward-text">{{ signInStatus > 0 ? '今天已签到，获得奖励' : '今天还未签到' }}</div>

        <!-- 爱心和积分显示 -->
        <div class="reward-display" v-if="signInStatus > 0">
          <div class="heart-icon">❤️</div>
          <div class="multiply-sign">×</div>
          <div class="points-number">{{ rewardAmount }}</div>
        </div>

        <!-- 查看积分按钮 -->
        <div class="view-points-btn" @click="goToPoints">
          查看我的积分
        </div>
      </div>

      <!-- 装饰元素 -->
      <div class="decoration-elements">
        <!-- 可以添加一些装饰性的图标或元素 -->
      </div>
    </div>

    <!-- 页面内容 -->
    <div class="content">
      <!-- 标题栏 -->
      <div class="title-section">
        <h1 class="page-title">签到日历</h1>
        <div class="points-link" @click="goToGuide">如何获得更多积分</div>
      </div>

      <!-- 日历网格 -->
      <div class="calendar-container">
        <div class="calendar-grid">
          <div v-for="(day, index) in daysArray" :key="index" class="calendar-day" :class="{
            'signed': day.isSigned,
            'today': day.isToday,
            'prev-month': day.isPrevMonth,
            'next-month': day.isNextMonth,
            'primary': day.isPrimary
          }" @click="signIn(day)">
            <div class="day-number">{{ day.day }}</div>
            <div v-if="day.isSigned" class="day-status-icon future-icon"></div>
          </div>
        </div>
      </div>

      <!-- 积分商城按钮 -->
      <div class="shop-button-container">
        <button class="shop-button" @click="goToShop">
          <img src="@/assets/images/signin/checkin-banner-bg.png" alt="积分商城" class="shop-icon" />
          <span class="shop-text">积分商城</span>
          <div class="shop-tip">
            <span class="tip-text">点击<br>查看</span>
          </div>
        </button>
      </div>

      <!-- 签到说明 -->
      <div class="signin-rules">
        <h3 class="rules-title">签到说明：</h3>
        <template v-if="Array.isArray(des)">
          <p class="rules-text" v-for="(rule, index) in des" :key="index">{{ rule }}</p>
        </template>
        <template v-else>
          <div class="rules-text" v-html="des"></div>
        </template>

      </div>
    </div>
  </div>
</template>

<script>
import { Toast, Dialog } from 'vant'

export default {
  name: 'SignInCalendar',
  data() {
    return {
      todayReward: 5, // 今日奖励积分（默认值）
      rewardAmount: 5, // API返回的奖励积分数量
      daysArray: [], // 日历数据
      currentMonth: new Date().getMonth() + 1,
      currentYear: new Date().getFullYear(),
      signedDays: [], // 已签到的日期
      todaySignData: [], // 今日签到数据
      primaryDates: [], // 需要红色显示的日期
      signInStatus: 0, // 签到状态：0-未签到，1-已签到
      des: '每日签到+5积分，<span class="reward-highlight">每月10号签到20积分</span>，月底签到+30积分。', // 签到说明HTML内容
      apiData: null // 存储API返回的完整数据
    }
  },
  created() {
    this.loadSignedDays()
  },
  methods: {
    // 获取奖励积分
    getRewardPoints(day) {
      if (day.day === 10) return '20'; // 每月10号
      if (day.day >= 28) return '30'; // 月底
      return '5'; // 普通日期
    },

    // 检查日期是否在当前页面显示范围内
    isDateInCurrentPageView(date) {
      if (!date) return false

      const normalizedDate = this.normalizeDate(date)
      if (!normalizedDate) return false

      const dateObj = new Date(normalizedDate)
      const dateYear = dateObj.getFullYear()
      const dateMonth = dateObj.getMonth() + 1
      const dateDay = dateObj.getDate()

      const currentYear = this.currentYear
      const currentMonth = this.currentMonth

      // 上个月和下个月的计算
      let prevMonth = currentMonth - 1
      let prevYear = currentYear
      if (prevMonth === 0) {
        prevMonth = 12
        prevYear = currentYear - 1
      }

      let nextMonth = currentMonth + 1
      let nextYear = currentYear
      if (nextMonth === 13) {
        nextMonth = 1
        nextYear = currentYear + 1
      }

      // 检查是否属于当前月
      if (dateYear === currentYear && dateMonth === currentMonth) {
        return true
      }

      // 检查是否属于上个月的末尾几天（在当前页面显示）
      if (dateYear === prevYear && dateMonth === prevMonth) {
        // 获取当前月第一天是星期几
        const firstDay = new Date(currentYear, currentMonth - 1, 1)
        const firstDayWeek = firstDay.getDay()
        const daysToAddFromPrevMonth = firstDayWeek === 0 ? 6 : firstDayWeek - 1

        if (daysToAddFromPrevMonth > 0) {
          const prevMonthLastDay = new Date(currentYear, currentMonth - 1, 0).getDate()
          const minPrevMonthDay = prevMonthLastDay - daysToAddFromPrevMonth + 1
          return dateDay >= minPrevMonthDay
        }
      }

      // 检查是否属于下个月的开头几天（在当前页面显示）
      if (dateYear === nextYear && dateMonth === nextMonth) {
        // 计算需要显示多少天的下个月日期
        const totalCells = 42 // 6行 x 7列
        const currentMonthDays = new Date(currentYear, currentMonth, 0).getDate()
        const firstDay = new Date(currentYear, currentMonth - 1, 1)
        const firstDayWeek = firstDay.getDay()
        const daysToAddFromPrevMonth = firstDayWeek === 0 ? 6 : firstDayWeek - 1
        const remainingCells = totalCells - currentMonthDays - daysToAddFromPrevMonth

        return dateDay <= remainingCells
      }

      return false
    },

    // 标准化日期字符串格式（YYYY-MM-DD）
    normalizeDate(date) {
      // 特殊日期的检测改为更精确的匹配
      const isSpecialDateInput = date &&
        ((typeof date === 'string' && (date.includes('2025-5-27') || date.includes('2025-5-28'))) ||
          (typeof date === 'object' && date.date && (date.date.includes('2025-5-27') || date.date.includes('2025-5-28'))))

      if (!date) return null

      // 如果是对象格式，获取date属性
      if (typeof date === 'object' && date !== null && date.date) {
        // 处理对象格式 {date: "2025-5-28", text: "+1"}
        const result = this.normalizeDate(date.date)
        return result
      }

      let dateStr
      if (typeof date === 'string') {
        // 移除时间部分，只保留日期
        dateStr = date.includes('T') ? date.split('T')[0] : date
        // 处理API返回的日期格式：2025-6-1 -> 2025-06-01
        if (/^\d{4}-\d{1,2}-\d{1,2}$/.test(dateStr)) {
          const parts = dateStr.split('-')
          dateStr = `${parts[0]}-${parts[1].padStart(2, '0')}-${parts[2].padStart(2, '0')}`
        }
      } else if (date instanceof Date) {
        dateStr = date.toISOString().split('T')[0]
      } else {
        return null
      }

      // 验证日期格式 (YYYY-MM-DD)
      if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
        return dateStr
      }

      return null
    },

    // 生成日历数据
    generateCalendar() {
      const year = this.currentYear
      const month = this.currentMonth

      // 当前月的第一天
      const firstDay = new Date(year, month - 1, 1)
      // 当前月的最后一天
      const lastDay = new Date(year, month, 0)

      // 获取当前月第一天是星期几（0是星期日，1是星期一...）
      let firstDayWeek = firstDay.getDay()
      // 转换为中国的星期格式：星期一为1，星期日为7
      // 但这里我们需要计算需要填充的前置天数
      // 如果第一天是星期一(1)，那么不需要前置天数
      // 如果第一天是星期二(2)，那么需要1天前置天数
      // 如果第一天是星期日(0)，那么需要6天前置天数
      const daysToAddFromPrevMonth = firstDayWeek === 0 ? 6 : firstDayWeek - 1

      // 获取当前月的总天数
      const totalDays = lastDay.getDate()

      // 获取上个月的最后一天
      const prevMonthLastDay = new Date(year, month - 1, 0).getDate()

      const days = []

      // 添加上个月的日期（填充第一周的空白）
      for (let i = daysToAddFromPrevMonth; i > 0; i--) {
        const day = prevMonthLastDay - i + 1

        // 计算上个月的年份和月份
        let prevMonth = month - 1
        let prevYear = year
        if (prevMonth === 0) {
          prevMonth = 12
          prevYear = year - 1
        }

        const prevDateStr = `${prevYear}-${prevMonth.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`

        // 检查上个月的日期是否已签到
        const isSigned = this.signedDays.some(signedDate => {
          if (!this.isDateInCurrentPageView(signedDate)) return false
          const normalizedDate = this.normalizeDate(signedDate)
          return normalizedDate === prevDateStr
        })

        // 检查上个月的日期是否需要显示+1标记
        const showPlus = this.todaySignData.some(signData => {
          const dateVal = typeof signData === 'object' && signData.date ? signData.date : signData
          if (!this.isDateInCurrentPageView(dateVal)) return false
          const normalizedDate = this.normalizeDate(dateVal)
          return normalizedDate === prevDateStr
        })

        // 检查上个月的日期是否需要红色高亮
        const isPrimary = this.primaryDates.some(primaryDate => {
          const dateVal = typeof primaryDate === 'object' && primaryDate.date ? primaryDate.date : primaryDate
          if (!this.isDateInCurrentPageView(dateVal)) return false
          const normalizedDate = this.normalizeDate(dateVal)
          return normalizedDate === prevDateStr
        })

        days.push({
          day,
          isPrevMonth: true,
          isNextMonth: false,
          isToday: false,
          isSigned,
          showPlus,
          isPrimary,
          isDisabled: true,
          dateStr: prevDateStr
        })
      }

      // 添加当前月的日期
      const currentDate = new Date()
      const isCurrentMonth = currentDate.getFullYear() === year && currentDate.getMonth() === month - 1
      const currentDay = currentDate.getDate()

      for (let i = 1; i <= totalDays; i++) {
        const isToday = isCurrentMonth && i === currentDay
        const currentDateStr = `${year}-${month.toString().padStart(2, '0')}-${i.toString().padStart(2, '0')}`

        // 检查是否已签到（检查在当前页面显示范围内的日期）
        const isSigned = this.signedDays.some(signedDate => {
          if (!this.isDateInCurrentPageView(signedDate)) return false
          const normalizedDate = this.normalizeDate(signedDate)
          return normalizedDate === currentDateStr
        })

        // 只处理当前页面显示范围内的日期标记
        const showPlus = this.todaySignData.some(signData => {
          const dateVal = typeof signData === 'object' && signData.date ? signData.date : signData
          if (!this.isDateInCurrentPageView(dateVal)) return false
          const normalizedDate = this.normalizeDate(dateVal)
          return normalizedDate === currentDateStr
        })
        const isPrimary = this.primaryDates.some(primaryDate => {
          const dateVal = typeof primaryDate === 'object' && primaryDate.date ? primaryDate.date : primaryDate
          if (!this.isDateInCurrentPageView(dateVal)) return false
          const normalizedDate = this.normalizeDate(dateVal)
          return normalizedDate === currentDateStr
        })
        const isDisabled = new Date(year, month - 1, i) > currentDate // 未来日期禁用

        days.push({
          day: i,
          isPrevMonth: false,
          isNextMonth: false,
          isToday,
          isSigned,
          showPlus,
          isPrimary,
          isDisabled,
          dateStr: currentDateStr
        })
      }

      // 添加下个月的日期，填满6行（42个格子）
      const totalCells = 42 // 7 days x 6 rows
      const remainingCells = totalCells - days.length

      for (let i = 1; i <= remainingCells; i++) {
        // 计算下个月的年份和月份
        let nextMonth = month + 1
        let nextYear = year
        if (nextMonth === 13) {
          nextMonth = 1
          nextYear = year + 1
        }

        const nextDateStr = `${nextYear}-${nextMonth.toString().padStart(2, '0')}-${i.toString().padStart(2, '0')}`

        // 检查下个月的日期是否已签到
        const isSigned = this.signedDays.some(signedDate => {
          if (!this.isDateInCurrentPageView(signedDate)) return false
          const normalizedDate = this.normalizeDate(signedDate)
          return normalizedDate === nextDateStr
        })

        // 检查下个月的日期是否需要显示+1标记
        const showPlus = this.todaySignData.some(signData => {
          const dateVal = typeof signData === 'object' && signData.date ? signData.date : signData
          if (!this.isDateInCurrentPageView(dateVal)) return false
          const normalizedDate = this.normalizeDate(dateVal)
          return normalizedDate === nextDateStr
        })

        // 检查下个月的日期是否需要红色高亮
        const isPrimary = this.primaryDates.some(primaryDate => {
          const dateVal = typeof primaryDate === 'object' && primaryDate.date ? primaryDate.date : primaryDate
          if (!this.isDateInCurrentPageView(dateVal)) return false
          const normalizedDate = this.normalizeDate(dateVal)
          return normalizedDate === nextDateStr
        })

        days.push({
          day: i,
          isPrevMonth: false,
          isNextMonth: true,
          isToday: false,
          isSigned,
          showPlus,
          isPrimary,
          isDisabled: true,
          dateStr: nextDateStr
        })
      }

      this.daysArray = days

      // 过滤出在当前页面显示范围内的特殊日期
      const currentPageTodaySignData = this.todaySignData.filter(signData => {
        const dateVal = typeof signData === 'object' && signData.date ? signData.date : signData
        return this.isDateInCurrentPageView(dateVal)
      })

      const currentPagePrimaryDates = this.primaryDates.filter(primaryDate => {
        const dateVal = typeof primaryDate === 'object' && primaryDate.date ? primaryDate.date : primaryDate
        return this.isDateInCurrentPageView(dateVal)
      })

      // 输出当前页面中有特殊标记的日历项（包括上月、当月、下月）
      const specialDays = this.daysArray.filter(day => day.showPlus || day.isPrimary)
    },

    // 上个月
    prevMonth() {
      if (this.currentMonth === 1) {
        this.currentYear--
        this.currentMonth = 12
      } else {
        this.currentMonth--
      }
      this.generateCalendar() // 直接重新生成日历，使用现有数据
    },

    // 下个月
    nextMonth() {
      if (this.currentMonth === 12) {
        this.currentYear++
        this.currentMonth = 1
      } else {
        this.currentMonth++
      }
      this.generateCalendar() // 直接重新生成日历，使用现有数据
    },

    // 加载已签到的日期
    loadSignedDays() {
      const userToken = localStorage.getItem('user_token')
      if (!userToken) {
        Toast('请先登录')
        this.$router.push('/login')
        return
      }

      // 调用签到首页API获取数据
      this.$http.get('/sign/getIndex', {
        headers: {
          'authorization': userToken
        }
      }).then(response => {
        if (response.data.code === 200) {
          const data = response.data.data

          // 保存完整API返回数据
          this.apiData = data;

          // 设置签到状态
          this.signInStatus = data.info.number

          // 设置奖励积分数量
          if (data.info && data.info.amount !== undefined) {
            this.rewardAmount = data.info.amount
          }

          // 设置签到说明文本（如果API返回了des字段）
          if (data.des) {
            this.des = data.des
          }

          // 处理API数据
          const calendarData = data.calendar

          // 提取需要显示+1的日期（从txt数组中）
          if (calendarData.txt && Array.isArray(calendarData.txt)) {
            // 直接使用完整对象数组
            this.todaySignData = calendarData.txt
            // 这些日期也是已签到的日期
            this.signedDays = calendarData.txt
          } else {
            // 测试数据 - 确保2025年5月27和28日显示+1
            // 使用标准化的日期格式
            this.todaySignData = [
              { "date": "2025-05-27", "text": "+1" },
              { "date": "2025-05-28", "text": "+1" }
            ]
            this.signedDays = this.todaySignData
          }

          // 提取需要红色显示的日期（从css数组中）
          if (calendarData.css && Array.isArray(calendarData.css)) {
            // 直接使用完整对象数组
            this.primaryDates = calendarData.css
          } else {
            // 测试数据 - 确保2025年5月27和28日显示红色
            // 使用标准化的日期格式
            this.primaryDates = [
              { "date": "2025-05-27", "class": "primary" },
              { "date": "2025-05-28", "class": "primary" }
            ]
          }

          // 重新生成日历
          this.generateCalendar()
        } else {
          Toast(response.data.msg || '获取签到记录失败')
        }
      }).catch(error => {
        Toast('网络异常，请稍后重试')
      })
    },

    // 签到
    signIn(day) {
      // 如果是过去的月份、未来的月份或禁用的日期，不处理
      if (day.isPrevMonth || day.isNextMonth || day.isDisabled) {
        return
      }

      // 只能在今天签到
      if (!day.isToday) {
        return
      }

      // 检查签到状态，如果已签到就不能再签到
      if (this.signInStatus !== 0) {
        Toast('今天已经签到过啦')
        return
      }

      const userToken = localStorage.getItem('user_token')
      if (!userToken) {
        Toast('请先登录')
        this.$router.push('/login')
        return
      }

      // 发送签到请求
      this.$http.post('/sign/doSubmit', {}, {
        headers: {
          'authorization': userToken
        }
      })
        .then(response => {
          if (response.data.code === 200) {
            Toast('签到成功')

            // 重新加载签到数据
            this.loadSignedDays()

            // 如果有奖励，弹窗显示
            if (response.data.data && response.data.data.reward) {
              this.showReward(response.data.data.reward)
            }
          } else {
            Toast(response.data.msg || '签到失败')
          }
        }).catch(error => {
          Toast('网络异常，请稍后重试')
        })
    },

    // 显示奖励
    showReward(reward) {
      Dialog.alert({
        title: '签到成功',
        message: `恭喜您获得${reward.points}积分奖励！`,
        confirmButtonText: '太棒了'
      })
    },

    // 返回上一页
    goBack() {
      this.$router.go(-1)
    },

    // 跳转到积分页面
    // 跳转到积分指南页面
    goToGuide() {
      this.$router.push('/guide?url=data.help4');
    },

    goToPoints() {
      this.$router.push('/points-mall')
    },

    // 跳转到积分商城
    goToShop() {
      this.$router.push('/points-mall')
    }
  }
}
</script>

<style scoped>
.sign-in-calendar {
  min-height: 100vh;
  background-color: #f7f7f7;
  padding-bottom: 20px;
}

/* 顶部渐变背景 */
.header-bg {
  position: relative;
  height: 280px;
  background: linear-gradient(135deg, #A8C8EC 0%, #7B9FE0 25%, #4A90E2 50%, #6B73FF 75%, #9C88FF 100%);
  overflow: visible;
  /* 更改为visible，确保返回按钮可见 */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 45px;
  /* 添加顶部内边距，为返回按钮留出空间 */
}

/* 返回按钮 */
.back-btn {
  position: absolute;
  top: 15px;
  left: 15px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  transition: transform 0.2s ease;
}

.back-btn:active {
  transform: scale(0.9);
}

/* 奖励内容区域 */
.reward-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  z-index: 5;
}

/* 奖励提示文字 */
.reward-text {
  color: white;
  font-size: 18px;
  font-weight: 500;
  line-height: 24px;
  margin-bottom: 20px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 爱心和积分显示区域 */
.reward-display {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30px;
}

.heart-icon {
  font-size: 32px;
  margin-right: 8px;
  animation: heartbeat 1.5s ease-in-out infinite;
}

@keyframes heartbeat {

  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.1);
  }
}

.multiply-sign {
  color: white;
  font-size: 24px;
  font-weight: 600;
  margin: 0 8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.points-number {
  color: white;
  font-size: 36px;
  font-weight: 700;
  line-height: 40px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-family: 'DIN', 'Arial', sans-serif;
}

/* 查看积分按钮 */
.view-points-btn {
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
  color: #8B4513;
  font-size: 16px;
  font-weight: 600;
  padding: 12px 32px;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
  border: none;
}

.view-points-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(255, 215, 0, 0.4);
}

.view-points-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
}

/* 装饰元素 */
.decoration-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

/* 添加一些装饰性的伪元素 */
.decoration-elements::before {
  content: '';
  position: absolute;
  top: 20%;
  left: 10%;
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: float 3s ease-in-out infinite;
}

.decoration-elements::after {
  content: '';
  position: absolute;
  bottom: 20%;
  right: 15%;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 50%;
  animation: float 3s ease-in-out infinite 1.5s;
}

@keyframes float {

  0%,
  100% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-10px);
  }
}

/* 页面内容 */
.content {
  background: white;
  border-radius: 0;
  margin: 0;
  padding: 0 17px;
}

/* 标题栏 */
.title-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
}

.page-title {
  font-size: 14px;
  font-weight: 600;
  line-height: 20px;
  color: #000000;
  margin: 0;
}

.points-link {
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  color: #858585;
  cursor: pointer;
}

/* 日历容器 */
.calendar-container {
  margin-bottom: 30px;
}

/* 日历网格 */
.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 10px 8px;
  margin-bottom: 20px;
}

.calendar-day {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 45px;
  cursor: pointer;
  position: relative;
}

.day-number {
  font-size: 16px;
  font-weight: 600;
  line-height: 22px;
  color: #000000;
  margin-bottom: 2px;
}

/* 不同月份的日期样式 */
.calendar-day.prev-month .day-number,
.calendar-day.next-month .day-number {
  color: #ABB0C0;
}

/* 已签到的日期 */
.calendar-day.signed .day-number {
  color: #000000;
}

/* 今天的日期 */
.calendar-day.today.signed .day-number {
  color: #000000;
}

.calendar-day.primary .day-number {
  color: #000000 !important;
}

/* 日期上的状态图标 */
.day-status-icon {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
}

.day-status-icon.future-icon {
  background-color: #0474FC;
}

.day-status-icon.future-icon::after {
  content: '';
  position: absolute;
  top: 4px;
  left: 3px;
  width: 10px;
  height: 8px;
  background-image: url("data:image/svg+xml,%3Csvg width='10' height='8' viewBox='0 0 10 8' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M1 4L3.5 6.5L9 1.5' stroke='white' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
}

/* 签到说明 */
.signin-rules {
  margin-top: 20px;
  margin-bottom: 40px;
}

.rules-title {
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  color: #000000;
  margin: 0 0 10px 0;
}

.rules-text {
  font-size: 12px;
  font-weight: 400;
  line-height: 17px;
  color: #8A8A8A;
  margin: 0;
}

/* 签到说明中的高亮文字样式 */
.reward-highlight {
  color: #3F86FF;
  font-weight: 600;
}

/* 积分商城按钮 */
.shop-button-container {
  position: relative;
  margin-bottom: 30px;
}

.shop-button {
  width: 100%;
  height: 72px;
  background: linear-gradient(180deg, #0094FF 0%, #1A50FE 100%);
  border: none;
  border-radius: 35.5px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  cursor: pointer;
  overflow: hidden;
}

.shop-icon {
  position: absolute;
  left: 26px;
  width: 60px;
  height: 60px;
  top: 6px;
}

.shop-text {
  font-family: 'YouSheBiaoTiHei', sans-serif;
  font-size: 36px;
  font-weight: 400;
  line-height: 47px;
  color: white;
  text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.39);
  margin-left: 50px;
}

.shop-tip {
  position: absolute;
  right: 15px;
  width: 59px;
  height: 59px;
  background: linear-gradient(180deg, #FBDE00 0%, #FDB900 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tip-text {
  font-size: 14px;
  font-weight: 600;
  line-height: 18px;
  color: #7F2E09;
  text-align: center;
}

/* 响应式调整 */
@media (max-width: 320px) {
  .calendar-grid {
    gap: 8px 6px;
  }

  .calendar-day {
    height: 40px;
  }

  .day-number {
    font-size: 14px;
  }

  .back-btn {
    top: 10px;
    left: 10px;
  }
}
</style>
