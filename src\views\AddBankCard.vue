<template>
  <div class="add-bank-card">
    <!-- 头部 -->
    <div class="header">
      <div class="back-icon" @click="goBack">
        <img src="@/assets/icons/back-icon.svg" alt="返回">
      </div>
      <div class="title">添加银行卡</div>
    </div>

    <!-- 表单 -->
    <div class="form-section">
      <!-- 持卡人 -->
      <div class="form-item">
        <div class="item-label">持卡人</div>
        <div class="item-input">
          <input type="text" v-model="cardInfo.holderName" placeholder="请输入持卡人姓名">
        </div>
      </div>

      <!-- 卡号 -->
      <div class="form-item">
        <div class="item-label">卡号</div>
        <div class="item-input">
          <input type="text" v-model="cardInfo.cardNumber" placeholder="请输入银行卡号" @input="onCardNumberInput">
        </div>
      </div>

      <!-- 银行 -->
      <div class="form-item">
        <div class="item-label">银行</div>
        <div class="item-input">
          <input type="text" v-model="cardInfo.bankName" placeholder="请选择银行" readonly @click="showBankPicker = true">
        </div>
        <div class="item-arrow">
          <van-icon name="arrow" />
        </div>
      </div>

      <!-- 卡类型 -->
      <div class="form-item">
        <div class="item-label">卡类型</div>
        <div class="item-input">
          <input type="text" v-model="cardInfo.cardType" placeholder="请选择类型" readonly
            @click="showCardTypePicker = true">
        </div>
        <div class="item-arrow">
          <van-icon name="arrow" />
        </div>
      </div>

      <!-- 手机号 -->
      <div class="form-item">
        <div class="item-label">预留手机号</div>
        <div class="item-input">
          <input type="tel" v-model="cardInfo.phone" placeholder="请输入银行预留手机号">
        </div>
      </div>
    </div>

    <!-- 验证码 -->
    <div class="verification-section">
      <div class="form-item">
        <div class="item-label">验证码</div>
        <div class="item-input verification">
          <input type="text" v-model="verificationCode" placeholder="请输入验证码">
          <div class="send-code-btn" @click="sendVerificationCode">
            {{ countdown > 0 ? `${countdown}s后重新获取` : '获取验证码' }}
          </div>
        </div>
      </div>
    </div>

    <!-- 提示 -->
    <div class="tips-section">
      <div class="tips-content">
        <p>1. 请确保填写信息准确无误</p>
        <p>2. 银行卡需要通过实名认证绑定</p>
        <p>3. 为保障资金安全，请使用持卡人本人的银行卡</p>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="bottom-btn" @click="submitForm">
      确认绑定
    </div>

    <!-- 银行选择弹窗 -->
    <van-popup v-model="showBankPicker" position="bottom" round>
      <div class="picker-popup">
        <div class="popup-title">选择银行</div>
        <div class="bank-list">
          <div class="bank-item" v-for="(bank, index) in banks" :key="index" @click="selectBank(bank)">
            <div class="bank-icon">
              <img :src="bank.icon" alt="银行图标">
            </div>
            <div class="bank-name">{{ bank.name }}</div>
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 卡类型选择弹窗 -->
    <van-popup v-model="showCardTypePicker" position="bottom" round>
      <div class="picker-popup">
        <div class="popup-title">选择类型</div>
        <div class="picker-options">
          <div class="picker-option" v-for="(option, index) in cardTypes" :key="index" @click="selectCardType(option)">
            {{ option }}
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>

export default {
  name: 'AddBankCard',
  data() {
    return {
      cardInfo: {
        holderName: '',
        cardNumber: '',
        bankName: '',
        cardType: '',
        phone: ''
      },
      verificationCode: '',
      countdown: 0,
      showBankPicker: false,
      showCardTypePicker: false,
      banks: [
        {
          icon: require('@/assets/images/avatar.jpg'),
          name: '中国工商银行'
        },
        {
          icon: require('@/assets/images/avatar.jpg'),
          name: '中国农业银行'
        },
        {
          icon: require('@/assets/images/avatar.jpg'),
          name: '中国银行'
        },
        {
          icon: require('@/assets/images/avatar.jpg'),
          name: '中国建设银行'
        },
        {
          icon: require('@/assets/images/avatar.jpg'),
          name: '交通银行'
        },
        {
          icon: require('@/assets/images/avatar.jpg'),
          name: '中国邮政储蓄银行'
        },
        {
          icon: require('@/assets/images/avatar.jpg'),
          name: '招商银行'
        },
        {
          icon: require('@/assets/images/avatar.jpg'),
          name: '中信银行'
        }
      ],
      cardTypes: ['储蓄卡', '信用卡']
    }
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    onCardNumberInput() {
      // 格式化银行卡号，每4位添加空格
      this.cardInfo.cardNumber = this.cardInfo.cardNumber.replace(/\s/g, '').replace(/(.{4})/g, '$1 ').trim()
    },
    selectBank(bank) {
      this.cardInfo.bankName = bank.name
      this.showBankPicker = false
    },
    selectCardType(type) {
      this.cardInfo.cardType = type
      this.showCardTypePicker = false
    },
    sendVerificationCode() {
      // 检查手机号
      if (!this.cardInfo.phone) {
        this.$toast('请先输入手机号')
        return
      }
      if (!/^1[3-9]\d{9}$/.test(this.cardInfo.phone)) {
        this.$toast('请输入正确的手机号')
        return
      }
      
      // 如果倒计时正在进行，不重复发送
      if (this.countdown > 0) {
        return
      }
      
      // 发送验证码逻辑
      this.$toast('验证码发送成功')
      this.countdown = 60
      
      // 启动倒计时
      const timer = setInterval(() => {
        this.countdown--
        if (this.countdown <= 0) {
          clearInterval(timer)
        }
      }, 1000)
    },
    async submitForm() {
      // 表单验证
      if (!this.cardInfo.holderName) {
        this.$toast('请输入持卡人姓名')
        return
      }
      if (!this.cardInfo.cardNumber) {
        this.$toast('请输入银行卡号')
        return
      }
      if (!this.cardInfo.bankName) {
        this.$toast('请选择银行')
        return
      }
      if (!this.cardInfo.cardType) {
        this.$toast('请选择卡类型')
        return
      }
      if (!this.cardInfo.phone) {
        this.$toast('请输入预留手机号')
        return
      }
      if (!/^1[3-9]\d{9}$/.test(this.cardInfo.phone)) {
        this.$toast('请输入正确的手机号')
        return
      }
      if (!this.verificationCode) {
        this.$toast('请输入验证码')
        return
      }
      
      try {
        // 发送请求绑定银行卡
        const response = await this.$http.post('/user/bindBankCard', {
          holder_name: this.cardInfo.holderName,
          card_number: this.cardInfo.cardNumber.replace(/\s/g, ''),
          bank_name: this.cardInfo.bankName,
          card_type: this.cardInfo.cardType,
          phone: this.cardInfo.phone,
          code: this.verificationCode
        }, {
          headers: {
            'authorization': localStorage.getItem('user_token') || ''
          }
        })
        
        if (response.data.code === 200) {
          this.$toast('绑定成功')
          setTimeout(() => {
            this.$router.go(-1)
          }, 1500)
        } else {
          this.$toast(response.data.msg || '绑定失败')
        }
      } catch (error) {
        console.error('绑定银行卡失败:', error)
        this.$toast('网络错误，请稍后再试')
      }
    }
  }
}
</script>

<style scoped>
.add-bank-card {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 30px;
}

.header {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  background-color: #fff;
  position: relative;
}

.back-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.form-section, .verification-section {
  background: #fff;
  margin: 12px 12px 0;
  border-radius: 8px;
  padding: 0 16px;
}

.form-item {
  display: flex;
  padding: 16px 0;
  border-bottom: 1px solid #f1f1f1;
  position: relative;
}

.form-item:last-child {
  border-bottom: none;
}

.item-label {
  width: 90px;
  font-size: 16px;
  color: #333;
  display: flex;
  align-items: center;
}

.item-input {
  flex: 1;
  display: flex;
  align-items: center;
}

.item-input.verification {
  display: flex;
  justify-content: space-between;
}

.item-input input {
  width: 100%;
  border: none;
  outline: none;
  font-size: 16px;
  color: #333;
  background: transparent;
}

.item-input input::placeholder {
  color: #999;
}

.item-arrow {
  width: 24px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.send-code-btn {
  padding: 5px 12px;
  background: linear-gradient(to right, #FF6389, #FF8A9F);
  border-radius: 15px;
  color: white;
  font-size: 14px;
  white-space: nowrap;
  margin-left: 10px;
}

.tips-section {
  margin: 15px 12px;
}

.tips-content {
  background: rgba(255, 99, 137, 0.1);
  border-radius: 8px;
  padding: 12px;
}

.tips-content p {
  margin: 5px 0;
  font-size: 14px;
  color: #FF6389;
  line-height: 1.5;
}

.bottom-btn {
  margin: 30px 12px;
  height: 48px;
  background: linear-gradient(to right, #FF6389, #FF8A9F);
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  font-weight: bold;
}

.picker-popup {
  padding: 20px 16px;
}

.popup-title {
  font-size: 18px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 20px;
}

.bank-list {
  display: flex;
  flex-wrap: wrap;
}

.bank-item {
  width: 25%;
  margin-bottom: 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.bank-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  margin-bottom: 5px;
}

.bank-icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.bank-name {
  font-size: 12px;
  color: #333;
  text-align: center;
}

.picker-options {
  padding: 0 10px;
}

.picker-option {
  padding: 15px 0;
  border-bottom: 1px solid #f1f1f1;
  text-align: center;
  font-size: 16px;
}

.picker-option:last-child {
  border-bottom: none;
}
</style>
