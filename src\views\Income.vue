<template>
  <div class="income">
    <!-- 头部导航 -->
    <div class="header">
      <div class="back-icon" @click="goBack">
        <img src="@/assets/icons/back-icon.svg" @error="handleImageError($event, 'icon')" alt="返回">
      </div>
      <div class="title">我的收益</div>
      <div class="right-placeholder"></div>
    </div> <!-- 吸顶区域容器 -->
    <div class="sticky-container" :class="{ 'is-sticky': isSticky }">
      <!-- 收益卡片 -->
      <div class="income-card">
        <div class="income-box">
          <div class="income-item">
            <div class="income-label">收益总计</div>
            <div class="income-amount">{{ totalIncome }}</div>
          </div>
          <div class="income-item">
            <div class="income-label">今日收益</div>
            <div class="income-amount">{{ stats.today }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 占位元素，用于防止内容跳动 -->
    <div class="sticky-placeholder" v-if="isSticky" :style="{ height: stickyHeight + 'px' }"></div>

    <!-- 收益详细记录 -->
    <div class="record-section">
      <div class="record-list">
        <div class="record-item" v-for="(item, index) in records" :key="index">
          <div class="record-left">
            <div class="record-icon">
              <div class="icon-bg">
                <img :src="getTypeIcon(item.type)" @error="handleImageError($event, 'income-icon')" alt="收益类型图标">
              </div>
            </div>
            <div class="record-info">
              <div class="record-title">{{ item.name }}</div>
              <div class="record-time">{{ item.create_time }}</div>
            </div>
          </div>
          <div class="record-amount">+{{ item.amount }}</div>
        </div>

        <!-- 加载状态 -->
        <div class="loading-state" v-if="loading">
          <div class="loading-spinner"></div>
          <div class="loading-text">加载中...</div>
        </div>

        <!-- 没有更多数据 -->
        <div class="no-more" v-if="finished && !loading && records.length > 0">
          <span>没有更多了~</span>
        </div>

        <!-- 空状态 -->
        <div class="empty-state" v-if="records.length === 0 && !loading">
          <div class="empty-icon">💰</div>
          <div class="empty-text">暂无收益记录</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ImageErrorHandler from '@/utils/ImageErrorHandler.js'

export default {
  name: 'Income', data() {
    return {
      totalIncome: '0.00',
      stats: {
        today: '0.00',
        yesterday: '0.00',
        thisMonth: '0.00'
      },
      records: [],
      page: 1,
      loading: false,
      finished: false,
      // 吸顶效果所需变量
      isSticky: false,
      stickyHeight: 0,
      stickyTriggerPosition: 0,
      scrollTimeout: null
    }
  },
  created() {
    // 创建组件时立即加载数据
    this.loadIncomeData()
  },
  mounted() {
    // 添加滚动事件监听和初始化吸顶元素
    this.setupScrollListener()
    this.initStickyElements()
    this.setupResizeListener()
  },
  beforeDestroy() {
    // 清理事件监听
    this.cleanupScrollListener()
    this.cleanupResizeListener()
  },
  methods: {
    // 处理图片加载错误
    handleImageError(event, type) {
      ImageErrorHandler.handleImageError(event, type)
    },

    goBack() {
      this.$router.go(-1)
    },

    goToWithdraw() {
      this.$router.push('/withdraw')
    },    // 获取类型对应的图标
    getTypeIcon(type) {
      // 优先使用统一的收益图标，提供一致的视觉体验
      return require('@/assets/images/income/income-icon.svg')
    },

    // 加载收益数据
    loadIncomeData() {
      if (this.finished || this.loading) return

      this.loading = true

      // 从localStorage获取token
      const userToken = localStorage.getItem('user_token')

      if (!userToken) {
        this.$toast('请先登录')
        this.$nextTick(() => { this.$router.replace('/login') })
        this.loading = false
        return
      }

      // 发起API请求获取收益列表
      this.$http.get(`/member/getIncomeList?page=${this.page}`, {
        headers: {
          'authorization': userToken
        }
      })
        .then(response => {
          if (response.data.code === 200) {
            const data = response.data.data
            // 处理收益列表数据
            if (data.list && data.list.length > 0) {
              // 将新数据添加到列表
              this.records = [...this.records, ...data.list]
              this.page++
            } else {
              // 如果当前页没有数据，则标记为加载完成
              this.finished = true
            }

            // 更新总收益和统计数据 - 根据API返回的数据格式调整
            if (data.total_income) {
              this.totalIncome = data.total_income
            }

            // 更新统计数据
            this.stats = {
              today: data.today_income || '0.00',
              yesterday: data.yesterday_income || '0.00',
              thisMonth: data.monthly_income || '0.00'
            }
          } else {
            this.$toast(response.data.msg || '获取收益列表失败')
            this.finished = true
          }

          this.loading = false
        })
        .catch(error => {
          console.error('获取收益列表失败:', error)
          this.$toast('网络异常，请稍后重试')
          this.loading = false
          this.finished = true
        })
    },    // 初始化吸顶元素
    initStickyElements() {
      this.$nextTick(() => {
        const stickyContainer = document.querySelector('.sticky-container')
        if (stickyContainer) {
          // 获取头部高度
          const header = document.querySelector('.header')
          const headerHeight = header ? header.offsetHeight : 0

          // 计算触发吸顶的位置，添加一点缓冲使切换更平滑
          this.stickyTriggerPosition = stickyContainer.offsetTop - headerHeight - 10

          // 保存吸顶容器的高度，用于设置占位元素高度
          this.stickyHeight = stickyContainer.offsetHeight + 5
        }
      })
    },

    // 设置滚动监听
    setupScrollListener() {
      window.addEventListener('scroll', this.handleScroll, { passive: true })
    },

    // 清理滚动监听
    cleanupScrollListener() {
      window.removeEventListener('scroll', this.handleScroll)
      if (this.scrollTimeout) {
        clearTimeout(this.scrollTimeout)
      }
    },

    // 设置窗口大小调整的监听
    setupResizeListener() {
      window.addEventListener('resize', this.handleResize, { passive: true })
    },

    // 清理窗口大小调整的监听
    cleanupResizeListener() {
      window.removeEventListener('resize', this.handleResize)
    },

    // 处理窗口大小变化
    handleResize() {
      // 重新计算吸顶元素的高度和位置
      this.initStickyElements()
    },

    // 处理滚动事件（分页加载和吸顶效果）
    handleScroll() {
      if (this.scrollTimeout) clearTimeout(this.scrollTimeout)

      this.scrollTimeout = setTimeout(() => {
        const scrollTop = document.documentElement.scrollTop || document.body.scrollTop
        const scrollHeight = document.documentElement.scrollHeight || document.body.scrollHeight
        const clientHeight = document.documentElement.clientHeight || window.innerHeight

        // 处理吸顶效果
        this.isSticky = scrollTop > this.stickyTriggerPosition

        // 当滚动到距离底部100px时加载更多数据
        if (scrollHeight - scrollTop - clientHeight < 100 && !this.loading && !this.finished) {
          this.loadIncomeData()
        }
      }, 100) // 减小节流时间，使吸顶效果更流畅
    }
  }
}
</script>

<style scoped>
.income {
  min-height: 100vh;
  background-color: #F8F9FA;
}

/* 头部导航 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  height: 44px;
  background-color: #fff;
  position: sticky;
  top: 0;
  z-index: 101;
  /* 头部层级要高于吸顶容器 */
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.back-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.back-icon img {
  width: 10px;
  height: 16px;
}

.title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 17px;
  font-weight: 500;
  color: #111827;
  font-family: 'PingFang SC', sans-serif;
}

.right-placeholder {
  width: 24px;
  height: 24px;
}

/* 吸顶容器样式 */
.sticky-container {
  position: relative;
  z-index: 99;
  background-color: #F8F9FA;
  transition: all 0.3s ease;
}

.sticky-container.is-sticky {
  position: fixed;
  top: 44px;
  /* 头部高度 */
  left: 0;
  right: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  z-index: 100;
  padding: 0 16px 10px;
}

/* 吸顶状态下的内部元素样式调整 */
.sticky-container.is-sticky .income-card {
  margin: 10px 0;
  transform: scale(0.98);
  transform-origin: top center;
}

/* 吸顶容器的占位元素 */
.sticky-placeholder {
  width: 100%;
  transition: height 0.3s ease;
}

/* 收益卡片 */
.income-card {
  margin: 13px 16px;
  border-radius: 10px;
  background: linear-gradient(180deg, #0474FC 0%, #2585EB 1%, #1F68D6 43%, #1869F5 79%, #0940A0 100%);
  padding: 15px;
  color: #fff;
  overflow: hidden;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.income-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at top right, rgba(255, 255, 255, 0.1), transparent 70%);
}

.income-box {
  display: flex;
  justify-content: space-between;
  position: relative;
  z-index: 2;
}

.income-item {
  flex: 1;
  text-align: center;
}

.income-label {
  font-size: 14px;
  color: #FFFFFF;
  opacity: 0.9;
  margin-bottom: 12px;
  font-family: 'PingFang SC', sans-serif;
}

.income-amount {
  font-size: 16px;
  font-weight: 600;
  color: #FFFFFF;
  font-family: 'DIN', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* 收益记录 */
.record-section {
  margin: 16px 16px 0;
  position: relative;
  z-index: 10;
}

.record-item {
  background-color: #fff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.record-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.record-icon {
  margin-right: 16px;
}

.icon-bg {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #E8EFFB;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.icon-bg img {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

.record-info {
  flex: 1;
}

.record-title {
  font-size: 16px;
  font-weight: 500;
  color: #1F2937;
  margin-bottom: 4px;
}

.record-time {
  font-size: 14px;
  color: #9CA3AF;
}

.record-amount {
  font-size: 17px;
  font-weight: 600;
  color: #0474FC;
  font-family: 'DIN', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30px 20px;
  gap: 12px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(4, 116, 252, 0.1);
  border-top: 3px solid #0474FC;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  color: #666666;
  font-size: 14px;
  font-weight: 500;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #666666;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
  color: #0474FC;
}

.empty-text {
  color: #666666;
  font-size: 15px;
  font-weight: 500;
}

/* 没有更多数据 */
.no-more {
  text-align: center;
  padding: 20px;
  color: #9CA3AF;
  font-size: 14px;
}
</style>
