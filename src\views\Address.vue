<template>
  <div class="address">
    <!-- 头部 -->
    <div class="header">
      <div class="back-icon" @click="goBack">
        <img src="@/assets/icons/back-icon.svg" alt="返回">
      </div>
      <div class="title">收货地址</div>
    </div>

    <!-- 地址列表 -->
    <div class="address-list">
      <div class="address-item" :class="{ 'is-default': isDefaultAddress(item) }" v-for="(item, index) in addresses"
        :key="item.id || index" @click="selectAddress(item)">
        <div class="address-content">
          <div class="address-info">
            <div class="address-header">
              <div class="name-phone">
                <span class="name">{{ item.name }}</span>
                <span class="phone">{{ item.mobile }}</span>
              </div>
            </div>
            <div class="address-detail">{{ item.area }} {{ item.address }}</div>
          </div>
          <div class="address-actions">
            <div class="action-buttons">
              <div class="default-btn" v-if="!isDefaultAddress(item)" @click.stop="setDefaultAddress(item)">
                设为默认
              </div>
              <div class="default-status" v-else>
                <div class="default-tag">默认地址</div>
              </div>
              <div class="edit-btn" @click.stop="editAddress(item)">
                <van-icon name="edit" />
              </div>
            </div>
          </div>
        </div>
      </div><!-- 空状态 -->
      <div class="empty-state" v-if="addresses.length === 0 && !loading">
        <van-empty description="暂无收货地址" />
      </div>

      <!-- 加载状态 -->
      <div class="loading-state" v-if="loading">
        <van-loading type="spinner" color="#0474FC " />
        <div class="loading-text">加载中...</div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="bottom-btn" @click="addAddress">
      <van-icon name="plus" />
      <span>添加收货地址</span>
    </div>
  </div>
</template>

<script>

export default {
  name: 'Address',
  data() {
    return {
      addresses: [],
      isSelectMode: false,
      loading: false
    }
  }, computed: {
    // 用于调试的计算属性
    defaultAddressCount() {
      return this.addresses.filter(addr => this.isDefaultAddress(addr)).length
    },

    // 获取默认地址
    defaultAddress() {
      return this.addresses.find(addr => this.isDefaultAddress(addr))
    }
  }, created() {
    // 判断是否为选择地址模式
    this.isSelectMode = this.$route.query.select === 'true'

    // 运行默认地址判断逻辑测试（开发环境）
    if (process.env.NODE_ENV === 'development') {
      this.testDefaultAddressLogic()
    }

    this.loadAddresses()
  },
  methods: {
    // 判断是否为默认地址
    isDefaultAddress(address) {
      // 支持多种可能的默认地址标识方式
      return address.status === 1 ||
        address.status === '1' ||
        address.isDefault === true ||
        address.is_default === 1 ||
        address.is_default === '1'
    },

    // 检查登录状态
    checkLoginStatus() {
      const userToken = localStorage.getItem('user_token')
      if (!userToken) {
        this.$toast('请先登录')
        this.$nextTick(() => { this.$router.replace('/login') })
        return false
      }
      return true
    },

    // 加载地址列表
    async loadAddresses() {
      if (!this.checkLoginStatus()) return

      this.loading = true
      const userToken = localStorage.getItem('user_token')

      try {
        const response = await this.$http.get('/member/getAddress', {
          headers: {
            'authorization': userToken
          }
        })

        if (response.data.code === 200) {
          this.addresses = response.data.data.list || []
          // 调试信息：打印地址数据结构
          console.log('加载的地址列表:', this.addresses)
          console.log('默认地址数量:', this.defaultAddressCount)

          // 检查每个地址的status字段
          this.addresses.forEach((addr, index) => {
            console.log(`地址${index + 1}: ID=${addr.id}, status=${addr.status}, name=${addr.name}`)
          })
        } else {
          this.$toast(response.data.msg || '获取地址列表失败')
        }
      } catch (error) {
        console.error('获取地址列表失败:', error)

        if (error.response && (error.response.status === 401 || error.response.status === 403)) {
          this.$toast('登录已过期，请重新登录')
          localStorage.removeItem('user_token')
          this.$nextTick(() => { this.$router.replace('/login') })
        } else {
          this.$toast('网络异常，请稍后重试')
        }
      } finally {
        this.loading = false
      }
    },

    goBack() {
      this.$router.go(-1)
    },
    addAddress() {
      this.$router.push('/address/add')
    }, editAddress(address) {
      this.$router.push(`/address/edit/${address.id}`)
    },

    // 设置默认地址
    async setDefaultAddress(address) {
      if (!this.checkLoginStatus()) return

      this.$toast.loading({
        message: '设置中...',
        forbidClick: true,
      })

      const userToken = localStorage.getItem('user_token')

      try {
        const response = await this.$http.post('/member/doAddressIndex', {
          id: address.id
        }, {
          headers: {
            'authorization': userToken,
            'Content-Type': 'application/json'
          }
        })

        this.$toast.clear()

        if (response.data.code === 200) {
          this.$toast.success('设置成功')
          console.log(`地址 ${address.id} 已设为默认地址`)
          // 重新加载地址列表以更新状态
          this.loadAddresses()
        } else {
          this.$toast(response.data.msg || '设置默认地址失败')
        }
      } catch (error) {
        this.$toast.clear()
        console.error('设置默认地址失败:', error)

        if (error.response && (error.response.status === 401 || error.response.status === 403)) {
          this.$toast('登录已过期，请重新登录')
          localStorage.removeItem('user_token')
          this.$nextTick(() => { this.$router.replace('/login') })
        } else {
          this.$toast('网络异常，请稍后重试')
        }
      }
    },

    // 测试方法：验证默认地址判断逻辑
    testDefaultAddressLogic() {
      const testCases = [
        { status: 1, expected: true, desc: 'status为数字1' },
        { status: '1', expected: true, desc: 'status为字符串1' },
        { status: 0, expected: false, desc: 'status为数字0' },
        { status: '0', expected: false, desc: 'status为字符串0' },
        { isDefault: true, expected: true, desc: 'isDefault为true' },
        { isDefault: false, expected: false, desc: 'isDefault为false' },
        { is_default: 1, expected: true, desc: 'is_default为数字1' },
        { is_default: '1', expected: true, desc: 'is_default为字符串1' },
        { is_default: 0, expected: false, desc: 'is_default为数字0' },
      ]

      console.log('=== 默认地址判断逻辑测试 ===')
      testCases.forEach((testCase, index) => {
        const result = this.isDefaultAddress(testCase)
        const passed = result === testCase.expected
        console.log(`测试${index + 1}: ${testCase.desc} - ${passed ? '✓' : '✗'} (预期: ${testCase.expected}, 实际: ${result})`)
      })
    },

    selectAddress(address) {
      if (this.isSelectMode) {
        // 选择地址模式，返回选中的地址
        this.$router.replace({
          path: this.$route.query.redirect || '/',
          query: { addressId: address.id }
        })
      }
    }
  }
}
</script>

<style scoped>
.address {
  min-height: 100vh;
  background-color: #f6f6f6;
  padding-bottom: 80px;
}

.header {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  background-color: #fff;
  position: relative;
}

.back-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon img {
  width: 20px;
  height: 20px;
}

.title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

.address-list {
  padding: 10px 16px;
}

.address-item {
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 10px;
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
}

.address-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 默认地址特殊样式 */
.address-item.is-default {
  border: 1px solid rgba(255, 97, 139, 0.3);
  background: linear-gradient(135deg, #fff 0%, rgba(255, 97, 139, 0.02) 100%);
}

.address-item.is-default::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 3px;
  height: 100%;
  background-color: #0474FC;
}

.address-content {
  padding: 15px;
  display: flex;
}

.address-info {
  flex: 1;
}

.address-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.name-phone {
  font-size: 16px;
  color: #333;
}

.name {
  font-weight: 500;
  margin-right: 10px;
}

.phone {
  color: #666;
}

.address-detail {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.address-actions {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-end;
  margin-left: 10px;
  min-width: 80px;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
}

.default-btn {
  padding: 4px 12px;
  background-color: #f5f5f5;
  color: #666;
  font-size: 12px;
  border-radius: 12px;
  cursor: pointer;
  border: 1px solid #e0e0e0;
  transition: all 0.3s ease;
}

.default-btn:hover {
  background-color: #e0e0e0;
  color: #333;
}

.default-btn:active {
  background-color: #d0d0d0;
}

.default-status {
  display: flex;
  justify-content: flex-end;
}

.default-tag {
  padding: 4px 12px;
  background-color: #0474FC;
  color: #fff;
  font-size: 12px;
  border-radius: 12px;
}

.edit-btn {
  color: #999;
  padding: 5px;
  cursor: pointer;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.edit-btn:hover {
  background-color: #f5f5f5;
  color: #666;
}

.empty-state {
  padding: 50px 0;
}

.loading-state {
  padding: 50px 0;
  text-align: center;
}

.loading-text {
  margin-top: 10px;
  font-size: 14px;
  color: #666;
}

.bottom-btn {
  position: fixed;
  bottom: 20px;
  left: 16px;
  right: 16px;
  height: 44px;
  background-color: #0474FC;
  color: #fff;
  font-size: 16px;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bottom-btn .van-icon {
  margin-right: 5px;
}
</style>
